<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" stroke-width="1"/>
    </pattern>
    <pattern id="circles" width="50" height="50" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="2" fill="white" />
      <circle cx="40" cy="40" r="2" fill="white" />
      <circle cx="25" cy="25" r="1" fill="white" />
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)" />
  <rect width="100%" height="100%" fill="url(#circles)" />
</svg>
