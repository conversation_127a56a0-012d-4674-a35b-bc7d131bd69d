import React from "react"
import Image from "next/image"
import Link from "next/link"

const Layout = ({ children }: { children: React.ReactNode }) => (
  <main className="grid flex-1 lg:grid-cols-2">
    <div className="bg-primary text-primary-foreground relative z-0 hidden flex-col items-center justify-center gap-4 p-6 text-center lg:flex">
      <div className="flex flex-1 flex-col items-center justify-center gap-2">
        <Link href="/" className="flex items-center justify-center">
          <Image
            className="h-12 w-auto object-contain"
            src="/logo-white.png"
            alt="Singapore Medical Group"
            width={512}
            height={175}
            priority
          />
        </Link>

        <h1 className="mt-4 text-2xl font-bold">
          Streamline Your Workflow Management
        </h1>

        <p className="max-w-sm">
          OneFlow brings together all your management needs in one integrated
          platform
        </p>
      </div>

      <p className="text-sm opacity-90">
        © {new Date().getFullYear()} - Singapore Medical Group
      </p>

      <div className="pointer-events-none absolute inset-0 isolate -z-10 bg-[url('/images/pattern.svg')] opacity-10" />
    </div>

    <div className="flex flex-col gap-4 p-6">
      <div className="flex flex-1 items-center justify-center">
        <div className="w-full max-w-sm">{children}</div>
      </div>
    </div>
  </main>
)

export default Layout
