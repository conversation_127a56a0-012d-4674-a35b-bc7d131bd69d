import type { Metada<PERSON> } from "next"
import { Source_Sans_3 as FontSans } from "next/font/google"

import { cn } from "@/lib/utils"
import Providers from "@/app/providers"

import "@/app/globals.css"

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
})

export const metadata: Metadata = {
  title: {
    template: "%s | OneFlow | Singapore Medical Group",
    default: "OneFlow | Singapore Medical Group",
  },
  description:
    "Streamline your healthcare workflow with Singapore Medical Group's integrated platform.",
}

const RootLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode
}>) => (
  <html lang="en" suppressHydrationWarning>
    <body
      className={cn(
        "relative flex min-h-svh flex-col overflow-x-hidden overscroll-y-none font-sans text-pretty antialiased",
        fontSans.variable
      )}
    >
      <Providers>{children}</Providers>
    </body>
  </html>
)

export default RootLayout
