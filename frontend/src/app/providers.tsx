import React from "react"
import TopLoader from "nextjs-toploader"

import { Toaster } from "@/components/ui/sonner"
import Preloader from "@/components/layouts/Preloader"
import ThemeProvider from "@/components/providers/ThemeProvider"

const Providers = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider
    attribute="class"
    defaultTheme="system"
    enableSystem
    disableTransitionOnChange
  >
    <Preloader />

    <TopLoader
      height={2}
      color="var(--primary)"
      shadow={false}
      showSpinner={false}
      showForHashAnchor={false}
    />

    {children}

    <Toaster />
  </ThemeProvider>
)

export default Providers
