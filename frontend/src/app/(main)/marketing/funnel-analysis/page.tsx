"use client"

import React from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ChevronDown,
  Download,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>hare2,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for funnel stages
const funnelStages = [
  {
    name: "Awareness",
    count: 28450,
    percentage: 100,
    conversionRate: null,
    dropoff: 0,
  },
  {
    name: "Interest",
    count: 12680,
    percentage: 44.6,
    conversionRate: 44.6,
    dropoff: 15770,
  },
  {
    name: "Consideration",
    count: 5840,
    percentage: 20.5,
    conversionRate: 46.1,
    dropoff: 6840,
  },
  {
    name: "Intent",
    count: 2845,
    percentage: 10.0,
    conversionRate: 48.7,
    dropoff: 2995,
  },
  {
    name: "Evaluation",
    count: 1420,
    percentage: 5.0,
    conversionRate: 49.9,
    dropoff: 1425,
  },
  {
    name: "Conversion",
    count: 995,
    percentage: 3.5,
    conversionRate: 70.1,
    dropoff: 425,
  },
]

// Mock data for funnel by channel
const funnelByChannel = [
  {
    channel: "Organic Search",
    awareness: 8535,
    interest: 3840,
    consideration: 1760,
    intent: 855,
    evaluation: 428,
    conversion: 299,
    conversionRate: 3.5,
  },
  {
    channel: "Paid Search",
    awareness: 6240,
    interest: 2810,
    consideration: 1310,
    intent: 655,
    evaluation: 325,
    conversion: 228,
    conversionRate: 3.7,
  },
  {
    channel: "Email",
    awareness: 5120,
    interest: 2560,
    consideration: 1280,
    intent: 640,
    evaluation: 320,
    conversion: 224,
    conversionRate: 4.4,
  },
  {
    channel: "Social Media",
    awareness: 4100,
    interest: 1640,
    consideration: 740,
    intent: 370,
    evaluation: 185,
    conversion: 130,
    conversionRate: 3.2,
  },
  {
    channel: "Referral",
    awareness: 2850,
    interest: 1140,
    consideration: 510,
    intent: 225,
    evaluation: 112,
    conversion: 78,
    conversionRate: 2.7,
  },
  {
    channel: "Direct",
    awareness: 1605,
    interest: 690,
    consideration: 240,
    intent: 100,
    evaluation: 50,
    conversion: 36,
    conversionRate: 2.2,
  },
]

// Mock data for funnel by campaign
const funnelByCampaign = [
  {
    campaign: "Spring Health Check Promotion",
    awareness: 12500,
    interest: 4625,
    consideration: 1875,
    intent: 625,
    evaluation: 315,
    conversion: 215,
    conversionRate: 1.7,
  },
  {
    campaign: "Cardiology Services Awareness",
    awareness: 25000,
    interest: 8750,
    consideration: 3125,
    intent: 625,
    evaluation: 250,
    conversion: 185,
    conversionRate: 0.7,
  },
  {
    campaign: "Pediatric Vaccination Drive",
    awareness: 18500,
    interest: 7215,
    consideration: 2775,
    intent: 925,
    evaluation: 460,
    conversion: 320,
    conversionRate: 1.7,
  },
  {
    campaign: "Mental Health Awareness Month",
    awareness: 35000,
    interest: 12250,
    consideration: 4550,
    intent: 1050,
    evaluation: 395,
    conversion: 275,
    conversionRate: 0.8,
  },
]

// Mock data for funnel insights
const funnelInsights = [
  {
    stage: "Interest",
    insight: "High dropoff rate from Awareness to Interest stage (55.4%)",
    recommendation:
      "Improve content relevance and targeting to increase initial engagement",
    impact: "High",
  },
  {
    stage: "Consideration",
    insight:
      "Email channel shows strongest retention from Interest to Consideration (50%)",
    recommendation:
      "Analyze email content strategy and apply learnings to other channels",
    impact: "Medium",
  },
  {
    stage: "Intent",
    insight:
      "Pediatric Vaccination campaign has highest Intent conversion (33.3%)",
    recommendation:
      "Review campaign messaging and apply successful elements to other campaigns",
    impact: "Medium",
  },
  {
    stage: "Conversion",
    insight: "Organic Search has the highest final conversion rate (3.5%)",
    recommendation:
      "Increase SEO investment and optimize landing pages for conversion",
    impact: "High",
  },
]

const FunnelAnalysis = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Marketing", href: "/marketing" },
        { label: "Funnel Analysis" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Funnel Analysis</CardTitle>
      <CardDescription>
        Analyze and optimize your marketing funnel stages
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 30 Days
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" size="sm">
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>
    </div>

    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Marketing Funnel Overview</CardTitle>
            <CardDescription>
              Conversion rates across funnel stages
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <LineChart className="mr-2 h-4 w-4" />
              View Trends
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {funnelStages.map((stage, index) => (
            <div key={index} className="relative">
              <div className="mb-1 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{stage.name}</span>
                  <span className="text-muted-foreground text-xs">
                    {stage.count.toLocaleString()} users
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-xs">
                    {stage.percentage.toFixed(1)}% of total
                  </span>
                  {stage.conversionRate && (
                    <span className="text-xs">
                      {stage.conversionRate.toFixed(1)}% conversion from
                      previous
                    </span>
                  )}
                </div>
              </div>
              <Progress value={stage.percentage} className="h-8" />
              {index < funnelStages.length - 1 && (
                <div className="absolute -bottom-4 left-1/2 flex -translate-x-1/2 items-center justify-center">
                  <div className="flex flex-col items-center">
                    <ArrowDown className="text-muted-foreground h-4 w-4" />
                    <span className="text-muted-foreground text-xs">
                      {stage.dropoff.toLocaleString()} drop off
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>

    <Tabs defaultValue="by-channel" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="by-channel">By Channel</TabsTrigger>
        <TabsTrigger value="by-campaign">By Campaign</TabsTrigger>
        <TabsTrigger value="insights">Funnel Insights</TabsTrigger>
      </TabsList>
      <TabsContent value="by-channel" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Funnel Performance by Channel</CardTitle>
            <CardDescription>
              Conversion metrics across different marketing channels
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Channel</th>
                    <th className="p-2 text-right font-medium">Awareness</th>
                    <th className="p-2 text-right font-medium">Interest</th>
                    <th className="p-2 text-right font-medium">
                      Consideration
                    </th>
                    <th className="p-2 text-right font-medium">Intent</th>
                    <th className="p-2 text-right font-medium">Evaluation</th>
                    <th className="p-2 text-right font-medium">Conversion</th>
                    <th className="p-2 text-center font-medium">Conv. Rate</th>
                  </tr>
                </thead>
                <tbody>
                  {funnelByChannel.map((channel, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{channel.channel}</td>
                      <td className="p-2 text-right">
                        {channel.awareness.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {channel.interest.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {channel.consideration.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {channel.intent.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {channel.evaluation.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {channel.conversion.toLocaleString()}
                      </td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            channel.conversionRate > 4
                              ? "bg-green-100 text-green-700"
                              : channel.conversionRate > 3
                                ? "bg-blue-100 text-blue-700"
                                : "bg-amber-100 text-amber-700"
                          }`}
                        >
                          {channel.conversionRate.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter>
            <div className="w-full space-y-2">
              <h3 className="text-sm font-medium">
                Channel Performance Summary
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Best Performing
                    </span>
                    <span className="font-medium">Email</span>
                  </div>
                  <Progress value={4.4} max={5} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>4.4% conversion rate</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Highest Volume
                    </span>
                    <span className="font-medium">Organic Search</span>
                  </div>
                  <Progress value={8535} max={8535} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>8,535 visitors</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Needs Improvement
                    </span>
                    <span className="font-medium">Direct</span>
                  </div>
                  <Progress value={2.2} max={5} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>2.2% conversion rate</span>
                  </div>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="by-campaign" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Funnel Performance by Campaign</CardTitle>
            <CardDescription>
              Conversion metrics across different marketing campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Campaign</th>
                    <th className="p-2 text-right font-medium">Awareness</th>
                    <th className="p-2 text-right font-medium">Interest</th>
                    <th className="p-2 text-right font-medium">
                      Consideration
                    </th>
                    <th className="p-2 text-right font-medium">Intent</th>
                    <th className="p-2 text-right font-medium">Evaluation</th>
                    <th className="p-2 text-right font-medium">Conversion</th>
                    <th className="p-2 text-center font-medium">Conv. Rate</th>
                  </tr>
                </thead>
                <tbody>
                  {funnelByCampaign.map((campaign, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{campaign.campaign}</td>
                      <td className="p-2 text-right">
                        {campaign.awareness.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {campaign.interest.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {campaign.consideration.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {campaign.intent.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {campaign.evaluation.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {campaign.conversion.toLocaleString()}
                      </td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            campaign.conversionRate > 1.5
                              ? "bg-green-100 text-green-700"
                              : campaign.conversionRate > 1
                                ? "bg-blue-100 text-blue-700"
                                : "bg-amber-100 text-amber-700"
                          }`}
                        >
                          {campaign.conversionRate.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter>
            <div className="w-full space-y-2">
              <h3 className="text-sm font-medium">
                Campaign Performance Summary
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Best Performing
                    </span>
                    <span className="font-medium">Pediatric Vaccination</span>
                  </div>
                  <Progress value={1.7} max={2} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>1.7% conversion rate</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Highest Volume
                    </span>
                    <span className="font-medium">Mental Health Awareness</span>
                  </div>
                  <Progress value={35000} max={35000} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>35,000 visitors</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Needs Improvement
                    </span>
                    <span className="font-medium">Cardiology Services</span>
                  </div>
                  <Progress value={0.7} max={2} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>0.7% conversion rate</span>
                  </div>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="insights" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Funnel Insights & Recommendations</CardTitle>
            <CardDescription>
              Key insights and optimization opportunities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {funnelInsights.map((insight, index) => (
                <div
                  key={index}
                  className="border-b pb-4 last:border-0 last:pb-0"
                >
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="bg-primary/10 text-primary inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium">
                        {insight.stage} Stage
                      </span>
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          insight.impact === "High"
                            ? "bg-red-100 text-red-700"
                            : insight.impact === "Medium"
                              ? "bg-amber-100 text-amber-700"
                              : "bg-blue-100 text-blue-700"
                        }`}
                      >
                        {insight.impact} Impact
                      </span>
                    </div>
                    <Button variant="ghost" size="sm">
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </div>
                  <div>
                    <h3 className="font-medium">Insight</h3>
                    <p className="text-muted-foreground mb-2 text-sm">
                      {insight.insight}
                    </p>
                    <h3 className="font-medium">Recommendation</h3>
                    <p className="text-muted-foreground text-sm">
                      {insight.recommendation}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">
              <Users className="mr-2 h-4 w-4" />
              Generate Audience Segments
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default FunnelAnalysis
