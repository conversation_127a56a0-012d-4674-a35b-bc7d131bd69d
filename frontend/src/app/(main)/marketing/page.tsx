"use client"

import React from "react"
import {
  ArrowRight,
  Calendar,
  ChevronUp,
  Filter,
  LineChart,
  Mail,
  MessageSquare,
  RefreshCw,
  Share2,
  Users,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for marketing metrics
const marketingMetrics = [
  {
    title: "Total Leads",
    value: "2,845",
    change: "+12.5%",
    trend: "up",
    period: "vs. last month",
  },
  {
    title: "Conversion Rate",
    value: "3.6%",
    change: "+0.8%",
    trend: "up",
    period: "vs. last month",
  },
  {
    title: "Avg. Acquisition Cost",
    value: "$142",
    change: "-8.3%",
    trend: "down",
    period: "vs. last month",
  },
  {
    title: "Active Campaigns",
    value: "12",
    change: "+2",
    trend: "up",
    period: "vs. last month",
  },
]

// Mock data for lead sources
const leadSources = [
  {
    source: "Organic Search",
    leads: 845,
    percentage: 29.7,
    conversion: 4.2,
    trend: "+5.3%",
  },
  {
    source: "Paid Search",
    leads: 620,
    percentage: 21.8,
    conversion: 3.8,
    trend: "+2.1%",
  },
  {
    source: "Email Campaigns",
    leads: 512,
    percentage: 18.0,
    conversion: 5.6,
    trend: "+12.4%",
  },
  {
    source: "Social Media",
    leads: 410,
    percentage: 14.4,
    conversion: 2.9,
    trend: "+8.7%",
  },
  {
    source: "Referrals",
    leads: 285,
    percentage: 10.0,
    conversion: 6.8,
    trend: "+3.2%",
  },
  {
    source: "Direct",
    leads: 173,
    percentage: 6.1,
    conversion: 3.1,
    trend: "-1.5%",
  },
]

// Mock data for recent campaigns
const recentCampaigns = [
  {
    id: 1,
    name: "Spring Health Check Promotion",
    status: "Active",
    type: "Email",
    audience: "Previous Patients",
    sent: 12500,
    opened: 4625,
    clicked: 1875,
    converted: 215,
    startDate: "Apr 15, 2024",
    endDate: "May 31, 2024",
  },
  {
    id: 2,
    name: "Cardiology Services Awareness",
    status: "Active",
    type: "Social Media",
    audience: "Age 45+",
    sent: 25000,
    opened: 8750,
    clicked: 3125,
    converted: 185,
    startDate: "May 1, 2024",
    endDate: "Jun 15, 2024",
  },
  {
    id: 3,
    name: "Pediatric Vaccination Drive",
    status: "Active",
    type: "Multi-channel",
    audience: "Parents",
    sent: 18500,
    opened: 7215,
    clicked: 2775,
    converted: 320,
    startDate: "May 10, 2024",
    endDate: "Jun 30, 2024",
  },
  {
    id: 4,
    name: "Mental Health Awareness Month",
    status: "Active",
    type: "Content Marketing",
    audience: "All Demographics",
    sent: 35000,
    opened: 12250,
    clicked: 4550,
    converted: 275,
    startDate: "May 1, 2024",
    endDate: "May 31, 2024",
  },
]

const Marketing = () => (
  <>
    <Breadcrumb links={[{ label: "Marketing" }]} />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Marketing</CardTitle>
      <CardDescription>
        Comprehensive Marketing Strategy and Execution
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 30 Days
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {marketingMetrics.map((metric, index) => (
        <Card key={index}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="mt-1 flex items-center">
              <span
                className={`text-xs ${
                  metric.trend === "up" ? "text-green-500" : "text-red-500"
                }`}
              >
                {metric.change}
              </span>
              <ChevronUp
                className={`h-3 w-3 ${
                  metric.trend === "up"
                    ? "text-green-500"
                    : "rotate-180 text-red-500"
                }`}
              />
              <span className="text-muted-foreground ml-1 text-xs">
                {metric.period}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>

    <div className="mt-6 grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Lead Sources</CardTitle>
              <CardDescription>
                Distribution of leads by acquisition channel
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <LineChart className="mr-2 h-4 w-4" />
              View Trends
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {leadSources.map((source, index) => (
              <div key={index}>
                <div className="mb-1 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{source.source}</span>
                    <span className="text-muted-foreground text-xs">
                      {source.leads} leads
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`text-xs ${
                        source.trend.startsWith("+")
                          ? "text-green-500"
                          : "text-red-500"
                      }`}
                    >
                      {source.trend}
                    </span>
                    <span className="text-muted-foreground text-xs">
                      {source.conversion}% conv.
                    </span>
                  </div>
                </div>
                <Progress value={source.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="w-full">
            View Detailed Analytics
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Marketing Channels</CardTitle>
              <CardDescription>
                Performance by marketing channel
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Mail className="text-primary h-4 w-4" />
                <span className="font-medium">Email Marketing</span>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Open Rate</span>
                  <span>37.2%</span>
                </div>
                <Progress value={37.2} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Click Rate</span>
                  <span>15.2%</span>
                </div>
                <Progress value={15.2} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Conversion</span>
                  <span>4.6%</span>
                </div>
                <Progress value={4.6} className="h-1" />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Users className="text-primary h-4 w-4" />
                <span className="font-medium">Social Media</span>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Engagement</span>
                  <span>12.8%</span>
                </div>
                <Progress value={12.8} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Click-through</span>
                  <span>3.5%</span>
                </div>
                <Progress value={3.5} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Conversion</span>
                  <span>2.9%</span>
                </div>
                <Progress value={2.9} className="h-1" />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <MessageSquare className="text-primary h-4 w-4" />
                <span className="font-medium">Content Marketing</span>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Avg. Time</span>
                  <span>3:42</span>
                </div>
                <Progress value={74} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Bounce Rate</span>
                  <span>28.5%</span>
                </div>
                <Progress value={28.5} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Conversion</span>
                  <span>3.2%</span>
                </div>
                <Progress value={3.2} className="h-1" />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <LineChart className="text-primary h-4 w-4" />
                <span className="font-medium">Paid Advertising</span>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">CTR</span>
                  <span>4.2%</span>
                </div>
                <Progress value={4.2} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">CPC</span>
                  <span>$1.85</span>
                </div>
                <Progress value={65} className="h-1" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Conversion</span>
                  <span>3.8%</span>
                </div>
                <Progress value={3.8} className="h-1" />
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="w-full">
            View Channel Analytics
          </Button>
        </CardFooter>
      </Card>
    </div>

    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Recent Campaigns</CardTitle>
            <CardDescription>
              Overview of your recent marketing campaigns
            </CardDescription>
          </div>
          <Button>
            <ArrowRight className="mr-2 h-4 w-4" />
            Campaign Manager
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="p-2 text-left font-medium">Campaign</th>
                <th className="p-2 text-center font-medium">Status</th>
                <th className="p-2 text-left font-medium">Type</th>
                <th className="p-2 text-left font-medium">Audience</th>
                <th className="p-2 text-right font-medium">Sent</th>
                <th className="p-2 text-right font-medium">Opened</th>
                <th className="p-2 text-right font-medium">Clicked</th>
                <th className="p-2 text-right font-medium">Converted</th>
                <th className="p-2 text-center font-medium">Dates</th>
              </tr>
            </thead>
            <tbody>
              {recentCampaigns.map((campaign) => (
                <tr key={campaign.id} className="border-b">
                  <td className="p-2 font-medium">{campaign.name}</td>
                  <td className="p-2 text-center">
                    <span
                      className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                        campaign.status === "Active"
                          ? "bg-green-100 text-green-700"
                          : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {campaign.status}
                    </span>
                  </td>
                  <td className="p-2">{campaign.type}</td>
                  <td className="p-2">{campaign.audience}</td>
                  <td className="p-2 text-right">
                    {campaign.sent.toLocaleString()}
                  </td>
                  <td className="p-2 text-right">
                    {campaign.opened.toLocaleString()} (
                    {((campaign.opened / campaign.sent) * 100).toFixed(1)}%)
                  </td>
                  <td className="p-2 text-right">
                    {campaign.clicked.toLocaleString()} (
                    {((campaign.clicked / campaign.sent) * 100).toFixed(1)}%)
                  </td>
                  <td className="p-2 text-right">
                    {campaign.converted.toLocaleString()} (
                    {((campaign.converted / campaign.sent) * 100).toFixed(1)}%)
                  </td>
                  <td className="p-2 text-center">
                    <span className="text-xs">
                      {campaign.startDate} - {campaign.endDate}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-muted-foreground text-sm">
          Showing 4 of 12 active campaigns
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            View All Campaigns
          </Button>
          <Button size="sm">Create Campaign</Button>
        </div>
      </CardFooter>
    </Card>
  </>
)

export default Marketing
