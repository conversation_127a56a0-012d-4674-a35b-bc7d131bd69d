"use client"

import React from "react"
import {
  Calendar,
  Download,
  Filter,
  Mail,
  MessageSquare,
  Phone,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for campaigns
const campaignData = [
  {
    id: "spring-checkup",
    name: "Spring Health Checkup",
    status: "Active",
    startDate: "2024-03-01",
    endDate: "2024-05-31",
    budget: 25000,
    spent: 18500,
    leads: 420,
    conversions: 105,
    roi: 2.8,
    channels: ["Email", "Social Media", "Search"],
  },
  {
    id: "diabetes-awareness",
    name: "Diabetes Awareness Program",
    status: "Active",
    startDate: "2024-02-15",
    endDate: "2024-06-30",
    budget: 35000,
    spent: 22000,
    leads: 380,
    conversions: 95,
    roi: 2.1,
    channels: ["Social Media", "Display Ads", "Community Events"],
  },
  {
    id: "cardiology-services",
    name: "Cardiology Services Promotion",
    status: "Completed",
    startDate: "2024-01-10",
    endDate: "2024-03-15",
    budget: 18000,
    spent: 18000,
    leads: 310,
    conversions: 85,
    roi: 2.4,
    channels: ["Email", "Direct Mail", "Radio"],
  },
  {
    id: "womens-health",
    name: "Women's Health Initiative",
    status: "Planned",
    startDate: "2024-06-01",
    endDate: "2024-08-31",
    budget: 30000,
    spent: 0,
    leads: 0,
    conversions: 0,
    roi: 0,
    channels: ["Social Media", "Email", "Community Events"],
  },
  {
    id: "telehealth-services",
    name: "Telehealth Services Awareness",
    status: "Active",
    startDate: "2024-04-01",
    endDate: "2024-07-31",
    budget: 22000,
    spent: 12000,
    leads: 280,
    conversions: 70,
    roi: 2.9,
    channels: ["Social Media", "Search", "Display Ads"],
  },
]

// Mock data for channel performance
const channelPerformance = [
  { name: "Email", leads: 380, conversions: 95, cpl: 42, conversionRate: 25 },
  {
    name: "Social Media",
    leads: 520,
    conversions: 130,
    cpl: 38,
    conversionRate: 25,
  },
  { name: "Search", leads: 290, conversions: 87, cpl: 45, conversionRate: 30 },
  {
    name: "Display Ads",
    leads: 210,
    conversions: 42,
    cpl: 60,
    conversionRate: 20,
  },
  {
    name: "Direct Mail",
    leads: 150,
    conversions: 45,
    cpl: 75,
    conversionRate: 30,
  },
  {
    name: "Community Events",
    leads: 180,
    conversions: 63,
    cpl: 85,
    conversionRate: 35,
  },
  { name: "Radio", leads: 120, conversions: 24, cpl: 95, conversionRate: 20 },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

const CampaignPerformance = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Marketing", href: "/marketing" },
        { label: "Campaign Performance" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Campaign Performance</CardTitle>
      <CardDescription>
        Analyze and optimize your marketing campaign performance
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 90 Days
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">5</div>
          <p className="text-muted-foreground text-xs">
            3 active, 1 completed, 1 planned
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(130000)}</div>
          <p className="text-muted-foreground text-xs">54% spent to date</p>
          <Progress value={54} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1,390</div>
          <p className="text-muted-foreground text-xs">$50.36 cost per lead</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">25.5%</div>
          <p className="text-muted-foreground text-xs">
            +2.3% from previous period
          </p>
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="campaigns" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="campaigns">Campaign Overview</TabsTrigger>
        <TabsTrigger value="channels">Channel Performance</TabsTrigger>
        <TabsTrigger value="audience">Audience Insights</TabsTrigger>
      </TabsList>
      <TabsContent value="campaigns" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Campaign Performance</CardTitle>
            <CardDescription>
              Overview of all marketing campaigns and their key metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Campaign</th>
                    <th className="p-2 text-center font-medium">Status</th>
                    <th className="p-2 text-center font-medium">Timeline</th>
                    <th className="p-2 text-right font-medium">Budget</th>
                    <th className="p-2 text-right font-medium">Leads</th>
                    <th className="p-2 text-right font-medium">Conversions</th>
                    <th className="p-2 text-right font-medium">ROI</th>
                  </tr>
                </thead>
                <tbody>
                  {campaignData.map((campaign) => (
                    <tr key={campaign.id} className="border-b">
                      <td className="p-2 font-medium">{campaign.name}</td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            campaign.status === "Active"
                              ? "bg-green-100 text-green-700"
                              : campaign.status === "Completed"
                                ? "bg-blue-100 text-blue-700"
                                : "bg-amber-100 text-amber-700"
                          }`}
                        >
                          {campaign.status}
                        </span>
                      </td>
                      <td className="p-2 text-center text-sm">
                        {campaign.startDate} to {campaign.endDate}
                      </td>
                      <td className="p-2 text-right">
                        <div>
                          <div>{formatCurrency(campaign.budget)}</div>
                          {campaign.status !== "Planned" && (
                            <div className="text-muted-foreground text-xs">
                              {Math.round(
                                (campaign.spent / campaign.budget) * 100
                              )}
                              % spent
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-2 text-right">{campaign.leads}</td>
                      <td className="p-2 text-right">{campaign.conversions}</td>
                      <td className="p-2 text-right">
                        {campaign.roi > 0 ? `${campaign.roi}x` : "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="channels" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Channel Performance</CardTitle>
            <CardDescription>
              Comparison of marketing channels and their effectiveness
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Channel</th>
                    <th className="p-2 text-right font-medium">Leads</th>
                    <th className="p-2 text-right font-medium">Conversions</th>
                    <th className="p-2 text-right font-medium">
                      Cost per Lead
                    </th>
                    <th className="p-2 text-right font-medium">
                      Conversion Rate
                    </th>
                    <th className="p-2 text-left font-medium">Effectiveness</th>
                  </tr>
                </thead>
                <tbody>
                  {channelPerformance.map((channel, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">
                        <div className="flex items-center gap-2">
                          {channel.name === "Email" && (
                            <Mail className="h-4 w-4 text-blue-500" />
                          )}
                          {channel.name === "Social Media" && (
                            <Users className="h-4 w-4 text-purple-500" />
                          )}
                          {channel.name === "Search" && (
                            <Filter className="h-4 w-4 text-amber-500" />
                          )}
                          {channel.name === "Display Ads" && (
                            <MessageSquare className="h-4 w-4 text-green-500" />
                          )}
                          {channel.name === "Direct Mail" && (
                            <Mail className="h-4 w-4 text-red-500" />
                          )}
                          {channel.name === "Community Events" && (
                            <Users className="h-4 w-4 text-indigo-500" />
                          )}
                          {channel.name === "Radio" && (
                            <Phone className="h-4 w-4 text-orange-500" />
                          )}
                          {channel.name}
                        </div>
                      </td>
                      <td className="p-2 text-right">{channel.leads}</td>
                      <td className="p-2 text-right">{channel.conversions}</td>
                      <td className="p-2 text-right">
                        {formatCurrency(channel.cpl)}
                      </td>
                      <td className="p-2 text-right">
                        {channel.conversionRate}%
                      </td>
                      <td className="p-2">
                        <div className="w-full">
                          <Progress
                            value={channel.conversionRate}
                            className="h-2"
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="audience" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Audience Insights</CardTitle>
            <CardDescription>
              Demographic and behavioral analysis of campaign audiences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-4 text-lg font-medium">
                  Demographic Breakdown
                </h3>
                <div className="bg-muted mb-4 flex h-[250px] items-center justify-center rounded-md">
                  <p className="text-muted-foreground">
                    Age and gender distribution chart
                  </p>
                </div>
                <div className="space-y-2">
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">Female (65%)</span>
                      <span className="text-sm font-medium">65%</span>
                    </div>
                    <Progress value={65} className="h-2" />
                  </div>
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">Male (35%)</span>
                      <span className="text-sm font-medium">35%</span>
                    </div>
                    <Progress value={35} className="h-2" />
                  </div>
                </div>
                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="rounded-md border p-3">
                    <h4 className="mb-1 text-sm font-medium">Age Groups</h4>
                    <ul className="space-y-1 text-sm">
                      <li className="flex items-center justify-between">
                        <span>18-34</span>
                        <span>22%</span>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>35-49</span>
                        <span>35%</span>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>50-64</span>
                        <span>28%</span>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>65+</span>
                        <span>15%</span>
                      </li>
                    </ul>
                  </div>
                  <div className="rounded-md border p-3">
                    <h4 className="mb-1 text-sm font-medium">Location</h4>
                    <ul className="space-y-1 text-sm">
                      <li className="flex items-center justify-between">
                        <span>Urban</span>
                        <span>45%</span>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>Suburban</span>
                        <span>40%</span>
                      </li>
                      <li className="flex items-center justify-between">
                        <span>Rural</span>
                        <span>15%</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="mb-4 text-lg font-medium">
                  Behavioral Insights
                </h3>
                <div className="space-y-4">
                  <div className="rounded-md border p-3">
                    <h4 className="mb-2 text-sm font-medium">
                      Engagement by Time of Day
                    </h4>
                    <div className="bg-muted mb-2 flex h-[150px] items-center justify-center rounded-md">
                      <p className="text-muted-foreground">
                        Time of day engagement chart
                      </p>
                    </div>
                    <p className="text-muted-foreground text-xs">
                      Peak engagement occurs between 7-9 AM and 6-8 PM on
                      weekdays.
                    </p>
                  </div>
                  <div className="rounded-md border p-3">
                    <h4 className="mb-2 text-sm font-medium">Top Interests</h4>
                    <ul className="space-y-2 text-sm">
                      <li>
                        <div className="mb-1 flex items-center justify-between">
                          <span>Preventive Health</span>
                          <span>78%</span>
                        </div>
                        <Progress value={78} className="h-1.5" />
                      </li>
                      <li>
                        <div className="mb-1 flex items-center justify-between">
                          <span>Fitness & Wellness</span>
                          <span>65%</span>
                        </div>
                        <Progress value={65} className="h-1.5" />
                      </li>
                      <li>
                        <div className="mb-1 flex items-center justify-between">
                          <span>Nutrition</span>
                          <span>58%</span>
                        </div>
                        <Progress value={58} className="h-1.5" />
                      </li>
                      <li>
                        <div className="mb-1 flex items-center justify-between">
                          <span>Family Health</span>
                          <span>52%</span>
                        </div>
                        <Progress value={52} className="h-1.5" />
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default CampaignPerformance
