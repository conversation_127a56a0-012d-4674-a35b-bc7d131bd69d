"use client"

import React, { useState } from "react"
import { Download } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FinancialPerformanceChart } from "@/components/financial-performance-chart"
// Import specialized drilldown modals
import FinancialPerformanceDrilldownModal from "@/components/financial/FinancialPerformanceDrilldownModal"
import MarketPositionDrilldownModal from "@/components/financial/MarketPositionDrilldownModal"
import OperationalMetricsDrilldownModal from "@/components/financial/OperationalMetricsDrilldownModal"
import PatientDemographicsDrilldownModal from "@/components/financial/PatientDemographicsDrilldownModal"
import PLPerformanceDrilldownModal from "@/components/financial/PLPerformanceDrilldownModal"
import RevenueLineDrilldownModal from "@/components/financial/RevenueLineDrilldownModal"
import RevenueStreamsDrilldownModal from "@/components/financial/RevenueStreamsDrilldownModal"
import InfoTooltip from "@/components/InfoTooltip"
import { KpiCard } from "@/components/kpi-card"
import { MarketPositionChart } from "@/components/market-position-chart"
import { OperationalMetricsChart } from "@/components/operational-metrics-chart"
import { PatientCohortChart } from "@/components/patient-cohort-chart"
import { PatientDemographicsChart } from "@/components/patient-demographics-chart"
import { PLPerformanceChart } from "@/components/pl-performance-chart"
import { RevenueLineChart } from "@/components/revenue-line-chart"
import { RevenueStreamsChart } from "@/components/revenue-streams-chart"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Financial = () => {
  // State for drilldown modals
  const [revenueStreamsDrilldownOpen, setRevenueStreamsDrilldownOpen] =
    useState(false)
  const [
    financialPerformanceDrilldownOpen,
    setFinancialPerformanceDrilldownOpen,
  ] = useState(false)
  const [plPerformanceDrilldownOpen, setPlPerformanceDrilldownOpen] =
    useState(false)
  const [revenueLineDrilldownOpen, setRevenueLineDrilldownOpen] =
    useState(false)
  const [
    patientDemographicsDrilldownOpen,
    setPatientDemographicsDrilldownOpen,
  ] = useState(false)
  const [operationalMetricsDrilldownOpen, setOperationalMetricsDrilldownOpen] =
    useState(false)
  const [marketPositionDrilldownOpen, setMarketPositionDrilldownOpen] =
    useState(false)

  // Sample data for drilldown modals
  const revenueStreamsData = [
    { name: "Primary Care", value: 240000, median: 220000, percentile: 200000 },
    { name: "Specialty", value: 320000, median: 300000, percentile: 280000 },
    { name: "Emergency", value: 180000, median: 170000, percentile: 160000 },
    { name: "Procedures", value: 420000, median: 400000, percentile: 380000 },
    { name: "Labs", value: 140000, median: 130000, percentile: 120000 },
  ]

  const financialPerformanceData = [
    {
      unit: "Cardiology",
      revenue: 300000,
      expenses: 200000,
      profit: 100000,
      target: 90000,
      profitMargin: 33.33,
      roi: 50.0,
    },
    {
      unit: "Orthopedics",
      revenue: 350000,
      expenses: 220000,
      profit: 130000,
      target: 120000,
      profitMargin: 37.14,
      roi: 59.09,
    },
    {
      unit: "Neurology",
      revenue: 400000,
      expenses: 250000,
      profit: 150000,
      target: 140000,
      profitMargin: 37.5,
      roi: 60.0,
    },
    {
      unit: "Pediatrics",
      revenue: 450000,
      expenses: 280000,
      profit: 170000,
      target: 160000,
      profitMargin: 37.78,
      roi: 60.71,
    },
    {
      unit: "OB/GYN",
      revenue: 500000,
      expenses: 300000,
      profit: 200000,
      target: 180000,
      profitMargin: 40.0,
      roi: 66.67,
    },
    {
      unit: "Gastroenterology",
      revenue: 550000,
      expenses: 320000,
      profit: 230000,
      target: 200000,
      profitMargin: 41.82,
      roi: 71.88,
    },
  ]

  const plPerformanceData = [
    { metric: "Revenue", actual: 9800, forecast: 9500, target: 10000 },
    { metric: "Gross Profit", actual: 6800, forecast: 6500, target: 7000 },
    { metric: "EBITDA", actual: 2040, forecast: 2000, target: 3000 },
    { metric: "Net Income", actual: 2156, forecast: 2100, target: 2200 },
  ]

  const revenueLineData = [
    { month: "Jan", primary: 25000, specialty: 35000, emergency: 15000 },
    { month: "Feb", primary: 26000, specialty: 36000, emergency: 16000 },
    { month: "Mar", primary: 27000, specialty: 37000, emergency: 17000 },
    { month: "Apr", primary: 28000, specialty: 38000, emergency: 18000 },
    { month: "May", primary: 29000, specialty: 39000, emergency: 19000 },
    { month: "Jun", primary: 30000, specialty: 40000, emergency: 20000 },
    { month: "Jul", primary: 31000, specialty: 41000, emergency: 21000 },
    { month: "Aug", primary: 32000, specialty: 42000, emergency: 22000 },
    { month: "Sep", primary: 33000, specialty: 43000, emergency: 23000 },
    { month: "Oct", primary: 34000, specialty: 44000, emergency: 24000 },
    { month: "Nov", primary: 35000, specialty: 45000, emergency: 25000 },
    { month: "Dec", primary: 36000, specialty: 46000, emergency: 26000 },
  ]

  const patientDemographicsData = [
    { name: "65+", value: 400, revenue: 380000, visits: 3200 },
    { name: "45-64", value: 400, revenue: 320000, visits: 2600 },
    { name: "30-44", value: 400, revenue: 220000, visits: 1600 },
    { name: "18-29", value: 400, revenue: 180000, visits: 1200 },
    { name: "0-17", value: 400, revenue: 150000, visits: 1400 },
  ]

  const operationalMetricsData = [
    { name: "Cost Per Visit", value: 112, target: 110, industry: 120 },
    { name: "Bed Occupancy", value: 85, target: 90, industry: 82 },
    { name: "Staff Utilization", value: 88, target: 90, industry: 85 },
    { name: "Patient Wait Time", value: 32, target: 30, industry: 38 },
  ]

  const marketPositionData = [
    { name: "Your Hospital", value: 28, growth: 8.5, marketShare: 28 },
    { name: "Memorial Health", value: 22, growth: 5.2, marketShare: 22 },
    { name: "City Medical", value: 18, growth: 3.8, marketShare: 18 },
    { name: "University Hospital", value: 15, growth: 6.5, marketShare: 15 },
    { name: "Regional Care", value: 12, growth: 4.2, marketShare: 12 },
    { name: "Others", value: 5, growth: 2.0, marketShare: 5 },
  ]

  return (
    <>
      <Breadcrumb links={[{ label: "Financial" }]} />

      <div className="flex flex-col gap-1.5">
        <CardTitle>Financial</CardTitle>
        <CardDescription>Financial performance and analysis</CardDescription>
      </div>

      <div className="flex flex-wrap items-center gap-2">
        <Select defaultValue="all-locations">
          <SelectTrigger className="bg-card w-full max-w-40">
            <SelectValue placeholder="All Locations" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-locations">All Locations</SelectItem>
            <SelectItem value="north">North Region</SelectItem>
            <SelectItem value="south">South Region</SelectItem>
            <SelectItem value="east">East Region</SelectItem>
            <SelectItem value="west">West Region</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="all-specialties">
          <SelectTrigger className="bg-card w-full max-w-40">
            <SelectValue placeholder="All Specialties" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-specialties">All Specialties</SelectItem>
            <SelectItem value="cardiology">Cardiology</SelectItem>
            <SelectItem value="orthopedics">Orthopedics</SelectItem>
            <SelectItem value="neurology">Neurology</SelectItem>
            <SelectItem value="pediatrics">Pediatrics</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="all-specifications">
          <SelectTrigger className="bg-card w-full max-w-40">
            <SelectValue placeholder="All Specifications" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-specifications">
              All Specifications
            </SelectItem>
            <SelectItem value="spec1">Specification 1</SelectItem>
            <SelectItem value="spec2">Specification 2</SelectItem>
            <SelectItem value="spec3">Specification 3</SelectItem>
            <SelectItem value="spec4">Specification 4</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="all-time">
          <SelectTrigger className="bg-card w-full max-w-40">
            <SelectValue placeholder="Date Range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-time">All Time</SelectItem>
            <SelectItem value="last-7-days">Last 7 Days</SelectItem>
            <SelectItem value="this-month">This Month</SelectItem>
            <SelectItem value="last-month">Last Month</SelectItem>
            <SelectItem value="custom-range">Custom Range</SelectItem>
          </SelectContent>
        </Select>

        <Button>
          <Download />
          Export Report
        </Button>
      </div>

      <main className="flex flex-1 flex-col gap-4">
        <div className="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-4">
          <KpiCard
            title="Revenue"
            value="$89.5M"
            description="Record quarterly performance"
            trend="+15.2%"
            comparison="vs $77.7M last quarter"
            trendDirection="up"
          />
          <KpiCard
            title="Operating Margin"
            value="23.4%"
            description="Exceeding efficiency goals"
            trend="+2.8%"
            comparison="Target: 20%"
            trendDirection="up"
          />
          <KpiCard
            title="Patient Satisfaction"
            value="94.8%"
            description="All-time high score"
            trend="+1.2%"
            comparison="4.8/5 average rating"
            trendDirection="up"
          />
          <KpiCard
            title="Market Share"
            value="32.7%"
            description="Dominant in key areas"
            trend="+3.5%"
            comparison="Leading in 4/6 specialties"
            trendDirection="up"
          />
          <KpiCard
            title="Patient Growth"
            value="2,845"
            description="Highest monthly intake"
            trend="+16.7%"
            comparison="+435 vs last month"
            trendDirection="up"
          />
          {/* <KpiCard
            title="Staff Efficiency"
            value="96.2%"
            description="Optimal utilization"
            trend="+4.1%"
            comparison="vs 92.4% target"
            trendDirection="up"
          /> */}
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Revenue Streams Analysis
                <InfoTooltip>
                  This chart compares revenue values across five key service
                  lines (
                  <span className="text-primary font-semibold">
                    Primary Care, Specialty, Emergency, Procedures, Labs
                  </span>
                  ), showing actual performance against median benchmarks and
                  25th-75th percentile ranges. The visualization reveals
                  <span className="text-primary font-semibold">
                    {" "}
                    Procedures as the highest revenue generator at $420K
                  </span>
                  , while
                  <span className="text-primary font-semibold">
                    {" "}
                    Labs contribute the least at $140K
                  </span>
                  , helping identify which service lines drive financial
                  performance.
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="last-quarter">
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder="Last Quarter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-quarter">Last Quarter</SelectItem>
                  <SelectItem value="last-year">Last Year</SelectItem>
                  <SelectItem value="all-time">All Time</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setRevenueStreamsDrilldownOpen(true)}
              >
                <RevenueStreamsChart />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Financial Performance Analysis
                <InfoTooltip>
                  This chart presents a multi-dimensional analysis of financial
                  performance across six departments, displaying{" "}
                  <span className="text-primary font-semibold">
                    revenue (blue bars) and expenses (orange bars)
                  </span>{" "}
                  on the left axis, while tracking{" "}
                  <span className="text-primary font-semibold">
                    profit margin and ROI percentages
                  </span>{" "}
                  on the right axis. The visualization shows{" "}
                  <span className="text-primary font-semibold">
                    Gastroenterology leading with 41.82% profit margin and
                    71.88% ROI
                  </span>
                  , while all departments are exceeding their target thresholds
                  (green line), indicating strong overall financial health.
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="revenue-vs-expenses">
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Select Metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="revenue-vs-expenses">
                    Revenue vs Expenses
                  </SelectItem>
                  <SelectItem value="profit-margin">Profit Margin</SelectItem>
                  <SelectItem value="cash-flow">Cash Flow</SelectItem>
                  <SelectItem value="department-costs">
                    Department Costs
                  </SelectItem>
                  <SelectItem value="patient-acquisition-cost">
                    Patient Acquisition Cost
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setFinancialPerformanceDrilldownOpen(true)}
              >
                <PatientCohortChart />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                P&L Performance vs Targets
                <InfoTooltip>
                  This chart compares{" "}
                  <span className="text-primary font-semibold">
                    actual financial performance (blue bars) against forecasted
                    projections (orange bars) and established targets (green
                    bars)
                  </span>{" "}
                  across key P&L metrics. The visualization helps identify where
                  performance is{" "}
                  <span className="text-primary font-semibold">
                    exceeding expectations (like in Revenue and Gross Profit)
                  </span>{" "}
                  and where it{" "}
                  <span className="text-primary font-semibold">
                    falls short of targets (like in Net Income)
                  </span>
                  , providing actionable insights for financial planning and
                  resource allocation.
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="current-month">
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder="Current Month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="current-month">Current Month</SelectItem>
                  <SelectItem value="ytd">YTD</SelectItem>
                  <SelectItem value="last-year">Last Year</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setPlPerformanceDrilldownOpen(true)}
              >
                <PLPerformanceChart />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Revenue Trends
                <InfoTooltip>
                  This line chart tracks monthly revenue trends across three
                  service categories (
                  <span className="text-primary font-semibold">
                    Primary Care, Specialty, and Emergency
                  </span>
                  ) over a 12-month period. The visualization shows consistent
                  growth in all three areas, with{" "}
                  <span className="text-primary font-semibold">
                    Specialty care generating the highest revenue ($46K in
                    December)
                  </span>
                  ,{" "}
                  <span className="text-primary font-semibold">
                    Primary Care showing steady increases ($36K in December)
                  </span>
                  , and{" "}
                  <span className="text-primary font-semibold">
                    Emergency services maintaining the lowest but still growing
                    revenue stream ($26K in December)
                  </span>
                  .
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="last-30-days">
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder="Last 30 Days" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-30-days">Last 30 Days</SelectItem>
                  <SelectItem value="last-quarter">Last Quarter</SelectItem>
                  <SelectItem value="ytd">Year to Date</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setRevenueLineDrilldownOpen(true)}
              >
                <RevenueLineChart />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Revenue by Demographic
                <InfoTooltip>
                  This chart segments patient demographics into key age groups
                  and analyzes their contribution to overall revenue. The
                  visualization reveals that
                  <span className="text-primary font-semibold">
                    {" "}
                    middle-aged adults (45-64){" "}
                  </span>
                  generate the highest revenue at
                  <span className="text-primary font-semibold"> 35%</span>,
                  followed by
                  <span className="text-primary font-semibold">
                    {" "}
                    older adults (65+){" "}
                  </span>{" "}
                  at
                  <span className="text-primary font-semibold"> 28%</span>,
                  <span className="text-primary font-semibold">
                    {" "}
                    young adults (25-44){" "}
                  </span>{" "}
                  at
                  <span className="text-primary font-semibold"> 22%</span>,
                  <span className="text-primary font-semibold">
                    {" "}
                    adolescents (13-24){" "}
                  </span>{" "}
                  at
                  <span className="text-primary font-semibold"> 10%</span>, and
                  <span className="text-primary font-semibold">
                    {" "}
                    children (0-12){" "}
                  </span>{" "}
                  at
                  <span className="text-primary font-semibold"> 5%</span>,
                  helping target marketing efforts and service development to
                  the most financially significant patient populations.
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="age-groups">
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Demographic Group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="age-groups">Age Groups</SelectItem>
                  <SelectItem value="gender">Gender</SelectItem>
                  <SelectItem value="location">Location</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setPatientDemographicsDrilldownOpen(true)}
              >
                <PatientDemographicsChart />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Financial & Operational Metrics
                <InfoTooltip>
                  This chart analyzes key operational metrics that impact
                  financial performance, including{" "}
                  <span className="text-primary font-semibold">
                    cost per visit
                  </span>
                  ,{" "}
                  <span className="text-primary font-semibold">
                    length of stay
                  </span>
                  ,{" "}
                  <span className="text-primary font-semibold">
                    readmission rates
                  </span>
                  , and{" "}
                  <span className="text-primary font-semibold">
                    resource utilization
                  </span>
                  . The visualization compares current performance against
                  historical benchmarks and industry standards, highlighting
                  that{" "}
                  <span className="text-primary font-semibold">
                    cost per visit has decreased by 8%
                  </span>
                  ,{" "}
                  <span className="text-primary font-semibold">
                    length of stay reduced by 0.4 days
                  </span>
                  , and{" "}
                  <span className="text-primary font-semibold">
                    resource utilization improved by 12%
                  </span>
                  , demonstrating the relationship between operational
                  efficiency and financial outcomes.
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="cost-per-visit">
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Cost per Visit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cost-per-visit">Cost per Visit</SelectItem>
                  <SelectItem value="revenue-per-visit">
                    Revenue per Visit
                  </SelectItem>
                  <SelectItem value="operating-margin">
                    Operating Margin
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setOperationalMetricsDrilldownOpen(true)}
              >
                <OperationalMetricsChart />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Cost Benchmarking
                <InfoTooltip>
                  This radar chart compares our{" "}
                  <span className="text-primary font-semibold">
                    {`organization's`} costs (blue area)
                  </span>{" "}
                  against{" "}
                  <span className="text-primary font-semibold">
                    industry averages (orange area)
                  </span>{" "}
                  across six key operational metrics. The visualization shows
                  our{" "}
                  <span className="text-primary font-semibold">
                    competitive advantage
                  </span>{" "}
                  in all areas, with the most significant cost efficiencies in{" "}
                  <span className="text-primary font-semibold">
                    Admin Overhead (12% vs. industry 30%)
                  </span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">
                    Staffing Cost per Visit ($60 vs. industry $85)
                  </span>
                  , while identifying smaller but still positive gaps in{" "}
                  <span className="text-primary font-semibold">
                    Facility Costs per sq.ft ($18 vs. industry $22)
                  </span>
                  .
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="cost-efficiency">
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Cost Efficiency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cost-efficiency">
                    Cost Efficiency
                  </SelectItem>
                  <SelectItem value="department-spend">
                    Department Spend
                  </SelectItem>
                  <SelectItem value="peer-comparison">
                    Peer Comparison
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setMarketPositionDrilldownOpen(true)}
              >
                <MarketPositionChart />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="flex items-center gap-2 text-sm font-medium">
                Financial Performance by Business Unit
                <InfoTooltip>
                  This chart provides a detailed financial performance
                  comparison across six business units (
                  <span className="text-primary font-semibold">Cardiology</span>{" "}
                  through{" "}
                  <span className="text-primary font-semibold">
                    Gastroenterology
                  </span>
                  ), showing{" "}
                  <span className="text-primary font-semibold">revenue</span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">expenses</span>{" "}
                  as stacked bars on the left axis and{" "}
                  <span className="text-primary font-semibold">
                    profit margin/ROI
                  </span>{" "}
                  as lines on the right axis. The visualization reveals{" "}
                  <span className="text-primary font-semibold">
                    Gastroenterology
                  </span>{" "}
                  as the top performer with{" "}
                  <span className="text-primary font-semibold">
                    $550K revenue
                  </span>
                  ,{" "}
                  <span className="text-primary font-semibold">
                    $230K profit
                  </span>
                  ,{" "}
                  <span className="text-primary font-semibold">
                    41.82% profit margin
                  </span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">71.88% ROI</span>
                  , while{" "}
                  <span className="text-primary font-semibold">Cardiology</span>{" "}
                  shows the lowest metrics but still maintains{" "}
                  <span className="text-primary font-semibold">
                    healthy 33.33% profit margin
                  </span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">50% ROI</span>.
                </InfoTooltip>
              </CardTitle>
              <Select defaultValue="cost-efficiency">
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Cost Efficiency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cost-efficiency">
                    Cost Efficiency
                  </SelectItem>
                  <SelectItem value="department-spend">
                    Department Spend
                  </SelectItem>
                  <SelectItem value="peer-comparison">
                    Peer Comparison
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pb-4">
              <div
                className="cursor-pointer"
                onClick={() => setPlPerformanceDrilldownOpen(true)}
              >
                <FinancialPerformanceChart />
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Drilldown Modals */}
      <RevenueStreamsDrilldownModal
        open={revenueStreamsDrilldownOpen}
        onOpenChange={setRevenueStreamsDrilldownOpen}
        initialData={revenueStreamsData}
        title="Revenue Streams Analysis"
        description="Detailed analysis of revenue streams across different service lines and time periods"
      />

      <FinancialPerformanceDrilldownModal
        open={financialPerformanceDrilldownOpen}
        onOpenChange={setFinancialPerformanceDrilldownOpen}
        initialData={financialPerformanceData}
        title="Financial Performance Analysis"
        description="Detailed analysis of financial performance across different business units and metrics"
      />

      <PLPerformanceDrilldownModal
        open={plPerformanceDrilldownOpen}
        onOpenChange={setPlPerformanceDrilldownOpen}
        initialData={plPerformanceData}
        title="P&L Performance Analysis"
        description="Detailed analysis of profit and loss performance against targets and forecasts"
      />

      <RevenueLineDrilldownModal
        open={revenueLineDrilldownOpen}
        onOpenChange={setRevenueLineDrilldownOpen}
        initialData={revenueLineData}
        title="Revenue Trends Analysis"
        description="Detailed analysis of revenue trends across different service categories and time periods"
      />

      <PatientDemographicsDrilldownModal
        open={patientDemographicsDrilldownOpen}
        onOpenChange={setPatientDemographicsDrilldownOpen}
        initialData={patientDemographicsData}
        title="Patient Demographics Analysis"
        description="Detailed analysis of patient demographics and their impact on revenue and utilization"
      />

      <OperationalMetricsDrilldownModal
        open={operationalMetricsDrilldownOpen}
        onOpenChange={setOperationalMetricsDrilldownOpen}
        initialData={operationalMetricsData}
        title="Operational Metrics Analysis"
        description="Detailed analysis of operational metrics and their impact on financial performance"
      />

      <MarketPositionDrilldownModal
        open={marketPositionDrilldownOpen}
        onOpenChange={setMarketPositionDrilldownOpen}
        initialData={marketPositionData}
        title="Market Position Analysis"
        description="Detailed analysis of market position compared to competitors and industry benchmarks"
      />
    </>
  )
}

export default Financial
