"use client"

import React from "react"
import {
  AlertCircle,
  Calendar,
  Download,
  Filter,
  TrendingDown,
  TrendingUp,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for expense categories
const expenseCategories = [
  {
    name: "Personnel",
    amount: 2450000,
    percentage: 42,
    change: "+3.2%",
    isPositive: false,
  },
  {
    name: "Medical Supplies",
    amount: 980000,
    percentage: 17,
    change: "-2.5%",
    isPositive: true,
  },
  {
    name: "Equipment",
    amount: 750000,
    percentage: 13,
    change: "+1.8%",
    isPositive: false,
  },
  {
    name: "Facilities",
    amount: 680000,
    percentage: 12,
    change: "+0.5%",
    isPositive: false,
  },
  {
    name: "Administrative",
    amount: 520000,
    percentage: 9,
    change: "-4.2%",
    isPositive: true,
  },
  {
    name: "IT & Technology",
    amount: 420000,
    percentage: 7,
    change: "+8.3%",
    isPositive: false,
  },
]

// Mock data for departments
const departmentExpenses = [
  {
    name: "Emergency",
    amount: 1250000,
    budget: 1200000,
    variance: "+4.2%",
    isOverBudget: true,
  },
  {
    name: "Surgery",
    amount: 1850000,
    budget: 2000000,
    variance: "-7.5%",
    isOverBudget: false,
  },
  {
    name: "Cardiology",
    amount: 980000,
    budget: 950000,
    variance: "+3.2%",
    isOverBudget: true,
  },
  {
    name: "Pediatrics",
    amount: 720000,
    budget: 750000,
    variance: "-4.0%",
    isOverBudget: false,
  },
  {
    name: "Oncology",
    amount: 1100000,
    budget: 1050000,
    variance: "+4.8%",
    isOverBudget: true,
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

const ExpenseAnalysis = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Financial", href: "/financial" },
        { label: "Expense Analysis" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Expense Analysis</CardTitle>
      <CardDescription>Analyze and optimize your expenses</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          FY 2024 Q2
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(5800000)}</div>
          <div className="mt-1 flex items-center">
            <TrendingUp className="mr-1 h-4 w-4 text-red-500" />
            <p className="text-xs text-red-500">+2.3% from last quarter</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Budget Variance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">-1.2%</div>
          <div className="mt-1 flex items-center">
            <TrendingDown className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">$70,000 under budget</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Cost per Patient
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(1250)}</div>
          <div className="mt-1 flex items-center">
            <TrendingDown className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">-3.5% from last quarter</p>
          </div>
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="categories" className="mt-6">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="categories">Expense Categories</TabsTrigger>
        <TabsTrigger value="departments">Departments</TabsTrigger>
        <TabsTrigger value="trends">Expense Trends</TabsTrigger>
        <TabsTrigger value="optimization">Cost Optimization</TabsTrigger>
      </TabsList>
      <TabsContent value="categories" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Expense Breakdown by Category</CardTitle>
            <CardDescription>
              Analysis of expenses across major operational categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Category</th>
                    <th className="p-2 text-right font-medium">Amount</th>
                    <th className="p-2 text-center font-medium">% of Total</th>
                    <th className="p-2 text-right font-medium">YoY Change</th>
                    <th className="p-2 text-left font-medium">Distribution</th>
                  </tr>
                </thead>
                <tbody>
                  {expenseCategories.map((category, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{category.name}</td>
                      <td className="p-2 text-right">
                        {formatCurrency(category.amount)}
                      </td>
                      <td className="p-2 text-center">
                        {category.percentage}%
                      </td>
                      <td
                        className={`p-2 text-right ${category.isPositive ? "text-green-500" : "text-red-500"}`}
                      >
                        {category.change}
                      </td>
                      <td className="p-2">
                        <div className="w-full">
                          <Progress
                            value={category.percentage}
                            className="h-2"
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="departments" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Expense Analysis by Department</CardTitle>
            <CardDescription>
              Comparison of actual expenses against budgeted amounts by
              department
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-right font-medium">Actual</th>
                    <th className="p-2 text-right font-medium">Budget</th>
                    <th className="p-2 text-right font-medium">Variance</th>
                    <th className="p-2 text-left font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {departmentExpenses.map((dept, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{dept.name}</td>
                      <td className="p-2 text-right">
                        {formatCurrency(dept.amount)}
                      </td>
                      <td className="p-2 text-right">
                        {formatCurrency(dept.budget)}
                      </td>
                      <td
                        className={`p-2 text-right ${!dept.isOverBudget ? "text-green-500" : "text-red-500"}`}
                      >
                        {dept.variance}
                      </td>
                      <td className="p-2">
                        <div
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${dept.isOverBudget ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"}`}
                        >
                          {dept.isOverBudget ? "Over Budget" : "Under Budget"}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="trends" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Expense Trends</CardTitle>
            <CardDescription>
              Historical expense patterns and future projections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted flex h-[300px] items-center justify-center rounded-md">
              <p className="text-muted-foreground">
                Expense trend chart will be displayed here
              </p>
            </div>
            <div className="mt-4 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-3">
                <h3 className="mb-2 font-medium">Key Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      Personnel costs have increased by 3.2% due to new hires in
                      the Cardiology department and annual salary adjustments.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingDown className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Medical supplies expenses decreased by 2.5% following
                      implementation of new inventory management system.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      IT & Technology expenses increased by 8.3% due to
                      cybersecurity infrastructure upgrades.
                    </span>
                  </li>
                </ul>
              </div>
              <div className="rounded-md border p-3">
                <h3 className="mb-2 font-medium">Projections</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                    <span>
                      Personnel expenses projected to increase by 4-5% in Q3 due
                      to planned expansion of Emergency department.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Administrative costs expected to decrease by 3% following
                      implementation of new workflow automation.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                    <span>
                      Equipment expenses likely to increase by 10-15% in Q4 due
                      to planned MRI machine replacement.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="optimization" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Cost Optimization Opportunities</CardTitle>
            <CardDescription>
              Identified areas for potential cost savings and efficiency
              improvements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="rounded-md border p-4">
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">Supply Chain Optimization</h3>
                  <div className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700">
                    High Impact
                  </div>
                </div>
                <p className="text-muted-foreground mb-2 text-sm">
                  Consolidating vendors and implementing just-in-time inventory
                  management could reduce medical supply costs by 8-10%.
                </p>
                <div className="flex items-center justify-between text-sm">
                  <span>Estimated Annual Savings:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(85000)}
                  </span>
                </div>
              </div>

              <div className="rounded-md border p-4">
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">
                    Energy Efficiency Improvements
                  </h3>
                  <div className="rounded-full bg-amber-100 px-2 py-1 text-xs text-amber-700">
                    Medium Impact
                  </div>
                </div>
                <p className="text-muted-foreground mb-2 text-sm">
                  Upgrading to LED lighting and implementing smart HVAC controls
                  could reduce facilities energy costs by 15-20%.
                </p>
                <div className="flex items-center justify-between text-sm">
                  <span>Estimated Annual Savings:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(42000)}
                  </span>
                </div>
              </div>

              <div className="rounded-md border p-4">
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">Staff Scheduling Optimization</h3>
                  <div className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700">
                    High Impact
                  </div>
                </div>
                <p className="text-muted-foreground mb-2 text-sm">
                  Implementing AI-driven staff scheduling based on patient
                  volume patterns could reduce overtime costs by 25-30%.
                </p>
                <div className="flex items-center justify-between text-sm">
                  <span>Estimated Annual Savings:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(120000)}
                  </span>
                </div>
              </div>

              <div className="rounded-md border p-4">
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="font-medium">Telehealth Expansion</h3>
                  <div className="rounded-full bg-amber-100 px-2 py-1 text-xs text-amber-700">
                    Medium Impact
                  </div>
                </div>
                <p className="text-muted-foreground mb-2 text-sm">
                  Expanding telehealth services for follow-up appointments could
                  reduce facility utilization costs and increase provider
                  efficiency.
                </p>
                <div className="flex items-center justify-between text-sm">
                  <span>Estimated Annual Savings:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(65000)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-primary/10 mt-4 rounded-md p-4">
              <div className="mb-2 flex items-center justify-between">
                <h3 className="font-medium">Total Potential Savings</h3>
                <span className="font-bold text-green-600">
                  {formatCurrency(312000)}
                </span>
              </div>
              <p className="text-muted-foreground text-sm">
                Implementing all recommended optimization strategies could
                reduce annual expenses by approximately 5.4%.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default ExpenseAnalysis
