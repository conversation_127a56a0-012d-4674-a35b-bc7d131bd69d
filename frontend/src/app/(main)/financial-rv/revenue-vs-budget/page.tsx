"use client"

import React from "react"
import {
  AlertCircle,
  Calendar,
  Download,
  Filter,
  TrendingDown,
  TrendingUp,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for revenue by department
const departmentRevenue = [
  {
    department: "Cardiology",
    actual: 2450000,
    budget: 2300000,
    variance: 150000,
    variancePercent: 6.5,
    trend: "+8.2%",
  },
  {
    department: "Orthopedics",
    actual: 1850000,
    budget: 2000000,
    variance: -150000,
    variancePercent: -7.5,
    trend: "-2.3%",
  },
  {
    department: "Oncology",
    actual: 3200000,
    budget: 3000000,
    variance: 200000,
    variancePercent: 6.7,
    trend: "+5.1%",
  },
  {
    department: "Neurology",
    actual: 1650000,
    budget: 1800000,
    variance: -150000,
    variancePercent: -8.3,
    trend: "-3.5%",
  },
  {
    department: "Pediatrics",
    actual: 1950000,
    budget: 1800000,
    variance: 150000,
    variancePercent: 8.3,
    trend: "+10.2%",
  },
]

// Mock data for revenue by service line
const serviceLineRevenue = [
  {
    serviceLine: "Inpatient Services",
    actual: 5200000,
    budget: 5000000,
    variance: 200000,
    variancePercent: 4.0,
  },
  {
    serviceLine: "Outpatient Services",
    actual: 3850000,
    budget: 4000000,
    variance: -150000,
    variancePercent: -3.8,
  },
  {
    serviceLine: "Emergency Services",
    actual: 1750000,
    budget: 1600000,
    variance: 150000,
    variancePercent: 9.4,
  },
  {
    serviceLine: "Diagnostic Services",
    actual: 2100000,
    budget: 2200000,
    variance: -100000,
    variancePercent: -4.5,
  },
  {
    serviceLine: "Surgical Services",
    actual: 4200000,
    budget: 4000000,
    variance: 200000,
    variancePercent: 5.0,
  },
]

// Mock data for monthly revenue
const monthlyRevenue = [
  {
    month: "Jan",
    actual: 1450000,
    budget: 1400000,
    variance: 50000,
    variancePercent: 3.6,
  },
  {
    month: "Feb",
    actual: 1380000,
    budget: 1450000,
    variance: -70000,
    variancePercent: -4.8,
  },
  {
    month: "Mar",
    actual: 1520000,
    budget: 1500000,
    variance: 20000,
    variancePercent: 1.3,
  },
  {
    month: "Apr",
    actual: 1620000,
    budget: 1550000,
    variance: 70000,
    variancePercent: 4.5,
  },
  {
    month: "May",
    actual: 1580000,
    budget: 1600000,
    variance: -20000,
    variancePercent: -1.3,
  },
  {
    month: "Jun",
    actual: 1650000,
    budget: 1600000,
    variance: 50000,
    variancePercent: 3.1,
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

const RevenueVSBudget = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Financial", href: "/financial" },
        { label: "Revenue vs. Budget" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Revenue vs. Budget</CardTitle>
      <CardDescription>
        Review your revenue vs. budget in details
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          FY 2024 Q2
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(17100000)}</div>
          <div className="mt-1 flex items-center">
            <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">+3.2% from last quarter</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Budget</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(16800000)}</div>
          <div className="mt-1 flex items-center">
            <TrendingUp className="text-muted-foreground mr-1 h-4 w-4" />
            <p className="text-muted-foreground text-xs">
              +2.5% from last quarter
            </p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Variance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-500">
            {formatCurrency(300000)}
          </div>
          <div className="mt-1 flex items-center">
            <p className="text-xs text-green-500">+1.8% above budget</p>
          </div>
          <Progress value={101.8} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">YTD Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(34200000)}</div>
          <div className="mt-1 flex items-center">
            <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">+2.1% above YTD budget</p>
          </div>
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="departments" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="departments">By Department</TabsTrigger>
        <TabsTrigger value="services">By Service Line</TabsTrigger>
        <TabsTrigger value="monthly">Monthly Trend</TabsTrigger>
      </TabsList>
      <TabsContent value="departments" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs. Budget by Department</CardTitle>
            <CardDescription>
              Comparison of actual revenue against budgeted amounts by
              department
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-right font-medium">Actual</th>
                    <th className="p-2 text-right font-medium">Budget</th>
                    <th className="p-2 text-right font-medium">Variance</th>
                    <th className="p-2 text-right font-medium">Variance %</th>
                    <th className="p-2 text-right font-medium">YoY Trend</th>
                    <th className="p-2 text-left font-medium">Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {departmentRevenue.map((dept, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{dept.department}</td>
                      <td className="p-2 text-right">
                        {formatCurrency(dept.actual)}
                      </td>
                      <td className="p-2 text-right">
                        {formatCurrency(dept.budget)}
                      </td>
                      <td
                        className={`p-2 text-right ${dept.variance >= 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {dept.variance >= 0 ? "+" : ""}
                        {formatCurrency(dept.variance)}
                      </td>
                      <td
                        className={`p-2 text-right ${dept.variancePercent >= 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {dept.variancePercent >= 0 ? "+" : ""}
                        {dept.variancePercent}%
                      </td>
                      <td
                        className={`p-2 text-right ${dept.trend.startsWith("+") ? "text-green-500" : "text-red-500"}`}
                      >
                        {dept.trend}
                      </td>
                      <td className="p-2">
                        <div className="w-full">
                          <Progress
                            value={
                              dept.variancePercent >= 0
                                ? 100 + dept.variancePercent
                                : 100 - Math.abs(dept.variancePercent)
                            }
                            className={`h-2 ${dept.variancePercent >= 0 ? "bg-green-100" : "bg-red-100"}`}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Key Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Pediatrics shows the strongest growth (+10.2% YoY) due to
                      expanded services and increased patient volume.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Oncology exceeded budget by 6.7% due to new treatment
                      options and increased referrals.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingDown className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      Neurology is 8.3% below budget, primarily due to temporary
                      staffing shortages and equipment downtime.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Recommendations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Investigate staffing issues in Neurology to address the
                      8.3% revenue shortfall and develop a recovery plan.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Consider expanding Pediatrics services further based on
                      strong performance and growth trends.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Review Orthopedics service line to identify opportunities
                      for improvement and address the 7.5% shortfall.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="services" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs. Budget by Service Line</CardTitle>
            <CardDescription>
              Comparison of actual revenue against budgeted amounts by service
              line
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Service Line</th>
                    <th className="p-2 text-right font-medium">Actual</th>
                    <th className="p-2 text-right font-medium">Budget</th>
                    <th className="p-2 text-right font-medium">Variance</th>
                    <th className="p-2 text-right font-medium">Variance %</th>
                    <th className="p-2 text-left font-medium">Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {serviceLineRevenue.map((service, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{service.serviceLine}</td>
                      <td className="p-2 text-right">
                        {formatCurrency(service.actual)}
                      </td>
                      <td className="p-2 text-right">
                        {formatCurrency(service.budget)}
                      </td>
                      <td
                        className={`p-2 text-right ${service.variance >= 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {service.variance >= 0 ? "+" : ""}
                        {formatCurrency(service.variance)}
                      </td>
                      <td
                        className={`p-2 text-right ${service.variancePercent >= 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {service.variancePercent >= 0 ? "+" : ""}
                        {service.variancePercent}%
                      </td>
                      <td className="p-2">
                        <div className="w-full">
                          <Progress
                            value={
                              service.variancePercent >= 0
                                ? 100 + service.variancePercent
                                : 100 - Math.abs(service.variancePercent)
                            }
                            className={`h-2 ${service.variancePercent >= 0 ? "bg-green-100" : "bg-red-100"}`}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-primary/10 mt-6 rounded-md p-4">
              <h3 className="mb-2 text-sm font-medium">
                Service Line Analysis
              </h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Analysis of service line performance reveals several key trends
                and opportunities:
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="mb-1 text-sm font-medium">
                    Strong Performers
                  </h4>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-center justify-between">
                      <span>Emergency Services</span>
                      <span className="text-green-500">+9.4%</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>Surgical Services</span>
                      <span className="text-green-500">+5.0%</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>Inpatient Services</span>
                      <span className="text-green-500">+4.0%</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="mb-1 text-sm font-medium">
                    Underperforming Areas
                  </h4>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-center justify-between">
                      <span>Outpatient Services</span>
                      <span className="text-red-500">-3.8%</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>Diagnostic Services</span>
                      <span className="text-red-500">-4.5%</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="mt-4">
                <h4 className="mb-1 text-sm font-medium">Recommendations</h4>
                <ul className="space-y-1 text-sm">
                  <li>
                    Investigate declining Outpatient Services revenue,
                    potentially related to increased competition or scheduling
                    issues.
                  </li>
                  <li>
                    Review Diagnostic Services pricing and utilization to
                    address the 4.5% shortfall against budget.
                  </li>
                  <li>
                    Consider expanding Emergency Services capacity based on
                    strong performance (+9.4% above budget).
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="monthly" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Revenue Trend</CardTitle>
            <CardDescription>
              Monthly comparison of actual revenue against budget
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted mb-6 flex h-[300px] items-center justify-center rounded-md">
              <p className="text-muted-foreground">
                Monthly revenue trend chart will be displayed here
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Month</th>
                    <th className="p-2 text-right font-medium">Actual</th>
                    <th className="p-2 text-right font-medium">Budget</th>
                    <th className="p-2 text-right font-medium">Variance</th>
                    <th className="p-2 text-right font-medium">Variance %</th>
                    <th className="p-2 text-left font-medium">Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {monthlyRevenue.map((month, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{month.month}</td>
                      <td className="p-2 text-right">
                        {formatCurrency(month.actual)}
                      </td>
                      <td className="p-2 text-right">
                        {formatCurrency(month.budget)}
                      </td>
                      <td
                        className={`p-2 text-right ${month.variance >= 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {month.variance >= 0 ? "+" : ""}
                        {formatCurrency(month.variance)}
                      </td>
                      <td
                        className={`p-2 text-right ${month.variancePercent >= 0 ? "text-green-500" : "text-red-500"}`}
                      >
                        {month.variancePercent >= 0 ? "+" : ""}
                        {month.variancePercent}%
                      </td>
                      <td className="p-2">
                        <div className="w-full">
                          <Progress
                            value={
                              month.variancePercent >= 0
                                ? 100 + month.variancePercent
                                : 100 - Math.abs(month.variancePercent)
                            }
                            className={`h-2 ${month.variancePercent >= 0 ? "bg-green-100" : "bg-red-100"}`}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="bg-muted/50 border-t">
                    <td className="p-2 font-medium">Q2 Total</td>
                    <td className="p-2 text-right font-medium">
                      {formatCurrency(9200000)}
                    </td>
                    <td className="p-2 text-right font-medium">
                      {formatCurrency(9100000)}
                    </td>
                    <td className="p-2 text-right font-medium text-green-500">
                      +{formatCurrency(100000)}
                    </td>
                    <td className="p-2 text-right font-medium text-green-500">
                      +1.1%
                    </td>
                    <td className="p-2"></td>
                  </tr>
                </tfoot>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Monthly Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      April showed the strongest performance at 4.5% above
                      budget, driven by increased surgical procedures and
                      emergency visits.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingDown className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      February was 4.8% below budget due to seasonal factors and
                      fewer operating days.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Overall Q2 performance was positive at 1.1% above budget,
                      showing recovery from Q1 performance.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Forecast</h3>
                <p className="text-muted-foreground mb-4 text-sm">
                  Based on current trends and seasonal patterns, the forecast
                  for upcoming months is:
                </p>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center justify-between">
                    <span>July</span>
                    <span className="text-green-500">+2.5% above budget</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>August</span>
                    <span className="text-green-500">+3.0% above budget</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>September</span>
                    <span className="text-green-500">+2.0% above budget</span>
                  </li>
                  <li className="mt-2 flex items-center justify-between font-medium">
                    <span>Q3 Forecast</span>
                    <span className="text-green-500">+2.5% above budget</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default RevenueVSBudget
