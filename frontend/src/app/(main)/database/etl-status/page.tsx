"use client"

import React from "react"
import {
  AlertCircle,
  Calendar,
  CheckCircle2,
  Clock,
  Download,
  Filter,
  Play,
  RefreshCw,
  XCircle,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for ETL jobs
const etlJobs = [
  {
    id: "epic-daily",
    name: "Epic EHR Daily Import",
    source: "Epic EHR",
    destination: "Data Warehouse",
    status: "Completed",
    lastRun: "Today at 06:30 AM",
    nextRun: "Tomorrow at 06:30 AM",
    duration: "42 minutes",
    recordsProcessed: 24580,
    errors: 0,
    warnings: 12,
  },
  {
    id: "cerner-daily",
    name: "Cerner EHR Daily Import",
    source: "<PERSON>rner EHR",
    destination: "Data Warehouse",
    status: "Completed",
    lastRun: "Today at 04:15 AM",
    nextRun: "Tomorrow at 04:15 AM",
    duration: "38 minutes",
    recordsProcessed: 18920,
    errors: 0,
    warnings: 8,
  },
  {
    id: "allscripts-daily",
    name: "Allscripts Daily Import",
    source: "Allscripts",
    destination: "Data Warehouse",
    status: "Failed",
    lastRun: "Today at 02:00 AM",
    nextRun: "Manual restart required",
    duration: "15 minutes (interrupted)",
    recordsProcessed: 4250,
    errors: 1,
    warnings: 3,
  },
  {
    id: "labcorp-daily",
    name: "LabCorp Results Import",
    source: "LabCorp",
    destination: "Data Warehouse",
    status: "Running",
    lastRun: "Running now...",
    nextRun: "Tomorrow at 02:00 AM",
    duration: "Running for 15 minutes",
    recordsProcessed: 8750,
    errors: 0,
    warnings: 5,
  },
  {
    id: "billing-daily",
    name: "Billing System Import",
    source: "Billing System",
    destination: "Financial Data Mart",
    status: "Scheduled",
    lastRun: "Yesterday at 11:00 PM",
    nextRun: "Today at 11:00 PM",
    duration: "28 minutes",
    recordsProcessed: 12450,
    errors: 0,
    warnings: 0,
  },
]

// Mock data for ETL performance
const etlPerformance = [
  {
    job: "Epic EHR Daily Import",
    avgDuration: 45,
    recordsPerMinute: 585,
    successRate: 99.8,
    trend: "-3 min",
  },
  {
    job: "Cerner EHR Daily Import",
    avgDuration: 40,
    recordsPerMinute: 498,
    successRate: 99.5,
    trend: "-2 min",
  },
  {
    job: "Allscripts Daily Import",
    avgDuration: 32,
    recordsPerMinute: 420,
    successRate: 95.2,
    trend: "+5 min",
  },
  {
    job: "LabCorp Results Import",
    avgDuration: 25,
    recordsPerMinute: 650,
    successRate: 99.9,
    trend: "-1 min",
  },
  {
    job: "Billing System Import",
    avgDuration: 30,
    recordsPerMinute: 445,
    successRate: 100,
    trend: "No change",
  },
]

// Mock data for ETL errors
const etlErrors = [
  {
    id: 1,
    job: "Allscripts Daily Import",
    timestamp: "2024-05-15 02:15:32",
    errorType: "Connection Timeout",
    message: "Connection to Allscripts API timed out after 60 seconds",
    records: "N/A",
    status: "Unresolved",
  },
  {
    id: 2,
    job: "Epic EHR Daily Import",
    timestamp: "2024-05-14 06:42:18",
    errorType: "Data Validation",
    message: "12 records failed validation: Invalid date format",
    records: "12",
    status: "Resolved",
  },
  {
    id: 3,
    job: "Cerner EHR Daily Import",
    timestamp: "2024-05-13 04:28:45",
    errorType: "Data Transformation",
    message: "Failed to transform provider credentials for 8 records",
    records: "8",
    status: "Resolved",
  },
  {
    id: 4,
    job: "NextGen Healthcare Import",
    timestamp: "2024-05-12 08:15:22",
    errorType: "Authentication Error",
    message: "API authentication failed: Invalid credentials",
    records: "N/A",
    status: "Resolved",
  },
  {
    id: 5,
    job: "Allscripts Daily Import",
    timestamp: "2024-05-11 02:08:12",
    errorType: "Connection Timeout",
    message: "Connection to Allscripts API timed out after 60 seconds",
    records: "N/A",
    status: "Resolved",
  },
]

const ETLStatus = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Database", href: "/database" },
        { label: "ETL Status" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>ETL Status</CardTitle>
      <CardDescription>Monitor and optimize your ETL status</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 24 Hours
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">ETL Jobs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">5</div>
          <div className="mt-1 flex items-center">
            <div className="flex gap-2 text-xs">
              <span className="flex items-center text-green-500">
                <CheckCircle2 className="mr-1 h-3 w-3" />2 Completed
              </span>
              <span className="flex items-center text-amber-500">
                <Clock className="mr-1 h-3 w-3" />1 Running
              </span>
              <span className="flex items-center text-red-500">
                <XCircle className="mr-1 h-3 w-3" />1 Failed
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Records Processed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">68,950</div>
          <p className="text-muted-foreground text-xs">+12.5% from yesterday</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">98.8%</div>
          <p className="text-muted-foreground text-xs">-0.2% from yesterday</p>
          <Progress value={98.8} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Avg. Processing Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">34.5 min</div>
          <p className="text-muted-foreground text-xs">
            -2.3 min from yesterday
          </p>
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="jobs" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="jobs">ETL Jobs</TabsTrigger>
        <TabsTrigger value="performance">Performance</TabsTrigger>
        <TabsTrigger value="errors">Errors & Warnings</TabsTrigger>
      </TabsList>
      <TabsContent value="jobs" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>ETL Jobs Status</CardTitle>
            <CardDescription>Current status of all ETL jobs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Job Name</th>
                    <th className="p-2 text-left font-medium">Source</th>
                    <th className="p-2 text-left font-medium">Destination</th>
                    <th className="p-2 text-center font-medium">Status</th>
                    <th className="p-2 text-left font-medium">Last Run</th>
                    <th className="p-2 text-left font-medium">Next Run</th>
                    <th className="p-2 text-left font-medium">Duration</th>
                    <th className="p-2 text-right font-medium">Records</th>
                    <th className="p-2 text-center font-medium">Issues</th>
                    <th className="p-2 text-center font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {etlJobs.map((job) => (
                    <tr key={job.id} className="border-b">
                      <td className="p-2 font-medium">{job.name}</td>
                      <td className="p-2">{job.source}</td>
                      <td className="p-2">{job.destination}</td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs ${
                            job.status === "Completed"
                              ? "bg-green-100 text-green-700"
                              : job.status === "Running"
                                ? "bg-blue-100 text-blue-700"
                                : job.status === "Failed"
                                  ? "bg-red-100 text-red-700"
                                  : "bg-gray-100 text-gray-700"
                          }`}
                        >
                          {job.status === "Completed" ? (
                            <CheckCircle2 className="h-3 w-3" />
                          ) : job.status === "Running" ? (
                            <Clock className="h-3 w-3" />
                          ) : job.status === "Failed" ? (
                            <XCircle className="h-3 w-3" />
                          ) : (
                            <Calendar className="h-3 w-3" />
                          )}
                          {job.status}
                        </span>
                      </td>
                      <td className="p-2">{job.lastRun}</td>
                      <td className="p-2">{job.nextRun}</td>
                      <td className="p-2">{job.duration}</td>
                      <td className="p-2 text-right">
                        {job.recordsProcessed.toLocaleString()}
                      </td>
                      <td className="p-2 text-center">
                        {job.errors > 0 ? (
                          <span className="font-medium text-red-500">
                            {job.errors} errors
                          </span>
                        ) : job.warnings > 0 ? (
                          <span className="text-amber-500">
                            {job.warnings} warnings
                          </span>
                        ) : (
                          <span className="text-green-500">None</span>
                        )}
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex justify-center gap-2">
                          {job.status === "Failed" && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <Play className="h-4 w-4 text-green-500" />
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="performance" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>ETL Performance Metrics</CardTitle>
            <CardDescription>
              Performance metrics for ETL jobs over the last 7 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Job Name</th>
                    <th className="p-2 text-center font-medium">
                      Avg. Duration
                    </th>
                    <th className="p-2 text-center font-medium">
                      Records/Minute
                    </th>
                    <th className="p-2 text-center font-medium">
                      Success Rate
                    </th>
                    <th className="p-2 text-right font-medium">Trend</th>
                  </tr>
                </thead>
                <tbody>
                  {etlPerformance.map((perf, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{perf.job}</td>
                      <td className="p-2 text-center">
                        {perf.avgDuration} min
                      </td>
                      <td className="p-2 text-center">
                        {perf.recordsPerMinute}
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{perf.successRate}%</span>
                          <Progress
                            value={perf.successRate}
                            className={`h-1.5 w-16 ${
                              perf.successRate < 97
                                ? "bg-red-100"
                                : perf.successRate < 99
                                  ? "bg-amber-100"
                                  : "bg-green-100"
                            }`}
                          />
                        </div>
                      </td>
                      <td
                        className={`p-2 text-right ${
                          perf.trend.startsWith("-")
                            ? "text-green-500"
                            : perf.trend.startsWith("+")
                              ? "text-red-500"
                              : "text-muted-foreground"
                        }`}
                      >
                        {perf.trend}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">
                  Performance Insights
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle2 className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      LabCorp Results Import shows the highest efficiency with
                      650 records processed per minute.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                    <span>
                      Allscripts Daily Import has shown a 5-minute increase in
                      processing time, suggesting potential performance
                      degradation.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle2 className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Epic and Cerner imports have both improved processing
                      times by 2-3 minutes after recent optimization efforts.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Recommendations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Investigate Allscripts performance degradation, focusing
                      on network connectivity and API response times.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Consider implementing the same optimization techniques
                      used for Epic and Cerner imports to other ETL processes.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Review and potentially increase parallelization for
                      Billing System Import to improve records/minute processing
                      rate.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="errors" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>ETL Errors & Warnings</CardTitle>
            <CardDescription>
              Recent errors and warnings from ETL processes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Job Name</th>
                    <th className="p-2 text-left font-medium">Timestamp</th>
                    <th className="p-2 text-left font-medium">Error Type</th>
                    <th className="p-2 text-left font-medium">Message</th>
                    <th className="p-2 text-center font-medium">
                      Records Affected
                    </th>
                    <th className="p-2 text-center font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {etlErrors.map((error) => (
                    <tr key={error.id} className="border-b">
                      <td className="p-2 font-medium">{error.job}</td>
                      <td className="p-2">{error.timestamp}</td>
                      <td className="p-2">{error.errorType}</td>
                      <td className="p-2">{error.message}</td>
                      <td className="p-2 text-center">{error.records}</td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs ${
                            error.status === "Resolved"
                              ? "bg-green-100 text-green-700"
                              : "bg-red-100 text-red-700"
                          }`}
                        >
                          {error.status === "Resolved" ? (
                            <CheckCircle2 className="h-3 w-3" />
                          ) : (
                            <XCircle className="h-3 w-3" />
                          )}
                          {error.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-primary/10 mt-6 rounded-md p-4">
              <h3 className="mb-2 text-sm font-medium">Error Analysis</h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Analysis of recent ETL errors reveals several patterns:
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <AlertCircle className="mt-0.5 h-4 w-4 text-red-500" />
                  <span>
                    Allscripts connection timeouts are recurring (2 incidents in
                    the past 5 days), suggesting potential network or API
                    availability issues that need investigation.
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                  <span>
                    Data validation and transformation errors are common but
                    typically affect a small number of records and are being
                    resolved promptly.
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="mt-0.5 h-4 w-4 text-green-500" />
                  <span>
                    Authentication errors have decreased significantly after
                    implementing credential management improvements last month.
                  </span>
                </li>
              </ul>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-muted-foreground text-sm">
              Showing 5 of 18 errors and warnings
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default ETLStatus
