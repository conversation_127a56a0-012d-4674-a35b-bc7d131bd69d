"use client"

import { ChevronRight, DatabaseIcon, Server } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import type {
  Database,
  DatabaseSource,
} from "@/app/(main)/database/data/mock-data"

interface DatabaseSourceSelectorProps {
  sources: DatabaseSource[]
  selectedSource: DatabaseSource | null
  onSourceSelect: (source: DatabaseSource) => void
  selectedDatabase: Database | null
  onDatabaseSelect: (database: Database) => void
}

export default function DatabaseSourceSelector({
  sources,
  selectedSource,
  onSourceSelect,
  selectedDatabase,
  onDatabaseSelect,
}: DatabaseSourceSelectorProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Database Sources</h3>

      <div className="space-y-1">
        {sources.map((source) => (
          <div key={source.id}>
            <Button
              variant={selectedSource?.id === source.id ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => onSourceSelect(source)}
            >
              <Server className="mr-2 h-4 w-4" />
              {source.name}
              {selectedSource?.id === source.id && (
                <ChevronRight className="ml-auto h-4 w-4" />
              )}
            </Button>

            {selectedSource?.id === source.id && (
              <div className="mt-1 ml-6 space-y-1">
                {source.databases.map((database) => (
                  <Button
                    key={database.id}
                    variant={
                      selectedDatabase?.id === database.id ? "default" : "ghost"
                    }
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => onDatabaseSelect(database)}
                  >
                    <DatabaseIcon className="mr-2 h-3.5 w-3.5" />
                    {database.name}
                  </Button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
