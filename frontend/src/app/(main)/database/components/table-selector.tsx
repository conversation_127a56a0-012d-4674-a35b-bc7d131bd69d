"use client"

import { TableIcon } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import type { TableSchema } from "@/app/(main)/database/data/mock-data"

interface TableSelectorProps {
  tables: TableSchema[]
  selectedTable: TableSchema | null
  onTableSelect: (table: TableSchema) => void
}

export default function TableSelector({
  tables,
  selectedTable,
  onTableSelect,
}: TableSelectorProps) {
  return (
    <div className="mt-6 space-y-4">
      <Separator />
      <h3 className="text-sm font-medium">Tables</h3>

      <div className="space-y-1">
        {tables.map((table) => (
          <Button
            key={table.id}
            variant={selectedTable?.id === table.id ? "default" : "ghost"}
            className="w-full justify-start"
            onClick={() => onTableSelect(table)}
          >
            <TableIcon className="mr-2 h-4 w-4" />
            {table.name}
            <span className="ml-auto text-xs opacity-75">{table.rowCount}</span>
          </Button>
        ))}
      </div>
    </div>
  )
}
