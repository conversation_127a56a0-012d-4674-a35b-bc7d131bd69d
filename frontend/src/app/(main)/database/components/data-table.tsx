"use client"

import { useState } from "react"
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  DatabaseIcon,
  Download,
  Edit2,
  FileText,
  Filter,
  Layers,
  Lock,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Save,
  Search,
  Server,
  Trash2,
  Users,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  mockDatabases,
  type TableSchema,
} from "@/app/(main)/database/data/mock-data"

interface DataTableProps {
  table?: TableSchema
  onTableSelect?: (table: TableSchema | null) => void
}

export default function DataTable({ table, onTableSelect }: DataTableProps) {
  // Source selection state
  const [selectedSourceId, setSelectedSourceId] = useState<string | null>(null)
  const [selectedDatabaseId, setSelectedDatabaseId] = useState<string | null>(
    null
  )
  const [selectedTableId, setSelectedTableId] = useState<string | null>(null)

  // Derived state from selected IDs
  const selectedSource = selectedSourceId
    ? mockDatabases.find((s) => s.id === selectedSourceId) || null
    : null

  const selectedDatabase =
    selectedSource && selectedDatabaseId
      ? selectedSource.databases.find((db) => db.id === selectedDatabaseId) ||
        null
      : null

  const selectedTable =
    selectedDatabase && selectedTableId
      ? selectedDatabase.tables.find((t) => t.id === selectedTableId) || null
      : table // Use passed table if available

  // Table pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")

  // Source selection handlers
  const handleSourceChange = (sourceId: string) => {
    setSelectedSourceId(sourceId)
    setSelectedDatabaseId(null)
    setSelectedTableId(null)
    setCurrentPage(1)
    setSearchQuery("")
  }

  const handleDatabaseChange = (databaseId: string) => {
    setSelectedDatabaseId(databaseId)
    setSelectedTableId(null)
    setCurrentPage(1)
    setSearchQuery("")
  }

  const handleTableSelect = (tableId: string) => {
    setSelectedTableId(tableId)
    setCurrentPage(1)
    setSearchQuery("")

    // Notify parent component of selected table
    if (onTableSelect) {
      const newTable =
        selectedDatabase?.tables.find((t) => t.id === tableId) || null
      onTableSelect(newTable)
    }
  }

  const pageSize = 10
  const totalPages = selectedTable
    ? Math.ceil(selectedTable.data.length / pageSize)
    : 0

  const filteredData =
    selectedTable?.data.filter((row) => {
      if (!searchQuery) return true

      return Object.values(row).some((value) =>
        String(value).toLowerCase().includes(searchQuery.toLowerCase())
      )
    }) || []

  const paginatedData = filteredData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  // Add tabs state for the component
  const [activeTab, setActiveTab] = useState("data")

  return (
    <div className="space-y-4">
      {/* Unified Controls Row - Source selectors, Tabs, and Action Buttons */}
      <div className="mb-4 flex flex-wrap items-center justify-between gap-2 overflow-x-auto py-1">
        {/* Left section: Database selection with consolidated approach */}
        <div className="flex min-w-0 items-center gap-2">
          {/* Unified database selection dropdown for all screen sizes */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8 whitespace-nowrap"
              >
                <Server className="mr-2 h-4 w-4" />
                {selectedTable ? (
                  // If a table is selected, show its name
                  <span className="max-w-[180px] truncate">
                    {selectedTable.name}
                  </span>
                ) : selectedDatabase ? (
                  // If only database is selected, show its name + prompt for table
                  <span className="max-w-[180px] truncate">
                    {selectedDatabase.name} • Select Table
                  </span>
                ) : selectedSource ? (
                  // If only source is selected, show its name + prompt for database
                  <span className="max-w-[180px] truncate">
                    {selectedSource.name} • Select DB
                  </span>
                ) : (
                  // Default state
                  "Select Database"
                )}
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-[280px]">
              {/* Sources section */}
              <div className="p-2">
                <p className="mb-1 text-xs font-medium">Sources</p>
                <div className="grid grid-cols-1 gap-1">
                  {mockDatabases.map((source) => (
                    <Button
                      key={source.id}
                      variant={
                        selectedSourceId === source.id ? "outline" : "ghost"
                      }
                      className={`h-8 w-full justify-start px-2 text-sm ${selectedSourceId === source.id ? "border-slate-200 bg-slate-100 dark:border-slate-700 dark:bg-slate-800" : ""}`}
                      onClick={() => handleSourceChange(source.id)}
                    >
                      <Server className="mr-2 h-4 w-4" />
                      {source.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Databases section */}
              {selectedSource && (
                <div className="border-t p-2">
                  <p className="mb-1 text-xs font-medium">Databases</p>
                  <div className="grid grid-cols-1 gap-1">
                    {selectedSource.databases.map((database) => (
                      <Button
                        key={database.id}
                        variant={
                          selectedDatabaseId === database.id
                            ? "outline"
                            : "ghost"
                        }
                        className={`h-8 w-full justify-start px-2 text-sm ${selectedDatabaseId === database.id ? "border-slate-200 bg-slate-100 dark:border-slate-700 dark:bg-slate-800" : ""}`}
                        onClick={() => handleDatabaseChange(database.id)}
                      >
                        <DatabaseIcon className="mr-2 h-4 w-4" />
                        {database.name}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Tables section */}
              {selectedDatabase && (
                <div className="border-t p-2">
                  <p className="mb-1 text-xs font-medium">Tables</p>
                  <div className="grid max-h-[180px] grid-cols-1 gap-1 overflow-auto">
                    {selectedDatabase.tables.map((table) => (
                      <Button
                        key={table.id}
                        variant={
                          selectedTableId === table.id ? "outline" : "ghost"
                        }
                        className={`h-8 w-full justify-start px-2 text-sm ${selectedTableId === table.id ? "border-slate-200 bg-slate-100 dark:border-slate-700 dark:bg-slate-800" : ""}`}
                        onClick={() => handleTableSelect(table.id)}
                      >
                        <div className="flex w-full items-center justify-between">
                          <span>{table.name}</span>
                          <span className="text-muted-foreground text-xs">
                            {table.rowCount} rows
                          </span>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Tabs */}
          <div className="ml-1 border-l pl-2">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="h-8">
                <TabsTrigger value="data" className="px-2 md:px-3">
                  Data
                </TabsTrigger>
                <TabsTrigger
                  value="schema"
                  disabled={!selectedTable}
                  className="px-2 md:px-3"
                >
                  Schema
                </TabsTrigger>
                <TabsTrigger
                  value="query"
                  disabled={!selectedTable}
                  className="px-2 md:px-3"
                >
                  Query
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Right section: Search and action buttons with responsive behavior */}
        <div className="ml-auto flex items-center gap-1">
          {/* Search input - visible on all but the smallest screens */}
          <div className="relative hidden w-[180px] sm:block md:w-[220px] lg:w-[260px] xl:w-[300px]">
            <Search className="text-muted-foreground absolute top-2 left-2.5 h-4 w-4" />
            <Input
              type="search"
              placeholder="Search table contents..."
              className="h-8 pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Primary action buttons - shown at large screens only */}
          <div className="hidden items-center gap-1 2xl:flex">
            <Button variant="outline" size="sm" className="h-8">
              <Filter className="mr-1 h-4 w-4" />
              Filter
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <RefreshCw className="mr-1 h-4 w-4" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <Download className="mr-1 h-4 w-4" />
              Export
            </Button>

            {/* Conditional buttons based on selection state */}
            {selectedTable && (
              <Button variant="outline" size="sm" className="ml-1 h-8">
                <Edit2 className="mr-1 h-4 w-4" />
                Edit
              </Button>
            )}
          </div>

          {/* Main actions dropdown - shown on all screen sizes */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-1 h-8">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only 2xl:not-sr-only 2xl:ml-1">
                  Actions
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {/* Data actions group */}
              <div className="p-2">
                <p className="text-muted-foreground mb-1 text-xs font-medium">
                  Data Actions
                </p>
                <DropdownMenuItem onClick={() => {}}>
                  <Filter className="mr-2 h-4 w-4" />
                  <span>Filter</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <Save className="mr-2 h-4 w-4" />
                  <span>Save View</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  <span>Refresh</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {}}>
                  <Download className="mr-2 h-4 w-4" />
                  <span>Export</span>
                </DropdownMenuItem>
              </div>

              {/* Table management actions - only shown when table is selected */}
              {selectedTable && (
                <div className="border-t p-2">
                  <p className="text-muted-foreground mb-1 text-xs font-medium">
                    Table Actions
                  </p>
                  <DropdownMenuItem onClick={() => {}}>
                    <Edit2 className="mr-2 h-4 w-4" />
                    <span>Edit Structure</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {}}>
                    <Users className="mr-2 h-4 w-4" />
                    <span>Permissions</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {}}>
                    <FileText className="mr-2 h-4 w-4" />
                    <span>View Schema</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {}}
                    className="text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    <span>Delete Table</span>
                  </DropdownMenuItem>
                </div>
              )}

              {/* Database actions - only shown when database is selected but no table */}
              {selectedDatabase && !selectedTable && (
                <div className="border-t p-2">
                  <p className="text-muted-foreground mb-1 text-xs font-medium">
                    Database Actions
                  </p>
                  <DropdownMenuItem onClick={() => {}}>
                    <Plus className="mr-2 h-4 w-4" />
                    <span>New Table</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {}}>
                    <Lock className="mr-2 h-4 w-4" />
                    <span>Database Security</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {}}>
                    <Layers className="mr-2 h-4 w-4" />
                    <span>Backups</span>
                  </DropdownMenuItem>
                </div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Search button for smallest screens */}
          <Button variant="outline" size="sm" className="h-8 sm:hidden">
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === "data" && (
        <>
          {/* Data Table - Always show table structure */}
          <div className="rounded-md border">
            {/* Table Info (inside the table area) */}
            {selectedTable && (
              <div className="bg-muted/30 border-b px-2 py-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                      {selectedTable.name}
                    </span>
                    <span className="text-muted-foreground flex items-center text-sm font-medium">
                      {selectedTable.description || "No description"} •{" "}
                      {selectedTable.rowCount} rows
                    </span>
                  </div>
                </div>
              </div>
            )}
            <Table>
              <TableHeader>
                <TableRow>
                  {selectedTable ? (
                    // When a table is selected, show its columns
                    selectedTable.columns.map((column) => (
                      <TableHead key={column.name}>{column.name}</TableHead>
                    ))
                  ) : (
                    // When no table is selected, show placeholder columns
                    <>
                      <TableHead>Column 1</TableHead>
                      <TableHead>Column 2</TableHead>
                      <TableHead>Column 3</TableHead>
                      <TableHead>Column 4</TableHead>
                    </>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {selectedTable && paginatedData.length > 0 ? (
                  // Show actual data when table is selected and has data
                  paginatedData.map((row, rowIndex) => (
                    <TableRow
                      key={rowIndex}
                      className={
                        rowIndex % 2 === 0
                          ? ""
                          : "bg-slate-50 dark:bg-slate-800/50"
                      }
                    >
                      {selectedTable.columns.map((column) => (
                        <TableCell key={column.name}>
                          {formatCellValue(
                            row[column.name] as
                              | string
                              | number
                              | boolean
                              | null
                              | undefined,
                            column.type as ColumnType
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  // Empty state with placeholder message
                  <TableRow>
                    <TableCell
                      colSpan={selectedTable ? selectedTable.columns.length : 4}
                      className="h-32 text-center"
                    >
                      {!selectedTable ? (
                        <div className="flex flex-col items-center justify-center">
                          <p className="text-muted-foreground mb-2 text-sm">
                            {!selectedSource
                              ? "Select a database source"
                              : !selectedDatabase
                                ? "Select a database"
                                : "Select a table"}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            Choose from the dropdown menus above to view and
                            manage your data
                          </p>
                        </div>
                      ) : (
                        "No data found"
                      )}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination - Only show when table is selected */}
          {selectedTable && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-muted-foreground text-sm">
                Showing{" "}
                {paginatedData.length > 0
                  ? (currentPage - 1) * pageSize + 1
                  : 0}{" "}
                to {Math.min(currentPage * pageSize, filteredData.length)} of{" "}
                {filteredData.length} entries
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm">
                  Page {currentPage} of {totalPages || 1}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages || totalPages === 0}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Schema Tab Content */}
      {activeTab === "schema" && selectedTable && (
        <div className="rounded-md border">
          <div className="bg-muted grid grid-cols-4 rounded-t-md p-3">
            <div className="font-medium">Column</div>
            <div className="font-medium">Type</div>
            <div className="font-medium">Nullable</div>
            <div className="font-medium">Description</div>
          </div>
          <div className="divide-y">
            {selectedTable.columns.map((column) => (
              <div key={column.name} className="grid grid-cols-4 p-3">
                <div className="font-mono text-sm">{column.name}</div>
                <div className="text-sm">{column.type}</div>
                <div className="text-sm">{column.nullable ? "Yes" : "No"}</div>
                <div className="text-muted-foreground text-sm">
                  {column.description || "-"}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Query Tab Content */}
      {activeTab === "query" && selectedTable && (
        <div className="space-y-4">
          <div className="bg-muted h-40 overflow-auto rounded-md p-4 font-mono text-sm">
            {`SELECT * FROM ${selectedTable.name}
WHERE patient_id = :patient_id
LIMIT 100;`}
          </div>
          <div className="flex justify-end">
            <button className="bg-primary text-primary-foreground rounded-md px-4 py-2 text-sm font-medium">
              Run Query
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

type ColumnType =
  | "boolean"
  | "status"
  | "date"
  | "datetime"
  | "string"
  | "number"

function formatCellValue(
  value: string | number | boolean | null | undefined,
  type: ColumnType
) {
  if (value === null || value === undefined) {
    return <span className="text-muted-foreground">NULL</span>
  }

  if (type === "boolean") {
    return value ? "Yes" : "No"
  }

  if (type === "status") {
    // Ensure that `value` is a string before calling `toLowerCase()`
    const valueAsString = String(value).toLowerCase()
    const statusMap: Record<
      string,
      {
        label: string
        variant: "default" | "outline" | "secondary" | "destructive"
      }
    > = {
      active: { label: "Active", variant: "default" },
      inactive: { label: "Inactive", variant: "outline" },
      pending: { label: "Pending", variant: "secondary" },
      error: { label: "Error", variant: "destructive" },
    }

    const status = statusMap[valueAsString] || {
      label: value,
      variant: "outline",
    }

    return <Badge variant={status.variant}>{status.label}</Badge>
  }

  if (type === "date" && value) {
    // Ensure that `value` is a string or number that can be converted to a Date
    if (typeof value === "string" || typeof value === "number") {
      return new Date(value).toLocaleDateString()
    }
  }

  if (type === "datetime" && value) {
    // Ensure that `value` is a string or number that can be converted to a Date
    if (typeof value === "string" || typeof value === "number") {
      return new Date(value).toLocaleString()
    }
  }

  return String(value)
}
