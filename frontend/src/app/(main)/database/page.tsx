import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card"
import DataTable from "@/app/(main)/database/components/data-table"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Database = () => {
  return (
    <>
      <Breadcrumb links={[{ label: "Database" }]} />

      <div className="flex flex-col gap-1.5">
        <CardTitle>Database</CardTitle>
        <CardDescription>All-in-one database management</CardDescription>
      </div>

      <Card>
        <CardContent>
          <DataTable />
        </CardContent>
      </Card>
    </>
  )
}

export default Database
