export interface Column {
  name: string
  type: string
  nullable: boolean
  description?: string
}

export interface TableSchema {
  id: string
  name: string
  description?: string
  rowCount: number
  columns: Column[]
  data: Record<string, unknown>[]
}

export interface Database {
  id: string
  name: string
  description?: string
  tables: TableSchema[]
}

export interface DatabaseSource {
  id: string
  name: string
  type: "postgres" | "mysql" | "mongodb" | "oracle" | "sqlserver"
  description?: string
  databases: Database[]
}

export const mockDatabases: DatabaseSource[] = [
  {
    id: "postgres-prod",
    name: "PostgreSQL (Production)",
    type: "postgres",
    description: "Production PostgreSQL database",
    databases: [
      {
        id: "patient-db",
        name: "PatientDB",
        description: "Patient records and medical history",
        tables: [
          {
            id: "patients",
            name: "patients",
            description: "Patient demographic information",
            rowCount: 1254,
            columns: [
              {
                name: "patient_id",
                type: "uuid",
                nullable: false,
                description: "Unique patient identifier",
              },
              {
                name: "first_name",
                type: "text",
                nullable: false,
                description: "Pat<PERSON>'s first name",
              },
              {
                name: "last_name",
                type: "text",
                nullable: false,
                description: "<PERSON><PERSON>'s last name",
              },
              {
                name: "date_of_birth",
                type: "date",
                nullable: false,
                description: "Patient's date of birth",
              },
              {
                name: "gender",
                type: "text",
                nullable: false,
                description: "Patient's gender",
              },
              {
                name: "email",
                type: "text",
                nullable: true,
                description: "Patient's email address",
              },
              {
                name: "phone",
                type: "text",
                nullable: true,
                description: "Patient's phone number",
              },
              {
                name: "address",
                type: "text",
                nullable: true,
                description: "Patient's address",
              },
              {
                name: "status",
                type: "status",
                nullable: false,
                description: "Patient's status",
              },
              {
                name: "created_at",
                type: "datetime",
                nullable: false,
                description: "Record creation timestamp",
              },
              {
                name: "updated_at",
                type: "datetime",
                nullable: false,
                description: "Record update timestamp",
              },
            ],
            data: [
              {
                patient_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                first_name: "Wei Jie",
                last_name: "Tan",
                date_of_birth: "1980-05-15",
                gender: "Male",
                email: "<EMAIL>",
                phone: "+65 9123 4567",
                address: "Blk 123 Ang Mo Kio Ave 3, #05-67, Singapore 560123",
                status: "active",
                created_at: "2023-01-15T08:30:00Z",
                updated_at: "2023-01-15T08:30:00Z",
              },
              {
                patient_id: "a47ac10b-58cc-4372-a567-0e02b2c3d123",
                first_name: "Siti",
                last_name: "Rahim",
                date_of_birth: "1992-08-21",
                gender: "Female",
                email: "<EMAIL>",
                phone: "+65 9876 5432",
                address: "Blk 456 Bedok North St 3, #12-34, Singapore 460456",
                status: "active",
                created_at: "2023-02-10T14:15:00Z",
                updated_at: "2023-02-10T14:15:00Z",
              },
              {
                patient_id: "b47ac10b-58cc-4372-a567-0e02b2c3d789",
                first_name: "Arun",
                last_name: "Nair",
                date_of_birth: "1975-11-30",
                gender: "Male",
                email: "<EMAIL>",
                phone: "+65 8123 9876",
                address: "Blk 789 Jurong West St 41, #03-21, Singapore 640789",
                status: "inactive",
                created_at: "2023-01-20T10:45:00Z",
                updated_at: "2023-03-15T16:30:00Z",
              },
              {
                patient_id: "c47ac10b-58cc-4372-a567-0e02b2c3d456",
                first_name: "Mei Ling",
                last_name: "Lim",
                date_of_birth: "1988-04-12",
                gender: "Female",
                email: "<EMAIL>",
                phone: "+65 9654 3210",
                address: "Blk 321 Clementi Ave 2, #08-45, Singapore 120321",
                status: "active",
                created_at: "2023-02-05T09:20:00Z",
                updated_at: "2023-02-05T09:20:00Z",
              },
              {
                patient_id: "d47ac10b-58cc-4372-a567-0e02b2c3d789",
                first_name: "Devan",
                last_name: "Pillai",
                date_of_birth: "1965-07-08",
                gender: "Male",
                email: "<EMAIL>",
                phone: "+65 9001 2345",
                address: "Blk 654 Toa Payoh Lor 5, #10-11, Singapore 310654",
                status: "pending",
                created_at: "2023-03-01T11:10:00Z",
                updated_at: "2023-03-01T11:10:00Z",
              },
            ],
          },
          {
            id: "medical_records",
            name: "medical_records",
            description: "Patient medical records",
            rowCount: 3542,
            columns: [
              {
                name: "record_id",
                type: "uuid",
                nullable: false,
                description: "Unique record identifier",
              },
              {
                name: "patient_id",
                type: "uuid",
                nullable: false,
                description: "Reference to patient",
              },
              {
                name: "visit_date",
                type: "date",
                nullable: false,
                description: "Date of visit",
              },
              {
                name: "diagnosis",
                type: "text",
                nullable: true,
                description: "Diagnosis information",
              },
              {
                name: "treatment",
                type: "text",
                nullable: true,
                description: "Treatment information",
              },
              {
                name: "notes",
                type: "text",
                nullable: true,
                description: "Additional notes",
              },
              {
                name: "doctor_id",
                type: "uuid",
                nullable: false,
                description: "Reference to doctor",
              },
              {
                name: "created_at",
                type: "datetime",
                nullable: false,
                description: "Record creation timestamp",
              },
              {
                name: "updated_at",
                type: "datetime",
                nullable: false,
                description: "Record update timestamp",
              },
            ],
            data: [
              {
                record_id: "e47ac10b-58cc-4372-a567-0e02b2c3d123",
                patient_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                visit_date: "2023-03-10",
                diagnosis: "Common cold",
                treatment: "Rest and fluids",
                notes: "Patient advised to return if symptoms worsen",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d111",
                created_at: "2023-03-10T09:30:00Z",
                updated_at: "2023-03-10T09:30:00Z",
              },
              {
                record_id: "f47ac10b-58cc-4372-a567-0e02b2c3d456",
                patient_id: "a47ac10b-58cc-4372-a567-0e02b2c3d123",
                visit_date: "2023-02-15",
                diagnosis: "Hypertension",
                treatment: "Prescribed lisinopril 10mg daily",
                notes: "Follow-up in 3 months",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d222",
                created_at: "2023-02-15T14:45:00Z",
                updated_at: "2023-02-15T14:45:00Z",
              },
              {
                record_id: "g47ac10b-58cc-4372-a567-0e02b2c3d789",
                patient_id: "b47ac10b-58cc-4372-a567-0e02b2c3d789",
                visit_date: "2023-01-25",
                diagnosis: "Type 2 diabetes",
                treatment: "Diet modification and metformin 500mg twice daily",
                notes: "Referred to nutritionist",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d333",
                created_at: "2023-01-25T11:15:00Z",
                updated_at: "2023-01-25T11:15:00Z",
              },
              {
                record_id: "h47ac10b-58cc-4372-a567-0e02b2c3d123",
                patient_id: "c47ac10b-58cc-4372-a567-0e02b2c3d456",
                visit_date: "2023-03-05",
                diagnosis: "Seasonal allergies",
                treatment: "Prescribed cetirizine 10mg daily",
                notes:
                  "Advised to avoid outdoor activities during high pollen count",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d444",
                created_at: "2023-03-05T10:30:00Z",
                updated_at: "2023-03-05T10:30:00Z",
              },
              {
                record_id: "i47ac10b-58cc-4372-a567-0e02b2c3d456",
                patient_id: "d47ac10b-58cc-4372-a567-0e02b2c3d789",
                visit_date: "2023-02-28",
                diagnosis: "Lower back pain",
                treatment: "Physical therapy and ibuprofen as needed",
                notes: "Recommended ergonomic assessment at workplace",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d555",
                created_at: "2023-02-28T15:20:00Z",
                updated_at: "2023-02-28T15:20:00Z",
              },
            ],
          },
          {
            id: "prescriptions",
            name: "prescriptions",
            description: "Medication prescriptions",
            rowCount: 2876,
            columns: [
              {
                name: "prescription_id",
                type: "uuid",
                nullable: false,
                description: "Unique prescription identifier",
              },
              {
                name: "patient_id",
                type: "uuid",
                nullable: false,
                description: "Reference to patient",
              },
              {
                name: "medication",
                type: "text",
                nullable: false,
                description: "Medication name",
              },
              {
                name: "dosage",
                type: "text",
                nullable: false,
                description: "Dosage information",
              },
              {
                name: "frequency",
                type: "text",
                nullable: false,
                description: "Frequency of use",
              },
              {
                name: "start_date",
                type: "date",
                nullable: false,
                description: "Start date",
              },
              {
                name: "end_date",
                type: "date",
                nullable: true,
                description: "End date",
              },
              {
                name: "doctor_id",
                type: "uuid",
                nullable: false,
                description: "Reference to doctor",
              },
              {
                name: "status",
                type: "status",
                nullable: false,
                description: "Prescription status",
              },
              {
                name: "created_at",
                type: "datetime",
                nullable: false,
                description: "Record creation timestamp",
              },
              {
                name: "updated_at",
                type: "datetime",
                nullable: false,
                description: "Record update timestamp",
              },
            ],
            data: [
              {
                prescription_id: "j47ac10b-58cc-4372-a567-0e02b2c3d123",
                patient_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                medication: "Amoxicillin",
                dosage: "500mg",
                frequency: "3 times daily",
                start_date: "2023-03-10",
                end_date: "2023-03-17",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d111",
                status: "active",
                created_at: "2023-03-10T09:35:00Z",
                updated_at: "2023-03-10T09:35:00Z",
              },
              {
                prescription_id: "k47ac10b-58cc-4372-a567-0e02b2c3d456",
                patient_id: "a47ac10b-58cc-4372-a567-0e02b2c3d123",
                medication: "Lisinopril",
                dosage: "10mg",
                frequency: "Once daily",
                start_date: "2023-02-15",
                end_date: null,
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d222",
                status: "active",
                created_at: "2023-02-15T14:50:00Z",
                updated_at: "2023-02-15T14:50:00Z",
              },
              {
                prescription_id: "l47ac10b-58cc-4372-a567-0e02b2c3d789",
                patient_id: "b47ac10b-58cc-4372-a567-0e02b2c3d789",
                medication: "Metformin",
                dosage: "500mg",
                frequency: "Twice daily",
                start_date: "2023-01-25",
                end_date: null,
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d333",
                status: "active",
                created_at: "2023-01-25T11:20:00Z",
                updated_at: "2023-01-25T11:20:00Z",
              },
              {
                prescription_id: "m47ac10b-58cc-4372-a567-0e02b2c3d123",
                patient_id: "c47ac10b-58cc-4372-a567-0e02b2c3d456",
                medication: "Cetirizine",
                dosage: "10mg",
                frequency: "Once daily",
                start_date: "2023-03-05",
                end_date: "2023-04-05",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d444",
                status: "active",
                created_at: "2023-03-05T10:35:00Z",
                updated_at: "2023-03-05T10:35:00Z",
              },
              {
                prescription_id: "n47ac10b-58cc-4372-a567-0e02b2c3d456",
                patient_id: "d47ac10b-58cc-4372-a567-0e02b2c3d789",
                medication: "Ibuprofen",
                dosage: "400mg",
                frequency: "As needed for pain",
                start_date: "2023-02-28",
                end_date: "2023-03-14",
                doctor_id: "d47ac10b-58cc-4372-a567-0e02b2c3d555",
                status: "inactive",
                created_at: "2023-02-28T15:25:00Z",
                updated_at: "2023-03-14T09:10:00Z",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "mysql-analytics",
    name: "MySQL (Analytics)",
    type: "mysql",
    description: "Analytics database for reporting",
    databases: [
      {
        id: "health-analytics",
        name: "HealthAnalytics",
        description: "Healthcare analytics and reporting",
        tables: [
          {
            id: "patient_visits",
            name: "patient_visits",
            description: "Aggregated patient visit data",
            rowCount: 876,
            columns: [
              {
                name: "month",
                type: "date",
                nullable: false,
                description: "Month of visits",
              },
              {
                name: "department",
                type: "text",
                nullable: false,
                description: "Medical department",
              },
              {
                name: "visit_count",
                type: "number",
                nullable: false,
                description: "Number of visits",
              },
              {
                name: "avg_visit_duration",
                type: "number",
                nullable: false,
                description: "Average visit duration in minutes",
              },
              {
                name: "new_patients",
                type: "number",
                nullable: false,
                description: "Number of new patients",
              },
              {
                name: "returning_patients",
                type: "number",
                nullable: false,
                description: "Number of returning patients",
              },
            ],
            data: [
              {
                month: "2023-01-01",
                department: "Cardiology (National Heart Centre Singapore)",
                visit_count: 245,
                avg_visit_duration: 32.5,
                new_patients: 87,
                returning_patients: 158,
              },
              {
                month: "2023-01-01",
                department: "Neurology (Tan Tock Seng Hospital)",
                visit_count: 187,
                avg_visit_duration: 45.2,
                new_patients: 62,
                returning_patients: 125,
              },
              {
                month: "2023-01-01",
                department: "Paediatrics (KK Women's and Children's Hospital)",
                visit_count: 312,
                avg_visit_duration: 28.7,
                new_patients: 134,
                returning_patients: 178,
              },
              {
                month: "2023-02-01",
                department: "Cardiology (National Heart Centre Singapore)",
                visit_count: 263,
                avg_visit_duration: 30.8,
                new_patients: 92,
                returning_patients: 171,
              },
              {
                month: "2023-02-01",
                department: "Neurology (Tan Tock Seng Hospital)",
                visit_count: 195,
                avg_visit_duration: 43.1,
                new_patients: 58,
                returning_patients: 137,
              },
            ],
          },
          {
            id: "medication_usage",
            name: "medication_usage",
            description: "Medication usage statistics",
            rowCount: 542,
            columns: [
              {
                name: "month",
                type: "date",
                nullable: false,
                description: "Month of data",
              },
              {
                name: "medication_category",
                type: "text",
                nullable: false,
                description: "Category of medication",
              },
              {
                name: "prescription_count",
                type: "number",
                nullable: false,
                description: "Number of prescriptions",
              },
              {
                name: "avg_duration_days",
                type: "number",
                nullable: false,
                description: "Average prescription duration in days",
              },
              {
                name: "total_cost",
                type: "number",
                nullable: false,
                description: "Total cost of prescriptions",
              },
            ],
            data: [
              {
                month: "2023-01-01",
                medication_category: "Antibiotics",
                prescription_count: 423,
                avg_duration_days: 7.5,
                total_cost: 12450.75,
              },
              {
                month: "2023-01-01",
                medication_category: "Antihypertensives",
                prescription_count: 587,
                avg_duration_days: 30.0,
                total_cost: 18932.5,
              },
              {
                month: "2023-01-01",
                medication_category: "Analgesics",
                prescription_count: 645,
                avg_duration_days: 14.2,
                total_cost: 8765.25,
              },
              {
                month: "2023-02-01",
                medication_category: "Antibiotics",
                prescription_count: 398,
                avg_duration_days: 7.8,
                total_cost: 11876.5,
              },
              {
                month: "2023-02-01",
                medication_category: "Antihypertensives",
                prescription_count: 602,
                avg_duration_days: 30.0,
                total_cost: 19456.75,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "mongodb-research",
    name: "MongoDB (Research)",
    type: "mongodb",
    description: "Research data storage",
    databases: [
      {
        id: "clinical-trials",
        name: "ClinicalTrials",
        description: "Clinical trials data",
        tables: [
          {
            id: "trials",
            name: "trials",
            description: "Clinical trial information",
            rowCount: 124,
            columns: [
              {
                name: "trial_id",
                type: "text",
                nullable: false,
                description: "Trial identifier",
              },
              {
                name: "title",
                type: "text",
                nullable: false,
                description: "Trial title",
              },
              {
                name: "status",
                type: "status",
                nullable: false,
                description: "Trial status",
              },
              {
                name: "start_date",
                type: "date",
                nullable: false,
                description: "Start date",
              },
              {
                name: "end_date",
                type: "date",
                nullable: true,
                description: "End date",
              },
              {
                name: "participants",
                type: "number",
                nullable: false,
                description: "Number of participants",
              },
              {
                name: "description",
                type: "text",
                nullable: false,
                description: "Trial description",
              },
              {
                name: "lead_researcher",
                type: "text",
                nullable: false,
                description: "Lead researcher",
              },
            ],
            data: [
              {
                trial_id: "CT-2023-001",
                title: "Efficacy of Novel Antihypertensive Treatment",
                status: "active",
                start_date: "2023-01-15",
                end_date: null,
                participants: 250,
                description:
                  "Randomized controlled trial evaluating the efficacy of a novel antihypertensive medication",
                lead_researcher: "Dr. Tan Wei Ming",
              },
              {
                trial_id: "CT-2023-002",
                title: "Diabetes Management App Effectiveness",
                status: "pending",
                start_date: "2023-04-01",
                end_date: null,
                participants: 500,
                description:
                  "Study to evaluate the effectiveness of a mobile app for diabetes management",
                lead_researcher: "Dr. Siti Khalid",
              },
              {
                trial_id: "CT-2022-015",
                title: "Cognitive Behavioral Therapy for Chronic Pain",
                status: "completed",
                start_date: "2022-06-10",
                end_date: "2023-02-28",
                participants: 180,
                description:
                  "Evaluation of cognitive behavioral therapy techniques for chronic pain management",
                lead_researcher: "Dr. Arun Nair",
              },
              {
                trial_id: "CT-2023-003",
                title: "Remote Patient Monitoring for Heart Failure",
                status: "active",
                start_date: "2023-02-15",
                end_date: null,
                participants: 320,
                description:
                  "Study of remote monitoring technologies for heart failure patients",
                lead_researcher: "Dr. Lim Mei Ling",
              },
              {
                trial_id: "CT-2022-018",
                title: "Nutritional Intervention for Type 2 Diabetes",
                status: "completed",
                start_date: "2022-08-01",
                end_date: "2023-03-15",
                participants: 210,
                description:
                  "Evaluation of specialized nutritional intervention for type 2 diabetes patients",
                lead_researcher: "Dr. Devan Pillai",
              },
            ],
          },
        ],
      },
    ],
  },
]
