"use client"

import React from "react"
import {
  AlertCircle,
  Calendar,
  CheckCircle2,
  Download,
  Filter,
  RefreshCw,
  Search,
  XCircle,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for data quality issues
const dataQualityIssues = [
  {
    id: 1,
    source: "Epic EHR",
    table: "patients",
    field: "date_of_birth",
    issueType: "Invalid Format",
    severity: "High",
    recordsAffected: 128,
    percentage: 0.8,
    detectedAt: "2024-05-15 08:23:15",
    status: "Open",
  },
  {
    id: 2,
    source: "<PERSON>rner EHR",
    table: "appointments",
    field: "provider_id",
    issueType: "Missing Value",
    severity: "Medium",
    recordsAffected: 215,
    percentage: 1.2,
    detectedAt: "2024-05-14 14:45:32",
    status: "In Progress",
  },
  {
    id: 3,
    source: "LabCorp",
    table: "lab_results",
    field: "result_value",
    issueType: "Out of Range",
    severity: "Low",
    recordsAffected: 42,
    percentage: 0.3,
    detectedAt: "2024-05-14 10:12:08",
    status: "Resolved",
  },
  {
    id: 4,
    source: "Epic EHR",
    table: "medications",
    field: "dosage",
    issueType: "Inconsistent Units",
    severity: "Medium",
    recordsAffected: 187,
    percentage: 1.5,
    detectedAt: "2024-05-13 16:38:45",
    status: "Open",
  },
  {
    id: 5,
    source: "Allscripts",
    table: "diagnoses",
    field: "icd_code",
    issueType: "Invalid Code",
    severity: "High",
    recordsAffected: 76,
    percentage: 0.6,
    detectedAt: "2024-05-13 09:22:17",
    status: "In Progress",
  },
]

// Mock data for data sources quality
const dataSourcesQuality = [
  {
    source: "Epic EHR",
    completeness: 98.2,
    accuracy: 97.5,
    consistency: 96.8,
    timeliness: 99.1,
    overall: 97.9,
    trend: "+0.3%",
  },
  {
    source: "Cerner EHR",
    completeness: 97.8,
    accuracy: 96.9,
    consistency: 95.4,
    timeliness: 98.7,
    overall: 97.2,
    trend: "+0.5%",
  },
  {
    source: "Allscripts",
    completeness: 95.3,
    accuracy: 94.8,
    consistency: 93.2,
    timeliness: 97.5,
    overall: 95.2,
    trend: "-0.2%",
  },
  {
    source: "LabCorp",
    completeness: 99.1,
    accuracy: 98.7,
    consistency: 97.9,
    timeliness: 99.5,
    overall: 98.8,
    trend: "+0.1%",
  },
  {
    source: "NextGen Healthcare",
    completeness: 94.2,
    accuracy: 93.5,
    consistency: 92.8,
    timeliness: 96.3,
    overall: 94.2,
    trend: "-0.4%",
  },
]

// Mock data for field-level quality
const fieldLevelQuality = [
  {
    field: "Patient Name",
    tables: ["patients", "appointments", "encounters"],
    completeness: 99.8,
    accuracy: 99.2,
    consistency: 98.7,
    issues: 42,
  },
  {
    field: "Date of Birth",
    tables: ["patients"],
    completeness: 99.5,
    accuracy: 97.8,
    consistency: 98.2,
    issues: 128,
  },
  {
    field: "Provider ID",
    tables: ["appointments", "encounters", "orders"],
    completeness: 98.2,
    accuracy: 99.5,
    consistency: 97.4,
    issues: 215,
  },
  {
    field: "Diagnosis Code",
    tables: ["diagnoses", "encounters"],
    completeness: 97.5,
    accuracy: 96.8,
    consistency: 95.9,
    issues: 76,
  },
  {
    field: "Medication Dosage",
    tables: ["medications", "orders"],
    completeness: 96.8,
    accuracy: 95.2,
    consistency: 94.7,
    issues: 187,
  },
]

const DataQuality = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Database", href: "/database" },
        { label: "Data Quality Logs" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Data Quality Logs</CardTitle>
      <CardDescription>All data quality logs in one place</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 7 Days
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Overall Data Quality
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">96.8%</div>
          <p className="text-muted-foreground text-xs">+0.3% from last week</p>
          <Progress value={96.8} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Open Issues</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">42</div>
          <p className="text-muted-foreground text-xs">-8 from last week</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Records Affected
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">648</div>
          <p className="text-muted-foreground text-xs">0.9% of total records</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            High Severity Issues
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">12</div>
          <p className="text-muted-foreground text-xs">-3 from last week</p>
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="issues" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="issues">Quality Issues</TabsTrigger>
        <TabsTrigger value="sources">Data Sources</TabsTrigger>
        <TabsTrigger value="fields">Field-Level Quality</TabsTrigger>
      </TabsList>
      <TabsContent value="issues" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Data Quality Issues</CardTitle>
            <CardDescription>
              Recent data quality issues detected across all data sources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Source</th>
                    <th className="p-2 text-left font-medium">Table</th>
                    <th className="p-2 text-left font-medium">Field</th>
                    <th className="p-2 text-left font-medium">Issue Type</th>
                    <th className="p-2 text-center font-medium">Severity</th>
                    <th className="p-2 text-right font-medium">Records</th>
                    <th className="p-2 text-right font-medium">%</th>
                    <th className="p-2 text-left font-medium">Detected</th>
                    <th className="p-2 text-center font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {dataQualityIssues.map((issue) => (
                    <tr key={issue.id} className="border-b">
                      <td className="p-2">{issue.source}</td>
                      <td className="p-2">{issue.table}</td>
                      <td className="p-2 font-medium">{issue.field}</td>
                      <td className="p-2">{issue.issueType}</td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            issue.severity === "High"
                              ? "bg-red-100 text-red-700"
                              : issue.severity === "Medium"
                                ? "bg-amber-100 text-amber-700"
                                : "bg-blue-100 text-blue-700"
                          }`}
                        >
                          {issue.severity}
                        </span>
                      </td>
                      <td className="p-2 text-right">
                        {issue.recordsAffected}
                      </td>
                      <td className="p-2 text-right">{issue.percentage}%</td>
                      <td className="p-2">{issue.detectedAt}</td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs ${
                            issue.status === "Open"
                              ? "bg-red-100 text-red-700"
                              : issue.status === "In Progress"
                                ? "bg-amber-100 text-amber-700"
                                : "bg-green-100 text-green-700"
                          }`}
                        >
                          {issue.status === "Open" ? (
                            <XCircle className="h-3 w-3" />
                          ) : issue.status === "In Progress" ? (
                            <AlertCircle className="h-3 w-3" />
                          ) : (
                            <CheckCircle2 className="h-3 w-3" />
                          )}
                          {issue.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-muted-foreground text-sm">
              Showing 5 of 42 issues
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="sources" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Data Source Quality</CardTitle>
            <CardDescription>
              Quality metrics for each data source
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Data Source</th>
                    <th className="p-2 text-center font-medium">
                      Completeness
                    </th>
                    <th className="p-2 text-center font-medium">Accuracy</th>
                    <th className="p-2 text-center font-medium">Consistency</th>
                    <th className="p-2 text-center font-medium">Timeliness</th>
                    <th className="p-2 text-center font-medium">Overall</th>
                    <th className="p-2 text-right font-medium">Trend</th>
                  </tr>
                </thead>
                <tbody>
                  {dataSourcesQuality.map((source, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{source.source}</td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{source.completeness}%</span>
                          <Progress
                            value={source.completeness}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{source.accuracy}%</span>
                          <Progress
                            value={source.accuracy}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{source.consistency}%</span>
                          <Progress
                            value={source.consistency}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{source.timeliness}%</span>
                          <Progress
                            value={source.timeliness}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span className="font-medium">{source.overall}%</span>
                          <Progress
                            value={source.overall}
                            className={`h-1 w-16 ${
                              source.overall < 95
                                ? "bg-amber-100"
                                : source.overall < 97
                                  ? "bg-blue-100"
                                  : "bg-green-100"
                            }`}
                          />
                        </div>
                      </td>
                      <td
                        className={`p-2 text-right ${source.trend.startsWith("+") ? "text-green-500" : "text-red-500"}`}
                      >
                        {source.trend}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Quality Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle2 className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      LabCorp maintains the highest overall data quality (98.8%)
                      with excellent timeliness (99.5%).
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                    <span>
                      NextGen Healthcare shows declining quality trends (-0.4%)
                      and needs attention, particularly in consistency metrics.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle2 className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Epic EHR and Cerner EHR both show positive quality trends,
                      with improvements in completeness metrics.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Recommendations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Investigate consistency issues in NextGen Healthcare data,
                      focusing on field-level validation rules.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Implement additional data quality checks for Allscripts to
                      address the slight decline in quality metrics.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertCircle className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Document and share {`LabCorp's`} data quality practices as
                      a benchmark for other data sources.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="fields" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Field-Level Quality</CardTitle>
            <CardDescription>
              Quality metrics for critical data fields across tables
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <div className="absolute top-0 left-0 w-full">
                <div className="mb-4 flex items-center gap-2">
                  <div className="relative max-w-sm flex-1">
                    <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                    <input
                      type="search"
                      placeholder="Search fields..."
                      className="border-input bg-background focus-visible:ring-ring h-9 w-full rounded-md border px-3 py-1 pl-8 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none"
                    />
                  </div>
                </div>
              </div>
              <div className="pt-14">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="p-2 text-left font-medium">Field</th>
                        <th className="p-2 text-left font-medium">Tables</th>
                        <th className="p-2 text-center font-medium">
                          Completeness
                        </th>
                        <th className="p-2 text-center font-medium">
                          Accuracy
                        </th>
                        <th className="p-2 text-center font-medium">
                          Consistency
                        </th>
                        <th className="p-2 text-right font-medium">Issues</th>
                      </tr>
                    </thead>
                    <tbody>
                      {fieldLevelQuality.map((field, index) => (
                        <tr key={index} className="border-b">
                          <td className="p-2 font-medium">{field.field}</td>
                          <td className="p-2">
                            <div className="flex flex-wrap gap-1">
                              {field.tables.map((table, i) => (
                                <span
                                  key={i}
                                  className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs"
                                >
                                  {table}
                                </span>
                              ))}
                            </div>
                          </td>
                          <td className="p-2 text-center">
                            <div className="flex flex-col items-center">
                              <span>{field.completeness}%</span>
                              <Progress
                                value={field.completeness}
                                className="h-1 w-16"
                              />
                            </div>
                          </td>
                          <td className="p-2 text-center">
                            <div className="flex flex-col items-center">
                              <span>{field.accuracy}%</span>
                              <Progress
                                value={field.accuracy}
                                className="h-1 w-16"
                              />
                            </div>
                          </td>
                          <td className="p-2 text-center">
                            <div className="flex flex-col items-center">
                              <span>{field.consistency}%</span>
                              <Progress
                                value={field.consistency}
                                className="h-1 w-16"
                              />
                            </div>
                          </td>
                          <td className="p-2 text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 px-2"
                            >
                              {field.issues} issues
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="bg-primary/10 mt-6 rounded-md p-4">
              <h3 className="mb-2 text-sm font-medium">
                Field Quality Analysis
              </h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Analysis of field-level quality metrics reveals several patterns
                and opportunities for improvement:
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="mt-0.5 h-4 w-4 text-green-500" />
                  <span>
                    Patient demographic fields (Name, Date of Birth) maintain
                    high completeness ({">"}99%) but Date of Birth shows
                    accuracy issues that should be addressed.
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                  <span>
                    Clinical fields like Medication Dosage and Diagnosis Code
                    show lower consistency scores, suggesting potential
                    standardization issues across systems.
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="mt-0.5 h-4 w-4 text-amber-500" />
                  <span>
                    Provider ID field has a significant number of issues (215)
                    despite good completeness, indicating potential reference
                    data problems.
                  </span>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default DataQuality
