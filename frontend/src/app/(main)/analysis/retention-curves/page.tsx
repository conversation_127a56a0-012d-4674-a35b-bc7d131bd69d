"use client"

import React from "react"
import {
  Calendar,
  Download,
  <PERSON>lter,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>hare2,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for overall retention
const overallRetention = [
  { period: "Week 1", retention: 100 },
  { period: "Week 2", retention: 82 },
  { period: "Week 4", retention: 68 },
  { period: "Week 8", retention: 54 },
  { period: "Week 12", retention: 47 },
  { period: "Week 24", retention: 38 },
  { period: "Week 36", retention: 32 },
  { period: "Week 48", retention: 28 },
]

// Mock data for retention by department
const retention<PERSON><PERSON>Department = [
  {
    department: "Cardiology",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 88 },
      { period: "Week 4", retention: 76 },
      { period: "Week 8", retention: 65 },
      { period: "Week 12", retention: 58 },
      { period: "Week 24", retention: 48 },
      { period: "Week 36", retention: 42 },
      { period: "Week 48", retention: 38 },
    ],
  },
  {
    department: "Orthopedics",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 85 },
      { period: "Week 4", retention: 72 },
      { period: "Week 8", retention: 58 },
      { period: "Week 12", retention: 50 },
      { period: "Week 24", retention: 42 },
      { period: "Week 36", retention: 35 },
      { period: "Week 48", retention: 30 },
    ],
  },
  {
    department: "Neurology",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 80 },
      { period: "Week 4", retention: 65 },
      { period: "Week 8", retention: 52 },
      { period: "Week 12", retention: 45 },
      { period: "Week 24", retention: 36 },
      { period: "Week 36", retention: 30 },
      { period: "Week 48", retention: 26 },
    ],
  },
  {
    department: "Pediatrics",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 90 },
      { period: "Week 4", retention: 82 },
      { period: "Week 8", retention: 72 },
      { period: "Week 12", retention: 65 },
      { period: "Week 24", retention: 55 },
      { period: "Week 36", retention: 48 },
      { period: "Week 48", retention: 42 },
    ],
  },
  {
    department: "Oncology",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 92 },
      { period: "Week 4", retention: 85 },
      { period: "Week 8", retention: 78 },
      { period: "Week 12", retention: 72 },
      { period: "Week 24", retention: 65 },
      { period: "Week 36", retention: 60 },
      { period: "Week 48", retention: 55 },
    ],
  },
]

// Mock data for retention by patient segment
const retentionBySegment = [
  {
    segment: "New Patients",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 75 },
      { period: "Week 4", retention: 58 },
      { period: "Week 8", retention: 42 },
      { period: "Week 12", retention: 35 },
      { period: "Week 24", retention: 28 },
      { period: "Week 36", retention: 22 },
      { period: "Week 48", retention: 18 },
    ],
  },
  {
    segment: "Returning Patients",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 88 },
      { period: "Week 4", retention: 78 },
      { period: "Week 8", retention: 68 },
      { period: "Week 12", retention: 62 },
      { period: "Week 24", retention: 52 },
      { period: "Week 36", retention: 45 },
      { period: "Week 48", retention: 40 },
    ],
  },
  {
    segment: "Chronic Condition",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 95 },
      { period: "Week 4", retention: 90 },
      { period: "Week 8", retention: 85 },
      { period: "Week 12", retention: 80 },
      { period: "Week 24", retention: 72 },
      { period: "Week 36", retention: 68 },
      { period: "Week 48", retention: 65 },
    ],
  },
  {
    segment: "Elderly (65+)",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 90 },
      { period: "Week 4", retention: 82 },
      { period: "Week 8", retention: 75 },
      { period: "Week 12", retention: 70 },
      { period: "Week 24", retention: 62 },
      { period: "Week 36", retention: 58 },
      { period: "Week 48", retention: 52 },
    ],
  },
  {
    segment: "Young Adults (18-35)",
    retention: [
      { period: "Week 1", retention: 100 },
      { period: "Week 2", retention: 72 },
      { period: "Week 4", retention: 55 },
      { period: "Week 8", retention: 38 },
      { period: "Week 12", retention: 30 },
      { period: "Week 24", retention: 22 },
      { period: "Week 36", retention: 18 },
      { period: "Week 48", retention: 15 },
    ],
  },
]

// Mock data for retention insights
const retentionInsights = [
  {
    title: "Oncology has highest long-term retention",
    description:
      "Oncology department maintains 55% retention at 48 weeks, significantly higher than the overall average of 28%.",
    recommendation:
      "Study the patient follow-up protocols in Oncology and consider applying similar approaches to other departments.",
    impact: "High",
  },
  {
    title: "Young Adults show steep initial drop-off",
    description:
      "Young Adults (18-35) segment shows a 28% drop in the first 2 weeks, compared to only 10% for Elderly patients.",
    recommendation:
      "Implement targeted engagement strategies for younger patients within the first 2 weeks after initial visit.",
    impact: "Medium",
  },
  {
    title: "Chronic condition patients have strong retention",
    description:
      "Patients with chronic conditions maintain 65% retention at 48 weeks, showing the effectiveness of continuous care programs.",
    recommendation:
      "Expand chronic care management programs and apply learnings to other patient segments.",
    impact: "High",
  },
  {
    title: "Critical drop-off period identified",
    description:
      "Across all segments, weeks 4-8 show the steepest decline in retention (average 14% drop).",
    recommendation:
      "Focus intervention efforts during this critical period with proactive outreach and follow-up appointments.",
    impact: "Medium",
  },
]

const RetentionCurves = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Analysis", href: "/analysis" },
        { label: "Retention Curves" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Retention Curves</CardTitle>
      <CardDescription>
        Analyze and optimize your patient retention curves
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 12 Months
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" size="sm">
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Overall Retention Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">28%</div>
          <p className="text-muted-foreground text-xs">at 48 weeks</p>
          <Progress value={28} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Best Performing Department
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">Oncology</div>
          <p className="text-muted-foreground text-xs">
            55% retention at 48 weeks
          </p>
          <Progress value={55} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Best Patient Segment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">Chronic Condition</div>
          <p className="text-muted-foreground text-xs">
            65% retention at 48 weeks
          </p>
          <Progress value={65} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Critical Drop-off Period
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">Weeks 4-8</div>
          <p className="text-muted-foreground text-xs">14% average decline</p>
        </CardContent>
      </Card>
    </div>

    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Overall Patient Retention</CardTitle>
            <CardDescription>
              Percentage of patients returning over time
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <LineChart className="mr-2 h-4 w-4" />
              View Trends
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="bg-primary/5 flex h-[300px] w-full items-center justify-center rounded-md">
          <div className="text-center">
            <p className="text-muted-foreground mb-2 text-sm">
              Retention Curve Visualization
            </p>
            <div className="flex h-[200px] items-end justify-center gap-4 px-8">
              {overallRetention.map((point, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className="bg-primary w-12 rounded-t-sm"
                    style={{ height: `${point.retention * 1.8}px` }}
                  ></div>
                  <span className="mt-2 text-xs">{point.period}</span>
                  <span className="text-xs font-medium">
                    {point.retention}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <Tabs defaultValue="by-department" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="by-department">By Department</TabsTrigger>
        <TabsTrigger value="by-segment">By Patient Segment</TabsTrigger>
        <TabsTrigger value="insights">Retention Insights</TabsTrigger>
      </TabsList>
      <TabsContent value="by-department" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Retention by Department</CardTitle>
            <CardDescription>
              Comparing retention rates across different departments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Department</th>
                    {overallRetention.map((period, index) => (
                      <th key={index} className="p-2 text-center font-medium">
                        {period.period}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {retentionByDepartment.map((dept, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{dept.department}</td>
                      {dept.retention.map((point, pointIndex) => (
                        <td key={pointIndex} className="p-2 text-center">
                          <span
                            className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                              point.retention > 50
                                ? "bg-green-100 text-green-700"
                                : point.retention > 30
                                  ? "bg-blue-100 text-blue-700"
                                  : "bg-amber-100 text-amber-700"
                            }`}
                          >
                            {point.retention}%
                          </span>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter>
            <div className="w-full space-y-2">
              <h3 className="text-sm font-medium">
                Department Retention Summary
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Best Performing
                    </span>
                    <span className="font-medium">Oncology</span>
                  </div>
                  <Progress value={55} max={100} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>55% retention at 48 weeks</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Needs Improvement
                    </span>
                    <span className="font-medium">Neurology</span>
                  </div>
                  <Progress value={26} max={100} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>26% retention at 48 weeks</span>
                  </div>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="by-segment" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Retention by Patient Segment</CardTitle>
            <CardDescription>
              Comparing retention rates across different patient segments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">
                      Patient Segment
                    </th>
                    {overallRetention.map((period, index) => (
                      <th key={index} className="p-2 text-center font-medium">
                        {period.period}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {retentionBySegment.map((segment, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{segment.segment}</td>
                      {segment.retention.map((point, pointIndex) => (
                        <td key={pointIndex} className="p-2 text-center">
                          <span
                            className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                              point.retention > 50
                                ? "bg-green-100 text-green-700"
                                : point.retention > 30
                                  ? "bg-blue-100 text-blue-700"
                                  : "bg-amber-100 text-amber-700"
                            }`}
                          >
                            {point.retention}%
                          </span>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter>
            <div className="w-full space-y-2">
              <h3 className="text-sm font-medium">
                Patient Segment Retention Summary
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Best Performing
                    </span>
                    <span className="font-medium">Chronic Condition</span>
                  </div>
                  <Progress value={65} max={100} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>65% retention at 48 weeks</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">
                      Needs Improvement
                    </span>
                    <span className="font-medium">Young Adults (18-35)</span>
                  </div>
                  <Progress value={15} max={100} className="h-1" />
                  <div className="flex items-center justify-between text-xs">
                    <span>15% retention at 48 weeks</span>
                  </div>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="insights" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Retention Insights & Recommendations</CardTitle>
            <CardDescription>
              Key insights and optimization opportunities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {retentionInsights.map((insight, index) => (
                <div
                  key={index}
                  className="border-b pb-4 last:border-0 last:pb-0"
                >
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          insight.impact === "High"
                            ? "bg-red-100 text-red-700"
                            : insight.impact === "Medium"
                              ? "bg-amber-100 text-amber-700"
                              : "bg-blue-100 text-blue-700"
                        }`}
                      >
                        {insight.impact} Impact
                      </span>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">{insight.title}</h3>
                    <p className="text-muted-foreground mb-2 text-sm">
                      {insight.description}
                    </p>
                    <h4 className="text-sm font-medium">Recommendation</h4>
                    <p className="text-muted-foreground text-sm">
                      {insight.recommendation}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">
              <Users className="mr-2 h-4 w-4" />
              Generate Intervention Plan
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default RetentionCurves
