"use client"

import type React from "react"
import { useState } from "react"
import {
  Activity,
  AlertCircle,
  // ArrowUpRight,
  // BarChart3,
  // BarChartIcon,
  Clock,
  DollarSign,
  FileText,
  Gauge,
  Heart,
  // LineChart,
  Star,
  Users,
  Zap,
} from "lucide-react"
import {
  Bar,
  BarChart,
  CartesianGrid,
  LabelList,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import AIDiagnosticDrilldownModal from "@/components/analysis/AIDiagnosticDrilldownModal"
import OperationalEfficiencyDrilldownModal from "@/components/analysis/OperationalEfficiencyDrilldownModal"
import PatientRetentionDrilldownModal from "@/components/analysis/PatientRetentionDrilldownModal"
import PatientSatisfactionDrilldownModal from "@/components/analysis/PatientSatisfactionDrilldownModal"
import PatientVolumeDrilldownModal from "@/components/analysis/PatientVolumeDrilldownModal"
import ResourceUtilizationDrilldownModal from "@/components/analysis/ResourceUtilizationDrilldownModal"
import RevenueGrowthDrilldownModal from "@/components/analysis/RevenueGrowthDrilldownModal"
import WaitTimeDrilldownModal from "@/components/analysis/WaitTimeDrilldownModal"
import InfoTooltip from "@/components/InfoTooltip"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Define analysis types
type AnalysisType =
  | "revenue-growth"
  | "patient-retention"
  | "operational-efficiency"
  | "risk-assessment"
  | "ai-diagnostic"
  | "wait-time"
  | "kpi-performance"
  | "resource-utilization"
  | "clinical-outcomes"
  | "patient-satisfaction"

// Analysis item interface
interface AnalysisItem {
  id: AnalysisType
  title: string
  category: string
  categoryColor: string
  date: string
  icon: React.ReactNode
}

const Analysis = () => {
  // State to track which analysis is selected
  const [selectedAnalysis, setSelectedAnalysis] =
    useState<AnalysisType>("revenue-growth")

  // Analysis items data
  const analysisItems: AnalysisItem[] = [
    {
      id: "revenue-growth",
      title: "Revenue Growth Analysis",
      category: "Financial",
      categoryColor: "text-primary",
      date: "2024-12-20",
      icon: <DollarSign className="size-3.5 text-blue-600" />,
    },
    {
      id: "patient-retention",
      title: "Patient Retention Trends",
      category: "Operations",
      categoryColor: "text-primary",
      date: "2024-12-19",
      icon: <Users className="size-3.5 text-blue-600" />,
    },
    {
      id: "operational-efficiency",
      title: "Operational Efficiency",
      category: "Operations",
      categoryColor: "text-primary",
      date: "2024-12-19",
      icon: <Zap className="size-3.5 text-blue-600" />,
    },
    {
      id: "risk-assessment",
      title: "Risk Assessment",
      category: "Risk",
      categoryColor: "text-primary",
      date: "2024-12-17",
      icon: <AlertCircle className="size-3.5 text-blue-600" />,
    },
    {
      id: "ai-diagnostic",
      title: "AI Diagnostic Accuracy",
      category: "Technology",
      categoryColor: "text-primary",
      date: "2024-12-16",
      icon: <Activity className="size-3.5 text-blue-600" />,
    },
    {
      id: "wait-time",
      title: "Wait Time Analysis",
      category: "Operations",
      categoryColor: "text-primary",
      date: "2024-12-15",
      icon: <Clock className="size-3.5 text-blue-600" />,
    },
    {
      id: "kpi-performance",
      title: "KPI Performance Review",
      category: "Performance",
      categoryColor: "text-primary",
      date: "2024-12-14",
      icon: <Star className="size-3.5 text-blue-600" />,
    },
    {
      id: "resource-utilization",
      title: "Resource Utilization",
      category: "Operations",
      categoryColor: "text-primary",
      date: "2024-12-13",
      icon: <Gauge className="size-3.5 text-blue-600" />,
    },
    {
      id: "clinical-outcomes",
      title: "Clinical Outcomes",
      category: "Medical",
      categoryColor: "text-primary",
      date: "2024-12-12",
      icon: <FileText className="size-3.5 text-blue-600" />,
    },
    {
      id: "patient-satisfaction",
      title: "Patient Satisfaction",
      category: "Service",
      categoryColor: "text-primary",
      date: "2024-12-11",
      icon: <Heart className="size-3.5 text-blue-600" />,
    },
  ]

  return (
    <>
      <Breadcrumb links={[{ label: "Analysis" }]} />

      <div className="flex flex-col gap-1.5">
        <CardTitle>Analysis</CardTitle>
        <CardDescription>
          AI-powered insights and recommendations
        </CardDescription>
      </div>

      <div className="grid gap-6 md:grid-cols-[1fr_2fr] lg:grid-cols-[1fr_2fr]">
        {/* Left Column - Generated Analyses */}
        <div className="space-y-6">
          <Card className="gap-0">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Generated Analyses</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {/* Map through analysis items */}
              {analysisItems.map((item) => (
                <Card
                  key={item.id}
                  className={`cursor-pointer py-1 hover:bg-gray-50 ${
                    selectedAnalysis === item.id
                      ? "border-l-primary border-l-4"
                      : "border"
                  }`}
                  onClick={() => setSelectedAnalysis(item.id)}
                >
                  <CardContent className="p-2">
                    <div className="flex items-start">
                      {/* <div className="mr-2 flex h-7 w-7 items-center justify-center rounded-full bg-blue-100">
                        {item.icon}
                      </div> */}
                      <div className="space-y-0.5">
                        <h3 className="text-sm font-medium">{item.title}</h3>
                        <div className="text-muted-foreground flex items-center text-xs">
                          <span className={item.categoryColor}>
                            {item.category}
                          </span>
                          <span className="mx-1">•</span>
                          <span>{item.date}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Dynamic Content Based on Selection */}
        <div className="space-y-6">
          {selectedAnalysis === "revenue-growth" && <RevenueGrowthAnalysis />}
          {selectedAnalysis === "patient-retention" && (
            <PatientRetentionAnalysis />
          )}
          {selectedAnalysis === "operational-efficiency" && (
            <OperationalEfficiencyAnalysis />
          )}
          {selectedAnalysis === "risk-assessment" && <RiskAssessmentAnalysis />}
          {selectedAnalysis === "ai-diagnostic" && <AIDiagnosticAnalysis />}
          {selectedAnalysis === "wait-time" && <WaitTimeAnalysis />}
          {selectedAnalysis === "kpi-performance" && <KPIPerformanceAnalysis />}
          {selectedAnalysis === "resource-utilization" && (
            <ResourceUtilizationAnalysis />
          )}
          {selectedAnalysis === "clinical-outcomes" && (
            <ClinicalOutcomesAnalysis />
          )}
          {selectedAnalysis === "patient-satisfaction" && (
            <PatientSatisfactionAnalysis />
          )}
        </div>
      </div>
    </>
  )
}

export default Analysis

// Revenue Growth Analysis Component
const RevenueGrowthAnalysis = () => {
  // State for drilldown modals
  const [revenueGrowthDrilldownOpen, setRevenueGrowthDrilldownOpen] =
    useState(false)
  const [patientVolumeDrilldownOpen, setPatientVolumeDrilldownOpen] =
    useState(false)
  const [revenuePerPatientDrilldownOpen, setRevenuePerPatientDrilldownOpen] =
    useState(false)
  const [growthForecastDrilldownOpen, setGrowthForecastDrilldownOpen] =
    useState(false)

  // Sample data for drilldown modals
  const revenueGrowthData = [
    { month: "Jul", current: 8.2, budget: 7.5, previous: 6.8 },
    { month: "Aug", current: 9.1, budget: 8.0, previous: 7.2 },
    { month: "Sep", current: 10.3, budget: 9.0, previous: 8.1 },
    { month: "Oct", current: 11.2, budget: 10.0, previous: 8.9 },
    { month: "Nov", current: 11.8, budget: 10.5, previous: 9.4 },
    { month: "Dec", current: 12.5, budget: 11.0, previous: 10.2 },
  ]

  const patientVolumeData = [
    { month: "Jul", current: 1670, budget: 1600, previous: 1450 },
    { month: "Aug", current: 1730, budget: 1650, previous: 1500 },
    { month: "Sep", current: 1800, budget: 1700, previous: 1550 },
    { month: "Oct", current: 1890, budget: 1750, previous: 1600 },
    { month: "Nov", current: 1990, budget: 1800, previous: 1650 },
    { month: "Dec", current: 2100, budget: 1850, previous: 1700 },
  ]

  const revenuePerPatientData = [
    { month: "Jul", current: 320, budget: 310, previous: 290 },
    { month: "Aug", current: 325, budget: 315, previous: 295 },
    { month: "Sep", current: 330, budget: 320, previous: 300 },
    { month: "Oct", current: 335, budget: 325, previous: 305 },
    { month: "Nov", current: 340, budget: 330, previous: 310 },
    { month: "Dec", current: 345, budget: 335, previous: 315 },
  ]

  const growthForecastData = [
    { month: "Jan", current: 15.2, budget: 13.5, previous: 12.8 },
    { month: "Feb", current: 15.8, budget: 14.0, previous: 13.2 },
    { month: "Mar", current: 16.3, budget: 14.5, previous: 13.6 },
    { month: "Apr", current: 16.8, budget: 15.0, previous: 14.0 },
    { month: "May", current: 17.2, budget: 15.5, previous: 14.4 },
    { month: "Jun", current: 17.5, budget: 16.0, previous: 14.8 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Revenue Growth Analysis</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Key Recommendation */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Focus on expanding the {`Women's`} Health unit while maintaining the
            current growth trajectory in Paediatrics. The analysis suggests
            potential for 15% revenue growth in Q1 2024 through targeted
            marketing and service expansion.
          </p>
        </div>

        {/* Key Insights, Expected Benefits, Potential Risks */}
        <div className="grid grid-cols-3 gap-4">
          {/* Key Insights */}
          <Card className="col-span-1 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <LineChart className="mr-2 h-4 w-4 text-sky-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Key Insights
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-sky-100">
                    <ArrowUpRight className="h-3 w-3 text-sky-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    {`Women's`} Health Leading
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  12% YoY growth, outperforming by 3-5%
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-sky-100">
                    <Users className="h-3 w-3 text-sky-600" />
                  </div> */}
                  <span className="text-xs font-medium">Patient Volume Up</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  15% increase in new registrations
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-sky-100">
                    <DollarSign className="h-3 w-3 text-sky-600" />
                  </div> */}
                  <span className="text-xs font-medium">Revenue Growth</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  8% increase in revenue per patient
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Expected Benefits */}
          <Card className="col-span-1 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <BarChartIcon className="mr-2 h-4 w-4 text-green-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Expected Benefits
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <DollarSign className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Revenue Boost</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Potential 15% increase in Q1 2024
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <BarChart3 className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Market Share</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Expected 5% market share gain
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <Zap className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Service Expansion</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  30% increase in service capacity
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Potential Risks */}
          <Card className="col-span-1 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <AlertCircle className="mr-2 h-4 w-4 text-red-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Potential Risks
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-100">
                    <Users className="h-3 w-3 text-red-600" />
                  </div> */}
                  <span className="text-xs font-medium">Staff Shortage</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Need 20% more specialists
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-100">
                    <Clock className="h-3 w-3 text-red-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    Implementation Time
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  6-month ramp-up period needed
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-100">
                    <AlertCircle className="h-3 w-3 text-red-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    Competition Response
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Potential price pressure from rivals
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Supporting Data */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-500">Supporting Data</h3>
          <div className="grid grid-cols-2 gap-4">
            {/* Revenue Growth by Unit */}
            <Card className="gap-0 p-1">
              <CardHeader className="p-3 pb-1">
                <CardTitle className="flex items-center gap-2 text-base font-medium">
                  Revenue Growth by Unit
                  <InfoTooltip>
                    This chart compares{" "}
                    <span className="text-primary font-semibold">
                      revenue growth percentages
                    </span>{" "}
                    across different{" "}
                    <span className="text-primary font-semibold">
                      hospital units
                    </span>
                    , highlighting which departments are experiencing the{" "}
                    <span className="text-primary font-semibold">
                      strongest financial performance
                    </span>
                    . The visualization shows both{" "}
                    <span className="text-primary font-semibold">
                      year-over-year growth rates
                    </span>{" "}
                    and{" "}
                    <span className="text-primary font-semibold">
                      quarterly trends
                    </span>
                    , helping identify high-performing units like{" "}
                    <span className="text-primary font-semibold">
                      {`Women's`} Health (12% YoY growth)
                    </span>{" "}
                    and areas needing attention.
                  </InfoTooltip>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3">
                <div
                  className="h-[180px] w-full cursor-pointer"
                  onClick={() => setRevenueGrowthDrilldownOpen(true)}
                >
                  <RevenueGrowthChart />
                </div>
              </CardContent>
            </Card>

            {/* Patient Volume Trends */}
            <Card className="gap-0 p-1">
              <CardHeader className="p-3 pb-1">
                <CardTitle className="flex items-center gap-2 text-base font-medium">
                  Patient Volume Trends
                  <InfoTooltip>
                    This chart tracks monthly patient volume across different
                    departments, showing both total visits and new patient
                    acquisition trends. The visualization reveals a{" "}
                    <span className="text-primary font-semibold">
                      15% increase in new registrations
                    </span>{" "}
                    and identifies{" "}
                    <span className="text-primary font-semibold">
                      seasonal patterns
                    </span>{" "}
                    in patient flow, helping optimize staffing and resource
                    allocation based on anticipated demand.
                  </InfoTooltip>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3">
                <div
                  className="h-[180px] w-full cursor-pointer"
                  onClick={() => setPatientVolumeDrilldownOpen(true)}
                >
                  <PatientVolumeChart />
                </div>
              </CardContent>
            </Card>

            {/* Revenue per Patient */}
            <Card className="gap-0 p-1">
              <CardHeader className="p-3 pb-1">
                <CardTitle className="flex items-center gap-2 text-base font-medium">
                  Revenue per Patient
                  <InfoTooltip>
                    This chart analyzes the average revenue generated per
                    patient visit across different service lines and
                    departments. The visualization shows an{" "}
                    <span className="text-primary font-semibold">
                      8% increase in revenue per patient
                    </span>{" "}
                    and breaks down revenue sources (
                    <span className="text-primary font-semibold">
                      consultations, treatments, procedures
                    </span>
                    ), helping identify opportunities for service optimization
                    and revenue enhancement.
                  </InfoTooltip>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3">
                <div
                  className="h-[180px] w-full cursor-pointer"
                  onClick={() => setRevenuePerPatientDrilldownOpen(true)}
                >
                  <RevenuePerPatientChart />
                </div>
              </CardContent>
            </Card>

            {/* Growth Forecast */}
            <Card className="gap-0 p-1">
              <CardHeader className="p-3 pb-1">
                <CardTitle className="flex items-center gap-2 text-base font-medium">
                  Growth Forecast
                  <InfoTooltip>
                    This chart projects future revenue growth across quarters,
                    comparing historical performance with forecasted trends. The
                    visualization predicts a potential{" "}
                    <span className="text-primary font-semibold">
                      15% revenue increase in Q1 2024
                    </span>{" "}
                    and identifies{" "}
                    <span className="text-primary font-semibold">
                      key growth drivers by service line
                    </span>
                    , helping with strategic planning and resource allocation
                    for anticipated expansion.
                  </InfoTooltip>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3">
                <div
                  className="h-[180px] w-full cursor-pointer"
                  onClick={() => setGrowthForecastDrilldownOpen(true)}
                >
                  <GrowthForecastChart />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Drilldown Modals */}
        <RevenueGrowthDrilldownModal
          open={revenueGrowthDrilldownOpen}
          onOpenChange={setRevenueGrowthDrilldownOpen}
          initialData={revenueGrowthData}
          title="Revenue Growth Analysis"
          description="Detailed analysis of revenue growth across different hospital units"
        />

        <PatientVolumeDrilldownModal
          open={patientVolumeDrilldownOpen}
          onOpenChange={setPatientVolumeDrilldownOpen}
          initialData={patientVolumeData}
          title="Patient Volume Analysis"
          description="Detailed analysis of patient volume trends and new patient acquisition"
        />

        <RevenueGrowthDrilldownModal
          open={revenuePerPatientDrilldownOpen}
          onOpenChange={setRevenuePerPatientDrilldownOpen}
          initialData={revenuePerPatientData}
          title="Revenue per Patient Analysis"
          description="Detailed analysis of revenue generated per patient across service lines"
        />

        <RevenueGrowthDrilldownModal
          open={growthForecastDrilldownOpen}
          onOpenChange={setGrowthForecastDrilldownOpen}
          initialData={growthForecastData}
          title="Growth Forecast Analysis"
          description="Detailed projections of future revenue growth and key drivers"
        />
      </CardContent>
    </Card>
  )
}

// Patient Retention Analysis Component
const PatientRetentionAnalysis = () => {
  // State for drilldown modal
  const [retentionDrilldownOpen, setRetentionDrilldownOpen] = useState(false)

  // Sample data for drilldown modal
  const retentionData = [
    { month: "Jan", current: 78, budget: 85, previous: 80 },
    { month: "Feb", current: 75, budget: 85, previous: 79 },
    { month: "Mar", current: 72, budget: 85, previous: 78 },
    { month: "Apr", current: 70, budget: 85, previous: 76 },
    { month: "May", current: 68, budget: 85, previous: 75 },
    { month: "Jun", current: 68, budget: 85, previous: 74 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Patient Retention Trends</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Implement a patient follow-up program to increase retention rates by
            25%. Focus on post-discharge communication and personalized care
            plans.
          </p>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <Card className="col-span-1 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <LineChart className="mr-2 h-4 w-4 text-amber-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Key Insights
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-amber-100">
                    <ArrowUpRight className="h-3 w-3 text-amber-600" />
                  </div> */}
                  <span className="text-xs font-medium">Retention Decline</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  15% drop in return visits over 6 months
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-amber-100">
                    <Users className="h-3 w-3 text-amber-600" />
                  </div> */}
                  <span className="text-xs font-medium">Patient Feedback</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Communication gaps identified in surveys
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-2 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="flex items-center gap-2 text-base font-medium">
                Patient Retention Rate by Department
                <InfoTooltip>
                  This chart compares patient retention rates across six medical
                  departments against the{" "}
                  <span className="text-primary font-semibold">
                    target benchmark of 85%
                  </span>
                  . The visualization reveals that{" "}
                  <span className="text-primary font-semibold">
                    OB/GYN leads with 86% retention
                  </span>{" "}
                  while{" "}
                  <span className="text-primary font-semibold">
                    Neurology struggles at 68%
                  </span>
                  , highlighting a{" "}
                  <span className="text-primary font-semibold">
                    15% overall drop in return visits
                  </span>{" "}
                  over 6 months and identifying departments requiring focused
                  patient follow-up programs.
                </InfoTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="flex h-[180px] w-full cursor-pointer items-center justify-center"
                onClick={() => setRetentionDrilldownOpen(true)}
              >
                <PatientRetentionChart />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Drilldown Modal */}
        <PatientRetentionDrilldownModal
          open={retentionDrilldownOpen}
          onOpenChange={setRetentionDrilldownOpen}
          initialData={retentionData}
          title="Patient Retention Analysis"
          description="Detailed analysis of patient retention rates across departments and time periods"
        />
      </CardContent>
    </Card>
  )
}

function PatientRetentionChart() {
  // Department data with current and target retention rates
  const departments = [
    { name: "Cardiology", current: 78, target: 85, color: "amber" },
    { name: "Pediatrics", current: 82, target: 85, color: "green" },
    { name: "Oncology", current: 75, target: 85, color: "amber" },
    { name: "Neurology", current: 68, target: 85, color: "red" },
    { name: "OB/GYN", current: 86, target: 85, color: "green" },
    { name: "Orthopedics", current: 72, target: 85, color: "amber" },
  ]

  return (
    <div className="relative h-full w-full">
      {/* Y-axis labels */}
      <div className="text-muted-foreground absolute top-0 left-0 flex h-full flex-col justify-between pr-2 text-[10px]">
        <span>100%</span>
        <span>80%</span>
        <span>60%</span>
        <span>40%</span>
        <span>20%</span>
        <span>0%</span>
      </div>

      {/* Chart grid lines */}
      <div className="absolute inset-0 left-8 flex flex-col justify-between">
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
      </div>

      {/* Bars */}
      <div className="absolute inset-0 left-8 flex items-end justify-between px-4 pt-6 pb-6">
        {departments.map((dept, index) => (
          <div key={index} className="flex flex-col items-center">
            {/* Current rate bar */}
            <div className="relative flex flex-col items-center">
              <div
                className={`bg-chart-1 w-8`}
                style={{
                  height: `${dept.current * 1.4}px`,
                }}
              ></div>

              {/* Target line */}
              <div
                className="absolute h-0.5 w-10 bg-gray-800"
                style={{ bottom: `${dept.target * 1.4}px`, left: "-1px" }}
              ></div>

              {/* Percentage label */}
              <span
                className="absolute text-[10px] font-medium"
                style={{ bottom: `${dept.current * 1.4 + 2}px` }}
              >
                {dept.current}%
              </span>
            </div>

            {/* Department name */}
            <span className="text-muted-foreground mt-2 max-w-[40px] text-center text-[10px]">
              {dept.name}
            </span>
          </div>
        ))}
      </div>

      {/* Legend */}
      {/* <div className="absolute right-0 bottom-0 flex items-center gap-3 text-[10px]">
        <div className="flex items-center">
          <div className="mr-1 h-2 w-2 bg-gray-800"></div>
          <span>Target</span>
        </div>
        <div className="flex items-center">
          <div className="mr-1 h-2 w-2 bg-green-500"></div>
          <span>Good</span>
        </div>
        <div className="flex items-center">
          <div className="mr-1 h-2 w-2 bg-amber-500"></div>
          <span>Warning</span>
        </div>
        <div className="flex items-center">
          <div className="mr-1 h-2 w-2 bg-red-500"></div>
          <span>Critical</span>
        </div>
      </div> */}
    </div>
  )
}

// Operational Efficiency Analysis Component
const OperationalEfficiencyAnalysis = () => {
  // State for drilldown modal
  const [efficiencyDrilldownOpen, setEfficiencyDrilldownOpen] = useState(false)

  // Sample data for drilldown modal
  const efficiencyData = [
    { month: "Jan", current: 65, budget: 80, previous: 60 },
    { month: "Feb", current: 68, budget: 80, previous: 62 },
    { month: "Mar", current: 70, budget: 80, previous: 64 },
    { month: "Apr", current: 72, budget: 80, previous: 66 },
    { month: "May", current: 75, budget: 80, previous: 68 },
    { month: "Jun", current: 78, budget: 80, previous: 70 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Operational Efficiency</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Optimize staff scheduling and implement digital check-in to reduce
            patient wait times by 35% and increase throughput by 20%.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Card className="col-span-1 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <Zap className="mr-2 h-4 w-4 text-green-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Efficiency Metrics
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <Clock className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Average Wait Time</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  42 minutes (18% above target)
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <Users className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Staff Utilization</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  72% (below optimal 85%)
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1 gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="flex items-center gap-2 text-base font-medium">
                Efficiency Improvement Potential
                <InfoTooltip>
                  This chart compares current operational metrics against
                  potential improvement targets across four key areas. The
                  visualization shows that wait times could be reduced from{" "}
                  <span className="text-primary font-semibold">
                    42 to 27 minutes (35% improvement)
                  </span>
                  , staff utilization could increase from{" "}
                  <span className="text-primary font-semibold">
                    72% to 85% (18% improvement)
                  </span>
                  , patient throughput could grow from{" "}
                  <span className="text-primary font-semibold">
                    8.5 to 10.2 patients/hour (20% improvement)
                  </span>
                  , and processing time could decrease from{" "}
                  <span className="text-primary font-semibold">
                    18 to 12 minutes (33% improvement)
                  </span>
                  .
                </InfoTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="cursor-pointer"
                onClick={() => setEfficiencyDrilldownOpen(true)}
              >
                <EfficiencyImprovementChart />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Drilldown Modal */}
        <OperationalEfficiencyDrilldownModal
          open={efficiencyDrilldownOpen}
          onOpenChange={setEfficiencyDrilldownOpen}
          initialData={efficiencyData}
          title="Operational Efficiency Analysis"
          description="Detailed analysis of operational efficiency metrics and improvement opportunities"
        />
      </CardContent>
    </Card>
  )
}

interface Metric {
  name: string
  current: number
  potential: number
  unit: string
  improvement: string
  color: string
}

function EfficiencyImprovementChart() {
  const rawMetrics: Metric[] = [
    {
      name: "Wait Time",
      current: 42,
      potential: 27,
      unit: "min",
      improvement: "35%",
      color: "var(--chart-1)", // green-500
    },
    {
      name: "Staff Utilization",
      current: 72,
      potential: 85,
      unit: "%",
      improvement: "18%",
      color: "var(--chart-2)", // blue-500
    },
    {
      name: "Patient Throughput",
      current: 8.5,
      potential: 10.2,
      unit: "pts/hr",
      improvement: "20%",
      color: "var(--chart-3)", // purple-500
    },
    {
      name: "Processing Time",
      current: 18,
      potential: 12,
      unit: "min",
      improvement: "33%",
      color: "var(--chart-4)", // teal-500
    },
  ]

  // Normalize values for bar length scaling
  const normalize = (value: number, metric: string) => {
    if (metric === "Wait Time" || metric === "Processing Time")
      return (value / 50) * 100
    if (metric === "Staff Utilization") return value
    return (value / 12) * 100
  }

  const chartData = rawMetrics.map((m) => ({
    name: m.name,
    current: normalize(m.current, m.name),
    potential: normalize(m.potential, m.name),
    improvement: m.improvement,
    unit: m.unit,
    currentLabel: `${m.current}${m.unit}`,
    potentialLabel: `${m.potential}${m.unit}`,
    color: m.color,
  }))

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart layout="vertical" data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" hide domain={[0, 100]} />
        <YAxis type="category" dataKey="name" tick={{ fontSize: 12 }} />
        <Tooltip
          formatter={(value: number, name: string, props) => {
            const item = chartData.find((d) => d.name === props.payload.name)
            return name === "current"
              ? item?.currentLabel
              : item?.potentialLabel
          }}
        />
        <Legend />
        <Bar dataKey="current" fill="var(--chart-1)" name="Current">
          <LabelList dataKey="currentLabel" position="right" fontSize={10} />
        </Bar>
        <Bar dataKey="potential" fill="var(--chart-3)" name="Potential">
          <LabelList dataKey="potentialLabel" position="right" fontSize={10} />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// Risk Assessment Analysis Component
const RiskAssessmentAnalysis = () => {
  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Risk Assessment</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Implement enhanced security protocols for patient data and develop
            contingency plans for potential staffing shortages in Q1 2024.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <AlertCircle className="mr-2 h-4 w-4 text-red-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Critical Risk Factors
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="p-3">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-100">
                      <AlertCircle className="h-3 w-3 text-red-600" />
                    </div> */}
                    <span className="text-xs font-medium">Data Security</span>
                  </div>
                  <span className="text-xs font-bold text-red-500">
                    High Risk
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-amber-100">
                      <Users className="h-3 w-3 text-amber-600" />
                    </div> */}
                    <span className="text-xs font-medium">Staffing Levels</span>
                  </div>
                  <span className="text-xs font-bold text-amber-500">
                    Medium Risk
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                      <DollarSign className="h-3 w-3 text-green-600" />
                    </div> */}
                    <span className="text-xs font-medium">
                      Financial Stability
                    </span>
                  </div>
                  <span className="text-xs font-bold text-green-500">
                    Low Risk
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}

// AI Diagnostic Analysis Component
const AIDiagnosticAnalysis = () => {
  // State for drilldown modal
  const [aiDiagnosticDrilldownOpen, setAiDiagnosticDrilldownOpen] =
    useState(false)

  // Sample data for drilldown modal
  const aiDiagnosticData = [
    { month: "Jan", current: 85, budget: 90, previous: 80 },
    { month: "Feb", current: 87, budget: 90, previous: 81 },
    { month: "Mar", current: 89, budget: 90, previous: 83 },
    { month: "Apr", current: 91, budget: 90, previous: 84 },
    { month: "May", current: 93, budget: 90, previous: 86 },
    { month: "Jun", current: 95, budget: 90, previous: 88 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>AI Diagnostic Accuracy</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Expand AI diagnostic tools to radiology and pathology departments
            based on 95% accuracy rates in pilot programs.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="flex items-center gap-2 text-base font-medium">
                AI Diagnostic Accuracy by Department
                <InfoTooltip>
                  This chart displays AI diagnostic accuracy percentages across
                  four medical specialties. The visualization shows{" "}
                  <span className="text-primary font-semibold">
                    Radiology leading with 95% accuracy
                  </span>
                  , followed by{" "}
                  <span className="text-primary font-semibold">
                    Pathology (92%)
                  </span>
                  ,
                  <span className="text-primary font-semibold">
                    Dermatology (88%)
                  </span>
                  , and{" "}
                  <span className="text-primary font-semibold">
                    Cardiology (85%)
                  </span>
                  . These results support the recommendation to expand AI
                  diagnostic tools to departments with highest accuracy rates,
                  particularly
                  <span className="text-primary font-semibold">
                    {" "}
                    Radiology and Pathology where performance exceeds 90%
                  </span>
                  .
                </InfoTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="cursor-pointer"
                onClick={() => setAiDiagnosticDrilldownOpen(true)}
              >
                <AIDiagnosticChart />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Drilldown Modal */}
        <AIDiagnosticDrilldownModal
          open={aiDiagnosticDrilldownOpen}
          onOpenChange={setAiDiagnosticDrilldownOpen}
          initialData={aiDiagnosticData}
          title="AI Diagnostic Accuracy Analysis"
          description="Detailed analysis of AI diagnostic accuracy across departments and time periods"
        />
      </CardContent>
    </Card>
  )
}

const AIDiagnosticChart = () => {
  const diagnosticData = [
    { department: "Radiology", accuracy: 95, label: "95%", color: "#8B5CF6" },
    { department: "Pathology", accuracy: 92, label: "92%", color: "#3B82F6" },
    { department: "Dermatology", accuracy: 88, label: "88%", color: "#10B981" },
    { department: "Cardiology", accuracy: 85, label: "85%", color: "#F59E0B" },
  ]

  return (
    <ResponsiveContainer width="100%" height={180}>
      <BarChart
        layout="vertical"
        data={diagnosticData}
        margin={{ left: 60, right: 30 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" domain={[0, 100]} hide />
        <YAxis dataKey="department" type="category" tick={{ fontSize: 12 }} />
        <Tooltip formatter={(value: number) => `${value}%`} />
        <Bar dataKey="accuracy" isAnimationActive fill="var(--chart-1)">
          <LabelList dataKey="label" position="right" fontSize={10} />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// Wait Time Analysis Component
const WaitTimeAnalysis = () => {
  // State for drilldown modal
  const [waitTimeDrilldownOpen, setWaitTimeDrilldownOpen] = useState(false)

  // Sample data for drilldown modal
  const waitTimeData = [
    { month: "Jan", current: 52, budget: 40, previous: 55 },
    { month: "Feb", current: 49, budget: 40, previous: 54 },
    { month: "Mar", current: 45, budget: 40, previous: 53 },
    { month: "Apr", current: 47, budget: 40, previous: 52 },
    { month: "May", current: 42, budget: 40, previous: 50 },
    { month: "Jun", current: 40, budget: 40, previous: 48 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Wait Time Analysis</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Implement staggered appointment scheduling and digital queue
            management to reduce average wait times by 40% and improve patient
            satisfaction scores.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <Clock className="mr-2 h-4 w-4 text-orange-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Wait Time Metrics
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-orange-100">
                    <Clock className="h-3 w-3 text-orange-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    Emergency Department
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Average wait: 47 minutes
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-orange-100">
                    <Clock className="h-3 w-3 text-orange-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    Outpatient Services
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  Average wait: 32 minutes
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="flex items-center gap-2 text-base font-medium">
                Wait Time Trends
                <InfoTooltip>
                  This chart tracks monthly wait time trends in minutes for both
                  Emergency Department and Outpatient Services from January
                  through May. The visualization shows a positive downward trend
                  with{" "}
                  <span className="text-primary font-semibold">
                    Emergency wait times decreasing from 52 to 42 minutes (19%
                    improvement)
                  </span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">
                    Outpatient wait times reducing from 38 to 28 minutes (26%
                    improvement)
                  </span>
                  , supporting the effectiveness of recent operational changes.
                </InfoTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="cursor-pointer"
                onClick={() => setWaitTimeDrilldownOpen(true)}
              >
                <WaitTimeTrendChart />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Drilldown Modal */}
        <WaitTimeDrilldownModal
          open={waitTimeDrilldownOpen}
          onOpenChange={setWaitTimeDrilldownOpen}
          initialData={waitTimeData}
          title="Wait Time Analysis"
          description="Detailed analysis of wait time trends across departments and time periods"
        />
      </CardContent>
    </Card>
  )
}

const WaitTimeTrendChart = () => {
  const waitTimeTrendData = [
    { period: "Jan", emergency: 52, outpatient: 38 },
    { period: "Feb", emergency: 49, outpatient: 35 },
    { period: "Mar", emergency: 45, outpatient: 32 },
    { period: "Apr", emergency: 47, outpatient: 30 },
    { period: "May", emergency: 42, outpatient: 28 },
  ]

  return (
    <ResponsiveContainer width="100%" height={180}>
      <BarChart data={waitTimeTrendData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="period" />
        <YAxis unit="min" />
        <Tooltip formatter={(value) => `${value} min`} />
        <Legend />
        <Bar dataKey="emergency" name="Emergency Dept" fill="var(--chart-1)">
          <LabelList dataKey="emergency" position="top" fontSize={10} />
        </Bar>
        <Bar
          dataKey="outpatient"
          name="Outpatient Services"
          fill="var(--chart-3)"
        >
          <LabelList dataKey="outpatient" position="top" fontSize={10} />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// KPI Performance Analysis Component
const KPIPerformanceAnalysis = () => {
  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>KPI Performance Review</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Revise quarterly targets for patient satisfaction and operational
            efficiency to align with industry benchmarks and strategic goals.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <Star className="mr-2 h-4 w-4 text-amber-500" /> */}
                <CardTitle className="text-sm font-medium">
                  KPI Performance Summary
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="p-3">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                      <Star className="h-3 w-3 text-green-600" />
                    </div> */}
                    <span className="text-xs font-medium">Revenue Growth</span>
                  </div>
                  <span className="text-xs font-bold text-green-500">
                    Exceeding Target
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-amber-100">
                      <Star className="h-3 w-3 text-amber-600" />
                    </div> */}
                    <span className="text-xs font-medium">
                      Patient Satisfaction
                    </span>
                  </div>
                  <span className="text-xs font-bold text-amber-500">
                    Meeting Target
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-100">
                      <Star className="h-3 w-3 text-red-600" />
                    </div> */}
                    <span className="text-xs font-medium">Staff Retention</span>
                  </div>
                  <span className="text-xs font-bold text-red-500">
                    Below Target
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}

// Resource Utilization Analysis Component
const ResourceUtilizationAnalysis = () => {
  // State for drilldown modal
  const [
    resourceUtilizationDrilldownOpen,
    setResourceUtilizationDrilldownOpen,
  ] = useState(false)

  // Sample data for drilldown modal
  const resourceUtilizationData = [
    { month: "Jan", current: 58, budget: 80, previous: 55 },
    { month: "Feb", current: 60, budget: 80, previous: 56 },
    { month: "Mar", current: 63, budget: 80, previous: 58 },
    { month: "Apr", current: 65, budget: 80, previous: 60 },
    { month: "May", current: 68, budget: 80, previous: 62 },
    { month: "Jun", current: 70, budget: 80, previous: 65 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Resource Utilization</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Optimize equipment scheduling and implement shared resource model to
            increase utilization rates by 25% and reduce operational costs.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <Gauge className="mr-2 h-4 w-4 text-green-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Resource Utilization Rates
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <Gauge className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Imaging Equipment</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  65% utilization (target: 80%)
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
                    <Gauge className="h-3 w-3 text-green-600" />
                  </div> */}
                  <span className="text-xs font-medium">Operating Rooms</span>
                </div>
                <p className="text-muted-foreground text-xs">
                  72% utilization (target: 85%)
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="flex items-center gap-2 text-base font-medium">
                Resource Utilization Trends
                <InfoTooltip>
                  This chart tracks monthly utilization rates for two key
                  hospital resources: Imaging Equipment and Operating Rooms. The
                  visualization shows a positive trend with{" "}
                  <span className="text-primary font-semibold">
                    Imaging Equipment utilization increasing from 58% to 68%
                  </span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">
                    Operating Rooms from 65% to 74%
                  </span>{" "}
                  over five months, though both remain below their{" "}
                  <span className="text-primary font-semibold">
                    optimal targets of 80% and 85%
                  </span>{" "}
                  respectively, indicating further optimization potential.
                </InfoTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="cursor-pointer"
                onClick={() => setResourceUtilizationDrilldownOpen(true)}
              >
                <ResourceUtilizationTrendChart />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Drilldown Modal */}
        <ResourceUtilizationDrilldownModal
          open={resourceUtilizationDrilldownOpen}
          onOpenChange={setResourceUtilizationDrilldownOpen}
          initialData={resourceUtilizationData}
          title="Resource Utilization Analysis"
          description="Detailed analysis of resource utilization rates across different equipment types and time periods"
        />
      </CardContent>
    </Card>
  )
}

const ResourceUtilizationTrendChart = () => {
  const resourceUtilizationData = [
    { period: "Jan", imaging: 58, or: 65 },
    { period: "Feb", imaging: 60, or: 67 },
    { period: "Mar", imaging: 63, or: 70 },
    { period: "Apr", imaging: 65, or: 72 },
    { period: "May", imaging: 68, or: 74 },
  ]

  return (
    <ResponsiveContainer width="100%" height={180}>
      <BarChart data={resourceUtilizationData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="period" />
        <YAxis unit="%" domain={[0, 100]} />
        <Tooltip formatter={(value) => `${value}%`} />
        <Legend />
        <Bar dataKey="imaging" name="Imaging Equipment" fill="var(--chart-1)">
          <LabelList dataKey="imaging" position="top" fontSize={10} />
        </Bar>
        <Bar dataKey="or" name="Operating Rooms" fill="var(--chart-3)">
          <LabelList dataKey="or" position="top" fontSize={10} />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// Clinical Outcomes Analysis Component
const ClinicalOutcomesAnalysis = () => {
  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Clinical Outcomes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Implement standardized treatment protocols for common conditions to
            improve recovery rates and reduce readmissions by 18%.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <FileText className="mr-2 h-4 w-4 text-blue-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Clinical Outcome Metrics
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="p-3">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
                      <FileText className="h-3 w-3 text-blue-600" />
                    </div> */}
                    <span className="text-xs font-medium">Recovery Rate</span>
                  </div>
                  <span className="text-xs font-bold">
                    87% (industry avg: 82%)
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
                      <FileText className="h-3 w-3 text-blue-600" />
                    </div> */}
                    <span className="text-xs font-medium">
                      Readmission Rate
                    </span>
                  </div>
                  <span className="text-xs font-bold">
                    12% (industry avg: 9%)
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}

// Patient Satisfaction Analysis Component
const PatientSatisfactionAnalysis = () => {
  // State for drilldown modal
  const [satisfactionDrilldownOpen, setSatisfactionDrilldownOpen] =
    useState(false)

  // Sample data for drilldown modal
  const satisfactionData = [
    { month: "Jan", current: 3.8, budget: 4.2, previous: 3.6 },
    { month: "Feb", current: 3.9, budget: 4.2, previous: 3.7 },
    { month: "Mar", current: 4.0, budget: 4.2, previous: 3.7 },
    { month: "Apr", current: 4.1, budget: 4.2, previous: 3.8 },
    { month: "May", current: 4.2, budget: 4.2, previous: 3.9 },
    { month: "Jun", current: 4.3, budget: 4.2, previous: 4.0 },
  ]

  return (
    <Card className="gap-0">
      <CardHeader className="pb-2">
        <CardTitle>Patient Satisfaction</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-500">
            Key Recommendation
          </h3>
          <p className="text-primary text-sm">
            Enhance communication training for staff and implement real-time
            feedback system to improve patient satisfaction scores by 15%.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <div className="flex items-center">
                {/* <Heart className="mr-2 h-4 w-4 text-pink-500" /> */}
                <CardTitle className="text-sm font-medium">
                  Satisfaction Scores
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 p-3">
              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-pink-100">
                    <Heart className="h-3 w-3 text-pink-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    Overall Satisfaction
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  3.8/5.0 (target: 4.2/5.0)
                </p>
              </div>

              <div className="space-y-1">
                <div className="flex items-center">
                  {/* <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-pink-100">
                    <Heart className="h-3 w-3 text-pink-600" />
                  </div> */}
                  <span className="text-xs font-medium">
                    Staff Communication
                  </span>
                </div>
                <p className="text-muted-foreground text-xs">
                  3.5/5.0 (target: 4.0/5.0)
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="gap-0 p-1">
            <CardHeader className="p-3 pb-1">
              <CardTitle className="flex items-center gap-2 text-base font-medium">
                Satisfaction Trends
                <InfoTooltip>
                  This chart tracks monthly satisfaction scores for two key
                  patient categories: Overall and Staff Communication. The
                  visualization shows a positive trend with both scores
                  improving from{" "}
                  <span className="text-primary font-semibold">3.8 to 4.2</span>{" "}
                  and{" "}
                  <span className="text-primary font-semibold">3.5 to 4.0</span>{" "}
                  respectively, indicating the effectiveness of{" "}
                  <span className="text-primary font-semibold">
                    communication training and real-time feedback systems
                  </span>
                  .
                </InfoTooltip>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <div
                className="cursor-pointer"
                onClick={() => setSatisfactionDrilldownOpen(true)}
              >
                <SatisfactionTrendChart />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Drilldown Modal */}
        <PatientSatisfactionDrilldownModal
          open={satisfactionDrilldownOpen}
          onOpenChange={setSatisfactionDrilldownOpen}
          initialData={satisfactionData}
          title="Patient Satisfaction Analysis"
          description="Detailed analysis of patient satisfaction scores across different categories and time periods"
        />
      </CardContent>
    </Card>
  )
}

const SatisfactionTrendChart = () => {
  const satisfactionData = [
    {
      category: "Overall",
      current: 3.8,
      target: 4.2,
    },
    {
      category: "Staff",
      current: 3.5,
      target: 4.0,
    },
  ]

  const normalize = (value: number) => (value / 5) * 100

  const formattedData = satisfactionData.map((item) => ({
    category: item.category,
    current: normalize(item.current),
    target: normalize(item.target),
    currentLabel: `${item.current.toFixed(1)}/5.0`,
    targetLabel: `${item.target.toFixed(1)}/5.0`,
  }))

  return (
    <ResponsiveContainer width="100%" height={180}>
      <BarChart layout="vertical" data={formattedData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" domain={[0, 100]} hide />
        <YAxis type="category" dataKey="category" tick={{ fontSize: 12 }} />
        <Tooltip
          formatter={(value: number, name: string, props) =>
            name === "current"
              ? props.payload.currentLabel
              : props.payload.targetLabel
          }
        />
        <Legend />
        <Bar dataKey="current" fill="var(--chart-1)" name="Current Score">
          <LabelList dataKey="currentLabel" position="right" fontSize={10} />
        </Bar>
        <Bar dataKey="target" fill="var(--chart-3)" name="Target Score">
          <LabelList dataKey="targetLabel" position="right" fontSize={10} />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// Placeholder chart components
function RevenueGrowthChart() {
  return (
    <div className="flex h-full w-full items-end justify-between gap-[2px]">
      {/* Jul */}
      <div className="flex w-1/6 flex-col items-center gap-1">
        <div className="flex w-full items-baseline gap-[1px]">
          <div className="bg-chart-1 h-24 w-1/3 rounded"></div>
          <div className="bg-chart-3 h-16 w-1/3 rounded"></div>
          <div className="bg-chart-5 h-12 w-1/3 rounded"></div>
        </div>
        <span className="text-muted-foreground text-[10px]">Jul</span>
      </div>

      {/* Aug */}
      <div className="flex w-1/6 flex-col items-center gap-1">
        <div className="flex w-full items-baseline gap-[1px]">
          <div className="bg-chart-1 h-28 w-1/3 rounded"></div>
          <div className="bg-chart-3 h-18 w-1/3 rounded"></div>
          <div className="bg-chart-5 h-14 w-1/3 rounded"></div>
        </div>
        <span className="text-muted-foreground text-[10px]">Aug</span>
      </div>

      {/* Sep */}
      <div className="flex w-1/6 flex-col items-center gap-1">
        <div className="flex w-full items-baseline gap-[1px]">
          <div className="bg-chart-1 h-32 w-1/3 rounded"></div>
          <div className="bg-chart-3 h-20 w-1/3 rounded"></div>
          <div className="bg-chart-5 h-16 w-1/3 rounded"></div>
        </div>
        <span className="text-muted-foreground text-[10px]">Sep</span>
      </div>

      {/* Oct */}
      <div className="flex w-1/6 flex-col items-center gap-1">
        <div className="flex w-full items-baseline gap-[1px]">
          <div className="bg-chart-1 h-36 w-1/3 rounded"></div>
          <div className="bg-chart-3 h-22 w-1/3 rounded"></div>
          <div className="bg-chart-5 h-18 w-1/3 rounded"></div>
        </div>
        <span className="text-muted-foreground text-[10px]">Oct</span>
      </div>

      {/* Nov */}
      <div className="flex w-1/6 flex-col items-center gap-1">
        <div className="flex w-full items-baseline gap-[1px]">
          <div className="bg-chart-1 h-40 w-1/3 rounded"></div>
          <div className="bg-chart-3 h-24 w-1/3 rounded"></div>
          <div className="bg-chart-5 h-20 w-1/3 rounded"></div>
        </div>
        <span className="text-muted-foreground text-[10px]">Nov</span>
      </div>

      {/* Dec */}
      <div className="flex w-1/6 flex-col items-center gap-1">
        <div className="flex w-full items-baseline gap-[1px]">
          <div className="bg-chart-1 h-44 w-1/3 rounded"></div>
          <div className="bg-chart-3 h-28 w-1/3 rounded"></div>
          <div className="bg-chart-5 h-24 w-1/3 rounded"></div>
        </div>
        <span className="text-muted-foreground text-[10px]">Dec</span>
      </div>
    </div>
  )
}

function PatientVolumeChart() {
  return (
    <div className="relative h-full w-full">
      <div className="absolute inset-0 flex flex-col justify-between">
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
      </div>

      <div className="relative h-full w-full">
        {/* New Patients Line */}
        <svg
          className="absolute inset-0 -top-0.5 h-full w-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,80 L20,75 L40,70 L60,65 L80,60 L100,55"
            fill="none"
            stroke="var(--chart-1)"
            strokeWidth="2"
          />
        </svg>

        {/* Returning Patients Line */}
        <svg
          className="absolute inset-0 -top-0.5 h-full w-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,50 L20,48 L40,45 L60,40 L80,35 L100,30"
            fill="none"
            stroke="var(--chart-3)"
            strokeWidth="2"
          />
        </svg>

        {/* Dots for New Patients */}
        <div className="bg-chart-1 absolute bottom-[20%] left-0 h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[25%] left-[20%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[30%] left-[40%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[35%] left-[60%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[40%] left-[80%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute right-0 bottom-[45%] h-1.5 w-1.5 rounded-full"></div>

        {/* Dots for Returning Patients */}
        <div className="bg-chart-3 absolute bottom-[50%] left-0 h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[52%] left-[20%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[55%] left-[40%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[60%] left-[60%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[65%] left-[80%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute right-0 bottom-[70%] h-1.5 w-1.5 rounded-full"></div>
      </div>

      <div className="text-muted-foreground absolute right-0 bottom-0 left-0 flex justify-between text-[10px]">
        <span>Jul</span>
        <span>Aug</span>
        <span>Sep</span>
        <span>Oct</span>
        <span>Nov</span>
        <span>Dec</span>
      </div>
    </div>
  )
}

function RevenuePerPatientChart() {
  return (
    <div className="relative h-full w-full">
      <div className="absolute inset-0 flex flex-col justify-between">
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
      </div>

      <div className="relative h-full w-full">
        {/* Revenue Per Patient Line */}
        <svg
          className="absolute inset-0 -top-0.5 h-full w-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,60 L20,58 L40,55 L60,52 L80,48 L100,45"
            fill="none"
            stroke="var(--chart-1)"
            strokeWidth="2"
          />
        </svg>

        {/* Dots */}
        <div className="bg-chart-1 absolute bottom-[40%] left-0 h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[42%] left-[20%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[45%] left-[40%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[48%] left-[60%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[52%] left-[80%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute right-0 bottom-[55%] h-1.5 w-1.5 rounded-full"></div>
      </div>

      <div className="text-muted-foreground absolute right-0 bottom-0 left-0 flex justify-between text-[10px]">
        <span>Jul</span>
        <span>Aug</span>
        <span>Sep</span>
        <span>Oct</span>
        <span>Nov</span>
        <span>Dec</span>
      </div>
    </div>
  )
}

function GrowthForecastChart() {
  return (
    <div className="relative h-full w-full">
      <div className="absolute inset-0 flex flex-col justify-between">
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
        <div className="border-b border-dashed border-gray-200"></div>
      </div>

      <div className="relative h-full w-full">
        {/* Projected Growth Line */}
        <svg
          className="absolute inset-0 -top-0.5 h-full w-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,60 L20,55 L40,50 L60,45 L80,40 L100,35"
            fill="none"
            stroke="var(--chart-1)"
            strokeWidth="2"
          />
        </svg>

        {/* Conservative Estimate Line */}
        <svg
          className="absolute inset-0 -top-0.5 h-full w-full"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,65 L20,63 L40,60 L60,58 L80,55 L100,52"
            fill="none"
            stroke="var(--chart-3)"
            strokeWidth="2"
          />
        </svg>

        {/* Dots for Projected */}
        <div className="bg-chart-1 absolute bottom-[40%] left-0 h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[45%] left-[20%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[50%] left-[40%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[55%] left-[60%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute bottom-[60%] left-[80%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-1 absolute right-0 bottom-[65%] h-1.5 w-1.5 rounded-full"></div>

        {/* Dots for Conservative */}
        <div className="bg-chart-3 absolute bottom-[35%] left-0 h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[37%] left-[20%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[40%] left-[40%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[42%] left-[60%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute bottom-[45%] left-[80%] h-1.5 w-1.5 rounded-full"></div>
        <div className="bg-chart-3 absolute right-0 bottom-[48%] h-1.5 w-1.5 rounded-full"></div>
      </div>

      <div className="text-muted-foreground absolute right-0 bottom-0 left-0 flex justify-between text-[10px]">
        <span>Jan</span>
        <span>Feb</span>
        <span>Mar</span>
        <span>Apr</span>
        <span>May</span>
        <span>Jun</span>
      </div>
    </div>
  )
}
