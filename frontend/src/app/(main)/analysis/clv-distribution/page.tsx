"use client"

import React from "react"
import {
  Calendar,
  Download,
  Filter,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>hare2,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for CLV distribution
const clvDistribution = [
  { range: "$0-$500", count: 3250, percentage: 32.5 },
  { range: "$501-$1,000", count: 2180, percentage: 21.8 },
  { range: "$1,001-$2,500", count: 1850, percentage: 18.5 },
  { range: "$2,501-$5,000", count: 1420, percentage: 14.2 },
  { range: "$5,001-$10,000", count: 780, percentage: 7.8 },
  { range: "$10,001-$25,000", count: 420, percentage: 4.2 },
  { range: "$25,001+", count: 100, percentage: 1.0 },
]

// Mock data for CLV by patient segment
const clvBySegment = [
  {
    segment: "New Patients (< 1 year)",
    averageClv: 850,
    medianClv: 650,
    distribution: [
      { range: "$0-$500", percentage: 45 },
      { range: "$501-$1,000", percentage: 30 },
      { range: "$1,001-$2,500", percentage: 15 },
      { range: "$2,501-$5,000", percentage: 7 },
      { range: "$5,001+", percentage: 3 },
    ],
  },
  {
    segment: "Established Patients (1-3 years)",
    averageClv: 2250,
    medianClv: 1850,
    distribution: [
      { range: "$0-$500", percentage: 20 },
      { range: "$501-$1,000", percentage: 25 },
      { range: "$1,001-$2,500", percentage: 30 },
      { range: "$2,501-$5,000", percentage: 15 },
      { range: "$5,001+", percentage: 10 },
    ],
  },
  {
    segment: "Long-term Patients (3+ years)",
    averageClv: 5850,
    medianClv: 4200,
    distribution: [
      { range: "$0-$500", percentage: 10 },
      { range: "$501-$1,000", percentage: 15 },
      { range: "$1,001-$2,500", percentage: 25 },
      { range: "$2,501-$5,000", percentage: 30 },
      { range: "$5,001+", percentage: 20 },
    ],
  },
  {
    segment: "Chronic Condition Patients",
    averageClv: 8750,
    medianClv: 7200,
    distribution: [
      { range: "$0-$500", percentage: 5 },
      { range: "$501-$1,000", percentage: 10 },
      { range: "$1,001-$2,500", percentage: 15 },
      { range: "$2,501-$5,000", percentage: 25 },
      { range: "$5,001+", percentage: 45 },
    ],
  },
  {
    segment: "Elderly Patients (65+)",
    averageClv: 6250,
    medianClv: 5100,
    distribution: [
      { range: "$0-$500", percentage: 8 },
      { range: "$501-$1,000", percentage: 12 },
      { range: "$1,001-$2,500", percentage: 20 },
      { range: "$2,501-$5,000", percentage: 30 },
      { range: "$5,001+", percentage: 30 },
    ],
  },
]

// Mock data for CLV by department
const clvByDepartment = [
  {
    department: "Cardiology",
    averageClv: 7850,
    medianClv: 6200,
    topPercentile: 18500,
    patientCount: 1250,
    yearOverYearChange: "+12.5%",
  },
  {
    department: "Orthopedics",
    averageClv: 5250,
    medianClv: 4100,
    topPercentile: 12800,
    patientCount: 1850,
    yearOverYearChange: "+8.2%",
  },
  {
    department: "Neurology",
    averageClv: 6750,
    medianClv: 5300,
    topPercentile: 15200,
    patientCount: 980,
    yearOverYearChange: "+9.5%",
  },
  {
    department: "Pediatrics",
    averageClv: 3250,
    medianClv: 2800,
    topPercentile: 7500,
    patientCount: 2450,
    yearOverYearChange: "+5.8%",
  },
  {
    department: "Oncology",
    averageClv: 12500,
    medianClv: 9800,
    topPercentile: 28500,
    patientCount: 750,
    yearOverYearChange: "+15.2%",
  },
  {
    department: "Primary Care",
    averageClv: 2850,
    medianClv: 2200,
    topPercentile: 6800,
    patientCount: 5200,
    yearOverYearChange: "+4.2%",
  },
]

// Mock data for CLV insights
const clvInsights = [
  {
    title: "High-value patient identification",
    description:
      "5% of patients (500) generate 35% of total CLV, with an average CLV of $15,200.",
    recommendation:
      "Develop targeted retention programs for high-value patients, focusing on personalized care and premium services.",
    impact: "High",
  },
  {
    title: "Chronic condition patient value",
    description:
      "Patients with chronic conditions have 3.9x higher CLV than the average patient population.",
    recommendation:
      "Expand chronic care management programs and improve care coordination for these high-value patients.",
    impact: "High",
  },
  {
    title: "New patient conversion opportunity",
    description:
      "Only 25% of new patients transition to the established patient segment after one year.",
    recommendation:
      "Implement early engagement strategies to improve new patient retention and lifetime value potential.",
    impact: "Medium",
  },
  {
    title: "Department CLV variation",
    description:
      "Oncology patients have the highest CLV ($12,500 avg), while Primary Care patients have the lowest ($2,850 avg).",
    recommendation:
      "Analyze referral patterns between departments to optimize patient journeys and increase overall CLV.",
    impact: "Medium",
  },
]

const CLVDistribution = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Analysis", href: "/analysis" },
        { label: "CLV Distribution" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>CLV Distribution</CardTitle>
      <CardDescription>
        Analyze customer lifetime value distribution
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 3 Years
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" size="sm">
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Average CLV</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">$3,850</div>
          <p className="text-muted-foreground text-xs">+8.5% year over year</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Median CLV</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">$1,950</div>
          <p className="text-muted-foreground text-xs">+6.2% year over year</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Top 10% CLV</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">$12,500+</div>
          <p className="text-muted-foreground text-xs">+15.8% year over year</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">10,000</div>
          <p className="text-muted-foreground text-xs">+5.2% year over year</p>
        </CardContent>
      </Card>
    </div>

    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>CLV Distribution</CardTitle>
            <CardDescription>
              Distribution of patients by lifetime value range
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <LineChart className="mr-2 h-4 w-4" />
              View Trends
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {clvDistribution.map((range, index) => (
            <div key={index}>
              <div className="mb-1 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{range.range}</span>
                  <span className="text-muted-foreground text-xs">
                    {range.count.toLocaleString()} patients
                  </span>
                </div>
                <span className="text-xs">{range.percentage}%</span>
              </div>
              <Progress value={range.percentage} className="h-2" />
            </div>
          ))}
        </div>

        <div className="bg-primary/10 mt-8 rounded-md p-4">
          <h3 className="mb-2 text-sm font-medium">Distribution Analysis</h3>
          <p className="text-muted-foreground mb-4 text-sm">
            The CLV distribution shows a typical long-tail pattern, with a large
            number of patients in lower CLV ranges and fewer patients with very
            high CLV.
          </p>
          <ul className="space-y-2 text-sm">
            <li className="flex items-start gap-2">
              <span className="text-primary mt-0.5">•</span>
              <span>
                54.3% of patients have a CLV under $1,000, representing only
                18.2% of total revenue.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary mt-0.5">•</span>
              <span>
                The top 13% of patients (those with CLV over $5,000) generate
                52.5% of total revenue.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-primary mt-0.5">•</span>
              <span>
                The distribution has shifted slightly toward higher CLV ranges
                compared to last year, indicating improved patient retention and
                service utilization.
              </span>
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>

    <Tabs defaultValue="by-segment" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="by-segment">By Patient Segment</TabsTrigger>
        <TabsTrigger value="by-department">By Department</TabsTrigger>
        <TabsTrigger value="insights">CLV Insights</TabsTrigger>
      </TabsList>
      <TabsContent value="by-segment" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>CLV by Patient Segment</CardTitle>
            <CardDescription>
              Comparing CLV metrics across different patient segments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">
                      Patient Segment
                    </th>
                    <th className="p-2 text-right font-medium">Average CLV</th>
                    <th className="p-2 text-right font-medium">Median CLV</th>
                    <th className="p-2 text-center font-medium">
                      CLV Distribution
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {clvBySegment.map((segment, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{segment.segment}</td>
                      <td className="p-2 text-right">
                        ${segment.averageClv.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        ${segment.medianClv.toLocaleString()}
                      </td>
                      <td className="p-2">
                        <div className="flex h-6 w-full overflow-hidden rounded-full">
                          {segment.distribution.map((dist, distIndex) => (
                            <div
                              key={distIndex}
                              className={`h-full ${
                                distIndex === 0
                                  ? "bg-blue-200"
                                  : distIndex === 1
                                    ? "bg-blue-300"
                                    : distIndex === 2
                                      ? "bg-blue-400"
                                      : distIndex === 3
                                        ? "bg-blue-500"
                                        : "bg-blue-600"
                              }`}
                              style={{ width: `${dist.percentage}%` }}
                              title={`${dist.range}: ${dist.percentage}%`}
                            ></div>
                          ))}
                        </div>
                        <div className="mt-1 flex justify-between text-xs">
                          <span>$0</span>
                          <span>$5,000+</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Segment Comparison</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Chronic condition patients have the highest average CLV at
                      $8,750, which is 2.3x higher than the overall average.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      New patients have the lowest average CLV at $850,
                      highlighting the importance of retention strategies.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      The CLV increases significantly as patients transition
                      from new to established to long-term segments.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">
                  Distribution Patterns
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      New patients show a left-skewed distribution with 75%
                      having CLV under $1,000.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Chronic condition patients show a right-skewed
                      distribution with 45% having CLV over $5,000.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Elderly patients show a bimodal distribution, suggesting
                      two distinct utilization patterns within this segment.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="by-department" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>CLV by Department</CardTitle>
            <CardDescription>
              Comparing CLV metrics across different clinical departments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-right font-medium">Average CLV</th>
                    <th className="p-2 text-right font-medium">Median CLV</th>
                    <th className="p-2 text-right font-medium">Top 10% CLV</th>
                    <th className="p-2 text-right font-medium">
                      Patient Count
                    </th>
                    <th className="p-2 text-center font-medium">YoY Change</th>
                  </tr>
                </thead>
                <tbody>
                  {clvByDepartment.map((dept, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{dept.department}</td>
                      <td className="p-2 text-right">
                        ${dept.averageClv.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        ${dept.medianClv.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        ${dept.topPercentile.toLocaleString()}
                      </td>
                      <td className="p-2 text-right">
                        {dept.patientCount.toLocaleString()}
                      </td>
                      <td className="p-2 text-center text-green-500">
                        {dept.yearOverYearChange}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">
                  Department Analysis
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Oncology has the highest average CLV at $12,500, followed
                      by Cardiology at $7,850.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Primary Care has the lowest average CLV at $2,850, but the
                      highest patient count at 5,200.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      All departments show positive year-over-year CLV growth,
                      with Oncology leading at +15.2%.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Revenue Impact</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Despite having fewer patients, Oncology generates the
                      highest total CLV at $9.4M due to high per-patient value.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      Primary Care generates $14.8M in total CLV due to high
                      patient volume, making it the largest contributor overall.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary mt-0.5">•</span>
                    <span>
                      The gap between median and top 10% CLV is largest in
                      Oncology, indicating high variability in treatment costs.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="insights" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>CLV Insights & Recommendations</CardTitle>
            <CardDescription>
              Key insights and optimization opportunities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {clvInsights.map((insight, index) => (
                <div
                  key={index}
                  className="border-b pb-4 last:border-0 last:pb-0"
                >
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          insight.impact === "High"
                            ? "bg-red-100 text-red-700"
                            : insight.impact === "Medium"
                              ? "bg-amber-100 text-amber-700"
                              : "bg-blue-100 text-blue-700"
                        }`}
                      >
                        {insight.impact} Impact
                      </span>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">{insight.title}</h3>
                    <p className="text-muted-foreground mb-2 text-sm">
                      {insight.description}
                    </p>
                    <h4 className="text-sm font-medium">Recommendation</h4>
                    <p className="text-muted-foreground text-sm">
                      {insight.recommendation}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button className="w-full">
              <Users className="mr-2 h-4 w-4" />
              Generate CLV Optimization Plan
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default CLVDistribution
