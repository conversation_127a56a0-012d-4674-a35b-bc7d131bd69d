"use client"

import React, { useEffect, useState } from "react"
import {
  ArrowDown,
  ArrowUp,
  ChevronRight,
  Download,
  Edit3,
  Plus,
  Save,
  Share2,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Breadcrumb } from "@/contexts/breadcrumb"

import { CustomCategoryDialog } from "./components/custom-category-dialog"
import { DistributionTable } from "./components/distribution-table"
import { mockKPIs } from "./data/mock-data"
import {
  allCategories,
  allUnits,
  breadcrumbItems,
  nestedUnits,
} from "./data/nested-mock-data"
import {
  CategoryDefinition,
  CustomUnitDefinition,
  DistributionUnit,
  KPI,
  NavigationState,
} from "./types"

// Utility functions for formatting
function formatCurrency(value: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  }).format(value)
}

function formatNumber(value: number): string {
  return new Intl.NumberFormat("en-US").format(value)
}

function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`
}

function formatKPIValue(value: number, unit: string): string {
  switch (unit) {
    case "CURRENCY":
      return formatCurrency(value)
    case "NUMBER":
      return formatNumber(value)
    case "PERCENTAGE":
      return formatPercentage(value)
    default:
      return String(value)
  }
}

export default function KPIPage() {
  // State management
  const [selectedKPI, setSelectedKPI] = useState<KPI>(mockKPIs[0])
  const [calculationMode, setCalculationMode] = useState<"auto" | "manual">(
    "auto"
  )
  const [isEditing, setIsEditing] = useState<string | null>(null)
  const [editedTarget, setEditedTarget] = useState<string>("")
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [showDistribution, setShowDistribution] = useState(false)
  const [customDialogOpen, setCustomDialogOpen] = useState(false)
  const [availableCategories, setAvailableCategories] =
    useState<CategoryDefinition[]>(allCategories)
  const [selectedCategoryType, setSelectedCategoryType] =
    useState<string>("unit")

  // Navigation state for multi-level drill-down
  const [navigationState, setNavigationState] = useState<NavigationState>({
    kpiId: null,
    unitId: null,
    type: null,
    path: [],
    depth: 0,
  })

  // Units to display based on current navigation depth
  const [currentUnits, setCurrentUnits] =
    useState<DistributionUnit[]>(nestedUnits)

  // Update current units when navigation changes
  useEffect(() => {
    if (navigationState.depth === 0 || navigationState.path.length === 0) {
      // Top level - show root units
      setCurrentUnits(nestedUnits)
      return
    }

    // Find the current unit
    const currentUnitId = navigationState.path[navigationState.path.length - 1]
    const unit = allUnits.find((u) => u.id === currentUnitId)

    if (unit && unit.children && unit.children.length > 0) {
      setCurrentUnits(unit.children)
    } else {
      // No children, show empty array
      setCurrentUnits([])
    }
  }, [navigationState])

  // Handle drill-down navigation
  const handleDrillDown = (unitId: string) => {
    const unit = allUnits.find((u) => u.id === unitId)
    if (!unit) return

    setNavigationState({
      kpiId: selectedKPI.id,
      unitId: unit.id,
      type: unit.type,
      path: unit.path || [unit.id],
      depth: unit.depth || 1,
      currentNodeName: unit.name,
      currentNodeAllocation: unit.allocation,
      currentNodeUnit: selectedKPI.unit,
    })
  }

  // Handle breadcrumb navigation
  const handleBreadcrumbNavigation = (path: string[], depth: number) => {
    if (depth === 0) {
      // Reset to top level
      setNavigationState({
        kpiId: null,
        unitId: null,
        type: null,
        path: [],
        depth: 0,
      })
      setShowDistribution(false)
      return
    }

    const unitId = path[path.length - 1]
    const unit = allUnits.find((u) => u.id === unitId)

    if (!unit) return

    setNavigationState({
      kpiId: selectedKPI.id,
      unitId: unit.id,
      type: unit.type,
      path,
      depth,
      currentNodeName: unit.name,
      currentNodeAllocation: unit.allocation,
      currentNodeUnit: selectedKPI.unit,
    })
  }

  // Redistribute allocations based on new target
  const redistributeAllocations = (newTarget: number) => {
    // In real implementation, would update the units recursively
    // For now, simplified to illustrate the concept
    console.log("Auto-redistributing allocations for target:", newTarget)
  }

  // Handle unit allocation change
  const handleAllocationChange = (unitId: string, value: string) => {
    const numericValue = parseFloat(value.replace(/[^0-9.]/g, ""))
    if (isNaN(numericValue)) return

    // In a real implementation, this would recursively update the allocation
    // across the nested structure while maintaining constraints
    console.log("Updating allocation for unit:", unitId, "to", numericValue)

    // Set to manual mode when manually editing
    setCalculationMode("manual")
  }

  // Handle unit percentage change
  const handlePercentageChange = (unitId: string, value: string) => {
    const numericValue = parseFloat(value.replace(/[^0-9.]/g, ""))
    if (isNaN(numericValue)) return

    // In a real implementation, this would recursively update percentages
    // across the nested structure
    console.log("Updating percentage for unit:", unitId, "to", numericValue)

    // Set to manual mode when manually editing
    setCalculationMode("manual")
  }

  // Toggle unit lock
  const handleToggleLock = (unitId: string) => {
    // In a real implementation, this would recursively update the lock status
    console.log("Toggling lock for unit:", unitId)
  }

  // Start editing a KPI target
  const startEditing = (kpi: KPI) => {
    setIsEditing(kpi.id)
    setEditedTarget(kpi.target.toString())
  }

  // Save edited KPI target
  const saveKPITarget = (kpiId: string) => {
    const numericValue = parseFloat(editedTarget.replace(/[^0-9.]/g, ""))

    if (isNaN(numericValue)) {
      setIsEditing(null)
      return
    }

    // In a real app, you would save this to your backend
    // For now, we'll just update the selected KPI if needed
    if (selectedKPI.id === kpiId) {
      setSelectedKPI({ ...selectedKPI, target: numericValue })
    }

    setIsEditing(null)
  }

  // Handle adding a new custom category
  const handleSaveCustomCategory = (
    categoryData: Omit<CustomUnitDefinition, "id" | "createdAt">
  ) => {
    const newCategory: CategoryDefinition = {
      type: categoryData.type,
      label: categoryData.name,
      description: categoryData.description,
      isCustom: true,
      createdBy: categoryData.createdBy,
      createdAt: new Date().toISOString(),
      icon: "folder", // Default icon
    }

    setAvailableCategories([...availableCategories, newCategory])
  }

  return (
    <div className="space-y-4">
      <Breadcrumb links={[{ label: "KPIs" }]} />
      {/* Page header with title and actions */}

      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-1.5">
          <CardTitle>KPI Planning</CardTitle>
          <CardDescription>
            Manage and distribute KPI targets across organization units
          </CardDescription>
        </div>

        <div className="flex items-center gap-2">
          {navigationState.depth > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBreadcrumbNavigation([], 0)}
              className="h-9"
            >
              Back to KPI List
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <Plus className="mr-2 h-4 w-4" />
                Add New
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onSelect={() => console.log("Add new KPI")}>
                New KPI
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => console.log("Add distribution unit")}
              >
                New Distribution Unit
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => setCustomDialogOpen(true)}>
                New Custom Category
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Main content */}
      {navigationState.depth === 0 ? (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>KPI Targets</CardTitle>
                <CardDescription>
                  Define top-level KPI targets for your organization
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="h-8">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px] font-medium">KPI</TableHead>
                  <TableHead className="w-[250px] font-medium">
                    Description
                  </TableHead>
                  <TableHead className="font-medium">Target</TableHead>
                  <TableHead className="font-medium">Achievement</TableHead>
                  <TableHead className="font-medium">Previous</TableHead>
                  <TableHead className="font-medium">Change</TableHead>
                  <TableHead className="text-center font-medium">
                    Status
                  </TableHead>
                  <TableHead className="text-right font-medium">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockKPIs.map((kpi) => {
                  const change = kpi.previousTarget
                    ? ((kpi.target - kpi.previousTarget) / kpi.previousTarget) *
                      100
                    : 0
                  return (
                    <TableRow
                      key={kpi.id}
                      className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50"
                    >
                      <TableCell className="font-medium">{kpi.name}</TableCell>
                      <TableCell className="text-neutral-500">
                        {kpi.description}
                      </TableCell>
                      <TableCell>
                        {isEditing === kpi.id ? (
                          <div className="flex items-center gap-2">
                            <Input
                              type="text"
                              value={editedTarget}
                              onChange={(e) => setEditedTarget(e.target.value)}
                              className="h-8 w-32 text-sm tabular-nums"
                              autoFocus
                            />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 text-emerald-600"
                              onClick={() => saveKPITarget(kpi.id)}
                            >
                              <Save className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        ) : (
                          <span className="text-sm font-medium tabular-nums">
                            {formatKPIValue(kpi.target, kpi.unit)}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        {kpi.actual ? (
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-full max-w-[100px] rounded-full bg-neutral-200 dark:bg-neutral-700">
                              <div
                                className={`h-2 rounded-full ${kpi.actual / kpi.target > 0.8 ? "bg-emerald-500" : kpi.actual / kpi.target > 0.5 ? "bg-amber-500" : "bg-rose-500"}`}
                                style={{
                                  width: `${Math.min(100, (kpi.actual / kpi.target) * 100)}%`,
                                }}
                              ></div>
                            </div>
                            <span className="text-sm tabular-nums">
                              {((kpi.actual / kpi.target) * 100).toFixed(1)}%
                            </span>
                          </div>
                        ) : (
                          "–"
                        )}
                      </TableCell>
                      <TableCell className="text-sm text-neutral-500 tabular-nums">
                        {kpi.previousTarget
                          ? formatKPIValue(kpi.previousTarget, kpi.unit)
                          : "–"}
                      </TableCell>
                      <TableCell>
                        {kpi.previousTarget ? (
                          <div
                            className={`flex items-center ${change >= 0 ? "text-emerald-600" : "text-rose-600"}`}
                          >
                            {change >= 0 ? (
                              <ArrowUp className="mr-1 h-3 w-3" />
                            ) : (
                              <ArrowDown className="mr-1 h-3 w-3" />
                            )}
                            <span className="text-sm tabular-nums">
                              {Math.abs(change).toFixed(1)}%
                            </span>
                          </div>
                        ) : (
                          "–"
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                            kpi.status === "approved"
                              ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-800/20 dark:text-emerald-400"
                              : kpi.status === "rejected"
                                ? "bg-rose-100 text-rose-800 dark:bg-rose-800/20 dark:text-rose-400"
                                : "bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-400"
                          }`}
                        >
                          {kpi.status === "approved"
                            ? "Approved"
                            : kpi.status === "rejected"
                              ? "Rejected"
                              : "Pending"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => startEditing(kpi)}
                          >
                            <Edit3 className="h-3.5 w-3.5" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => {
                              setSelectedKPI(kpi)
                              setNavigationState({
                                kpiId: kpi.id,
                                unitId: null,
                                type: "unit", // Default to unit view
                                path: [],
                                depth: 1,
                              })
                              setCurrentUnits(nestedUnits)
                            }}
                          >
                            <ChevronRight className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader className="pb-0">
            <div className="flex flex-col space-y-4">
              {/* KPI details header */}
              <div className="flex items-start justify-between border-b pb-4">
                <div>
                  <div className="mb-2 flex items-center gap-3">
                    <h2 className="text-xl font-semibold tracking-tight">
                      {selectedKPI.name}
                    </h2>
                    {selectedKPI.status && (
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                          selectedKPI.status === "approved"
                            ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-800/20 dark:text-emerald-400"
                            : selectedKPI.status === "rejected"
                              ? "bg-rose-100 text-rose-800 dark:bg-rose-800/20 dark:text-rose-400"
                              : "bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-400"
                        }`}
                      >
                        {selectedKPI.status === "approved"
                          ? "Approved"
                          : selectedKPI.status === "rejected"
                            ? "Rejected"
                            : "Pending"}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-neutral-500">
                    {selectedKPI.description}
                  </p>
                  <div className="mt-4 grid grid-cols-3 gap-8">
                    <div>
                      <h3 className="text-sm font-medium text-neutral-500">
                        Target
                      </h3>
                      <p className="text-xl font-semibold tabular-nums">
                        {formatKPIValue(selectedKPI.target, selectedKPI.unit)}
                      </p>
                    </div>
                    {selectedKPI.actual && (
                      <div>
                        <h3 className="text-sm font-medium text-neutral-500">
                          Current Achievement
                        </h3>
                        <div className="flex items-center gap-3">
                          <p className="text-xl font-semibold tabular-nums">
                            {formatKPIValue(
                              selectedKPI.actual,
                              selectedKPI.unit
                            )}
                          </p>
                          <div className="flex flex-col">
                            <span className="text-sm tabular-nums">
                              {(
                                (selectedKPI.actual / selectedKPI.target) *
                                100
                              ).toFixed(1)}
                              %
                            </span>
                            <div className="mt-1 h-1.5 w-full min-w-[80px] rounded-full bg-neutral-200 dark:bg-neutral-700">
                              <div
                                className={`h-1.5 rounded-full ${selectedKPI.actual / selectedKPI.target > 0.8 ? "bg-emerald-500" : selectedKPI.actual / selectedKPI.target > 0.5 ? "bg-amber-500" : "bg-rose-500"}`}
                                style={{
                                  width: `${Math.min(100, (selectedKPI.actual / selectedKPI.target) * 100)}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    {selectedKPI.previousTarget && (
                      <div>
                        <h3 className="text-sm font-medium text-neutral-500">
                          Previous Period
                        </h3>
                        <p className="text-xl font-semibold tabular-nums">
                          {formatKPIValue(
                            selectedKPI.previousTarget,
                            selectedKPI.unit
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant={calculationMode === "auto" ? "default" : "outline"}
                    size="sm"
                    className="h-8"
                    onClick={() => {
                      setCalculationMode("auto")
                      redistributeAllocations(selectedKPI.target)
                    }}
                  >
                    Auto-distribute
                  </Button>
                  <Button variant="outline" size="sm" className="h-8">
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </Button>
                </div>
              </div>

              {/* Category tabs */}
              <div className="flex items-center border-b">
                <div className="-mb-px flex">
                  {availableCategories.map((category) => (
                    <button
                      key={category.type}
                      onClick={() => setSelectedCategoryType(category.type)}
                      className={`border-b-2 px-4 py-2 text-sm font-medium ${
                        selectedCategoryType === category.type
                          ? "border-neutral-900 text-neutral-900 dark:border-neutral-100 dark:text-neutral-100"
                          : "border-transparent text-neutral-500 hover:border-neutral-300 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300"
                      }`}
                    >
                      {category.label}
                    </button>
                  ))}
                  <button
                    onClick={() => setCustomDialogOpen(true)}
                    className="border-b-2 border-transparent px-4 py-2 text-sm font-medium text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300"
                    aria-label="Add custom category"
                  >
                    <Plus className="h-3.5 w-3.5" />
                  </button>
                </div>
              </div>
            </div>

            <CardDescription className="pt-2 pb-1 text-sm">
              {navigationState.path.length > 0 && (
                <>
                  {/* Contextual description based on navigation */}
                  {navigationState.path.map((pathId, index) => {
                    const unit = allUnits.find((u) => u.id === pathId)
                    if (index !== navigationState.path.length - 1) return null
                    return unit ? (
                      <React.Fragment key={pathId}>
                        Distribution for{" "}
                        <span className="font-medium">{unit.name}</span>
                      </React.Fragment>
                    ) : null
                  })}
                </>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Distribution table with embedded breadcrumbs and context-aware header */}
            <DistributionTable
              selectedKPI={selectedKPI}
              units={currentUnits}
              canDrillDown={true}
              categoryType={selectedCategoryType}
              categoryLabel={
                availableCategories.find(
                  (cat) => cat.type === selectedCategoryType
                )?.label || "Distribution"
              }
              navigationPath={navigationState.path.map(
                (id) =>
                  breadcrumbItems.find((item) => item.id === id) || {
                    id,
                    name: id,
                  }
              )}
              currentNodeAllocation={navigationState.currentNodeAllocation}
              onDrillDown={handleDrillDown}
              onNavigate={handleBreadcrumbNavigation}
              onAllocationChange={handleAllocationChange}
              onPercentageChange={handlePercentageChange}
              onToggleLock={handleToggleLock}
              calculationMode={calculationMode}
            />

            {/* Empty state for leaf nodes */}
            {currentUnits.length === 0 && (
              <div className="py-12 text-center">
                <p className="text-neutral-500">
                  No further distribution units at this level.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => {
                    // Go back one level
                    if (navigationState.path.length > 1) {
                      const newPath = navigationState.path.slice(0, -1)
                      const newDepth = navigationState.depth - 1
                      handleBreadcrumbNavigation(newPath, newDepth)
                    }
                  }}
                >
                  Go Back
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Footer with metadata and actions */}
      <div className="mt-4 flex items-center justify-between text-xs text-neutral-500">
        <div>
          Last updated: {new Date().toLocaleDateString()} | Created by:
          Administrator
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-8 text-xs">
            <Share2 className="mr-2 h-3.5 w-3.5" />
            Share Plan
          </Button>
          <Button variant="outline" size="sm" className="h-8 text-xs">
            <Download className="mr-2 h-3.5 w-3.5" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Dialog for custom category creation */}
      <CustomCategoryDialog
        open={customDialogOpen}
        onOpenChange={setCustomDialogOpen}
        onSave={handleSaveCustomCategory}
      />
    </div>
  )
}
