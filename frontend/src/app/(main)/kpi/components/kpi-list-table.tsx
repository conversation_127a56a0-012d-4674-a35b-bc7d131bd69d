import React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Edit3, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { KPI } from "../types"
import { calculatePercentageChange, formatKPIValue } from "../utils"

interface KPIListTableProps {
  kpis: KPI[]
  onSelectKPI: (kpi: KPI) => void
  onEditTarget: (kpiId: string, target: string) => void
  isEditing: string | null
  editedTarget: string
  setIsEditing: (id: string | null) => void
  setEditedTarget: (value: string) => void
}

export function KPIListTable({
  kpis,
  onSelectKPI,
  onEditTarget,
  isEditing,
  editedTarget,
  setIsEditing,
  setEditedTarget,
}: KPIListTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow className="bg-slate-50 dark:bg-slate-800/50">
          <TableHead className="w-[180px]">KPI</TableHead>
          <TableHead className="w-[250px]">Description</TableHead>
          <TableHead>Target</TableHead>
          <TableHead>Achievement</TableHead>
          <TableHead>Previous</TableHead>
          <TableHead>Change</TableHead>
          <TableHead className="text-center">Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {kpis.map((kpi) => {
          const change = kpi.previousTarget
            ? calculatePercentageChange(kpi.target, kpi.previousTarget)
            : 0

          return (
            <TableRow key={kpi.id}>
              <TableCell className="font-medium">{kpi.name}</TableCell>
              <TableCell>{kpi.description}</TableCell>
              <TableCell>
                {isEditing === kpi.id ? (
                  <Input
                    value={editedTarget}
                    onChange={(e) => setEditedTarget(e.target.value)}
                    onBlur={() => {
                      onEditTarget(kpi.id, editedTarget)
                      setIsEditing(null)
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        onEditTarget(kpi.id, editedTarget)
                        setIsEditing(null)
                      }
                    }}
                    className="h-8 w-32"
                    autoFocus
                  />
                ) : (
                  <span
                    className="inline-block cursor-pointer rounded px-2 py-1 hover:bg-slate-100 dark:hover:bg-slate-800"
                    onClick={() => {
                      setIsEditing(kpi.id)
                      setEditedTarget(kpi.target.toString())
                    }}
                  >
                    {formatKPIValue(kpi.target, kpi.unit)}
                  </span>
                )}
              </TableCell>
              <TableCell>
                {kpi.actual ? (
                  <div className="flex items-center gap-2">
                    <div className="h-2.5 w-full max-w-[100px] rounded-full bg-slate-200 dark:bg-slate-700">
                      <div
                        className={`h-2.5 rounded-full ${kpi.actual / kpi.target > 0.8 ? "bg-emerald-500" : kpi.actual / kpi.target > 0.5 ? "bg-amber-500" : "bg-rose-500"}`}
                        style={{
                          width: `${Math.min(100, (kpi.actual / kpi.target) * 100)}%`,
                        }}
                      ></div>
                    </div>
                    <span className="text-sm tabular-nums">
                      {((kpi.actual / kpi.target) * 100).toFixed(1)}%
                    </span>
                  </div>
                ) : (
                  "–"
                )}
              </TableCell>
              <TableCell className="text-muted-foreground tabular-nums">
                {kpi.previousTarget
                  ? formatKPIValue(kpi.previousTarget, kpi.unit)
                  : "–"}
              </TableCell>
              <TableCell>
                {kpi.previousTarget ? (
                  <div
                    className={`flex items-center ${change >= 0 ? "text-emerald-600" : "text-rose-600"}`}
                  >
                    {change >= 0 ? (
                      <ArrowUp className="mr-1 h-3 w-3" />
                    ) : (
                      <ArrowDown className="mr-1 h-3 w-3" />
                    )}
                    <span className="tabular-nums">
                      {Math.abs(change).toFixed(1)}%
                    </span>
                  </div>
                ) : (
                  "–"
                )}
              </TableCell>
              <TableCell className="text-center">
                {kpi.status && (
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      kpi.status === "approved"
                        ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-800/20 dark:text-emerald-400"
                        : kpi.status === "rejected"
                          ? "bg-rose-100 text-rose-800 dark:bg-rose-800/20 dark:text-rose-400"
                          : "bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-400"
                    }`}
                  >
                    {kpi.status === "approved"
                      ? "Approved"
                      : kpi.status === "rejected"
                        ? "Rejected"
                        : "Pending"}
                  </span>
                )}
              </TableCell>
              <TableCell className="space-x-1 text-right">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onSelectKPI(kpi)}
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          )
        })}
      </TableBody>
    </Table>
  )
}
