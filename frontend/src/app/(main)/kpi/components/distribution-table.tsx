import React from "react"
import {
  <PERSON><PERSON><PERSON>,
  ArrowUp,
  ChevronRight,
  Edit,
  Home,
  Lock,
  <PERSON>O<PERSON>,
  Trash2,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { DistributionUnit, KPI } from "../types"
import { formatKPIValue } from "../utils"

interface BreadcrumbItem {
  id: string
  name: string
  type?: string
}

interface DistributionTableProps {
  selectedKPI: KPI
  units: DistributionUnit[]
  canDrillDown?: boolean
  categoryType?: string
  categoryLabel: string
  navigationPath: BreadcrumbItem[]
  currentNodeAllocation?: number // The allocation amount for the current node
  onDrillDown: (unitId: string) => void
  onNavigate: (path: string[], depth: number) => void
  onAllocationChange: (unitId: string, value: string) => void
  onPercentageChange: (unitId: string, value: string) => void
  onToggleLock: (unitId: string) => void
  calculationMode: "auto" | "manual"
}

export function DistributionTable({
  selectedKPI,
  units,
  canDrillDown = false,
  categoryLabel,
  navigationPath,
  currentNodeAllocation,
  onDrillDown,
  onNavigate,
  onAllocationChange,
  onPercentageChange,
  onToggleLock,
  calculationMode,
}: DistributionTableProps) {
  // Function to get partial path for breadcrumb navigation
  const getPartialPath = (index: number) => {
    return navigationPath.slice(0, index + 1).map((item) => item.id)
  }

  return (
    <div className="space-y-3">
      {/* Embedded breadcrumb navigation */}
      {navigationPath.length > 0 && (
        <div className="flex items-center px-1 pb-2 text-sm">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onNavigate([], 0)}
            className="h-7 p-0 hover:bg-transparent"
          >
            <Home className="mr-1 h-3.5 w-3.5" />
            <span>KPI List</span>
          </Button>

          {navigationPath.map((item, index) => (
            <React.Fragment key={item.id}>
              <ChevronRight className="mx-1 h-4 w-4 text-neutral-400" />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const path = getPartialPath(index)
                  onNavigate(path, index + 1)
                }}
                className={`h-7 p-0 hover:bg-transparent ${index === navigationPath.length - 1 ? "font-medium text-neutral-900 dark:text-neutral-100" : "text-neutral-500"}`}
              >
                {item.name}
              </Button>
            </React.Fragment>
          ))}
        </div>
      )}

      {/* Table header showing current context */}
      <div className="pb-2">
        <h3 className="text-base font-medium">
          {navigationPath.length > 0
            ? `${navigationPath[navigationPath.length - 1].name} - ${categoryLabel}`
            : `${categoryLabel}`}
        </h3>
        <p className="text-sm text-neutral-500">
          {navigationPath.length > 0
            ? `Distribution of ${formatKPIValue(currentNodeAllocation || selectedKPI.target, selectedKPI.unit)} across ${navigationPath[navigationPath.length - 1].name}`
            : `Distribution of ${formatKPIValue(selectedKPI.target, selectedKPI.unit)} by ${categoryLabel.toLowerCase()}`}
        </p>
        {navigationPath.length > 0 && currentNodeAllocation && (
          <div className="mt-1 flex items-center gap-2">
            <span className="text-sm text-neutral-500">
              Current allocation:{" "}
              <span className="font-medium tabular-nums">
                {formatKPIValue(currentNodeAllocation, selectedKPI.unit)}
              </span>{" "}
              <span className="text-neutral-400">
                (
                {((currentNodeAllocation / selectedKPI.target) * 100).toFixed(
                  1
                )}
                % of {formatKPIValue(selectedKPI.target, selectedKPI.unit)}{" "}
                total)
              </span>
            </span>
          </div>
        )}
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px] font-medium">Name</TableHead>
            <TableHead className="font-medium">Allocation</TableHead>
            <TableHead className="font-medium">% of Total</TableHead>
            <TableHead className="font-medium">Previous</TableHead>
            <TableHead className="font-medium">Change</TableHead>
            <TableHead className="text-right font-medium">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {units.map((unit) => {
            const yearOverYearChange =
              unit.previousAllocation > 0
                ? ((unit.allocation - unit.previousAllocation) /
                    unit.previousAllocation) *
                  100
                : 0

            return (
              <TableRow
                key={unit.id}
                className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50"
              >
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    {unit.name}
                    {canDrillDown && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        onClick={() => onDrillDown(unit.id)}
                      >
                        <ChevronRight className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="relative w-40">
                    <Input
                      type="text"
                      value={formatKPIValue(unit.allocation, selectedKPI.unit)}
                      onChange={(e) =>
                        onAllocationChange(unit.id, e.target.value)
                      }
                      className={`h-8 pr-10 text-sm tabular-nums ${unit.locked ? "bg-neutral-50 dark:bg-neutral-800/50" : ""}`}
                      disabled={unit.locked || calculationMode === "auto"}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <button
                        onClick={() => onToggleLock(unit.id)}
                        className="text-neutral-400 hover:text-neutral-500 dark:hover:text-neutral-300"
                        disabled={calculationMode === "auto"}
                      >
                        {unit.locked ? (
                          <Lock className="h-3.5 w-3.5" />
                        ) : (
                          <LockOpen className="h-3.5 w-3.5" />
                        )}
                      </button>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Input
                    type="text"
                    value={`${unit.percentage.toFixed(1)}`}
                    onChange={(e) =>
                      onPercentageChange(unit.id, e.target.value)
                    }
                    className={`h-8 w-20 text-right text-sm tabular-nums ${unit.locked ? "bg-neutral-50 dark:bg-neutral-800/50" : ""}`}
                    disabled={unit.locked || calculationMode === "auto"}
                  />
                </TableCell>
                <TableCell className="text-sm text-neutral-500 tabular-nums">
                  {formatKPIValue(unit.previousAllocation, selectedKPI.unit)}
                </TableCell>
                <TableCell>
                  <div
                    className={`flex items-center ${yearOverYearChange >= 0 ? "text-emerald-600" : "text-rose-600"}`}
                  >
                    {yearOverYearChange >= 0 ? (
                      <ArrowUp className="mr-1 h-3 w-3" />
                    ) : (
                      <ArrowDown className="mr-1 h-3 w-3" />
                    )}
                    <span className="text-sm tabular-nums">
                      {Math.abs(yearOverYearChange).toFixed(1)}%
                    </span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-1">
                    <Button variant="ghost" size="icon" className="h-7 w-7">
                      <Edit className="h-3.5 w-3.5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 text-neutral-400 hover:text-rose-600"
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                    </Button>
                    {canDrillDown && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={() => onDrillDown(unit.id)}
                      >
                        <ChevronRight className="h-3.5 w-3.5" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
