import React from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ChevronDown,
  Lock,
  <PERSON><PERSON><PERSON>,
  Save,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { categoryTotals } from "../data/mock-data"
import { CategoryType, DistributionUnit, KPI } from "../types"
import { formatKPIValue } from "../utils"

interface KPIDistributionProps {
  selectedKPI: KPI
  splitData: DistributionUnit[]
  handleAllocationChange: (unitId: string, value: string) => void
  handleLockToggle: (unitId: string) => void
  handleBackToList: () => void
  selectedSplitType: CategoryType
  setSelectedSplitType: (type: CategoryType) => void
  calculationMode: "auto" | "manual"
  setCalculationMode: (mode: "auto" | "manual") => void
  redistributeAllocations: (target: number) => void
}

export function KPIDistribution({
  selectedKPI,
  splitData,
  handleAllocationChange,
  handleLockToggle,
  handleBackToList,
  selectedSplitType,
  setSelectedSplitType,
  calculationMode,
  setCalculationMode,
  redistributeAllocations,
}: KPIDistributionProps) {
  return (
    <Card>
      <CardHeader className="pb-0">
        <div className="flex flex-col space-y-4">
          {/* Top section with KPI details */}
          <div className="flex items-start justify-between border-b pb-4">
            <div>
              <div className="mb-2 flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToList}
                  className="-ml-2 h-8"
                >
                  <ChevronDown className="mr-1 h-4 w-4 rotate-90" />
                  Back
                </Button>
                <h2 className="text-2xl font-bold tracking-tight">
                  {selectedKPI.name}
                </h2>
                {selectedKPI.status && (
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      selectedKPI.status === "approved"
                        ? "bg-emerald-100 text-emerald-800 dark:bg-emerald-800/20 dark:text-emerald-400"
                        : selectedKPI.status === "rejected"
                          ? "bg-rose-100 text-rose-800 dark:bg-rose-800/20 dark:text-rose-400"
                          : "bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-400"
                    }`}
                  >
                    {selectedKPI.status === "approved"
                      ? "Approved"
                      : selectedKPI.status === "rejected"
                        ? "Rejected"
                        : "Pending"}
                  </span>
                )}
              </div>
              <p className="text-muted-foreground">{selectedKPI.description}</p>
              <div className="mt-6 grid grid-cols-3 gap-8">
                <div>
                  <h3 className="text-muted-foreground text-sm font-medium">
                    Target
                  </h3>
                  <p className="text-2xl font-bold tabular-nums">
                    {formatKPIValue(selectedKPI.target, selectedKPI.unit)}
                  </p>
                </div>
                {selectedKPI.actual && (
                  <div>
                    <h3 className="text-muted-foreground text-sm font-medium">
                      Current Achievement
                    </h3>
                    <div className="mt-1 flex items-center gap-3">
                      <p className="text-2xl font-bold tabular-nums">
                        {formatKPIValue(selectedKPI.actual, selectedKPI.unit)}
                      </p>
                      <div className="flex flex-col">
                        <span className="text-sm tabular-nums">
                          {(
                            (selectedKPI.actual / selectedKPI.target) *
                            100
                          ).toFixed(1)}
                          %
                        </span>
                        <div className="mt-1 h-2 w-full min-w-[100px] rounded-full bg-slate-200 dark:bg-slate-700">
                          <div
                            className={`h-2 rounded-full ${selectedKPI.actual / selectedKPI.target > 0.8 ? "bg-emerald-500" : selectedKPI.actual / selectedKPI.target > 0.5 ? "bg-amber-500" : "bg-rose-500"}`}
                            style={{
                              width: `${Math.min(100, (selectedKPI.actual / selectedKPI.target) * 100)}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {selectedKPI.previousTarget && (
                  <div>
                    <h3 className="text-muted-foreground text-sm font-medium">
                      Previous Period
                    </h3>
                    <p className="text-2xl font-bold tabular-nums">
                      {formatKPIValue(
                        selectedKPI.previousTarget,
                        selectedKPI.unit
                      )}
                    </p>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={calculationMode === "auto" ? "default" : "outline"}
                size="sm"
                className="h-8"
                onClick={() => {
                  setCalculationMode("auto")
                  redistributeAllocations(selectedKPI.target)
                }}
              >
                Auto-distribute
              </Button>
              <Button variant="outline" size="sm" className="h-8">
                <Save className="mr-2 h-4 w-4" />
                Save
              </Button>
            </div>
          </div>

          {/* Distribution type tabs */}
          <div className="flex items-center border-b">
            <div className="-mb-px flex">
              {categoryTotals.map((category) => (
                <button
                  key={category.type}
                  onClick={() => setSelectedSplitType(category.type)}
                  className={`border-b-2 px-4 py-2 text-sm font-medium ${selectedSplitType === category.type ? "border-primary text-primary" : "text-muted-foreground hover:text-foreground border-transparent hover:border-slate-300"}`}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        <CardDescription className="pt-4">
          {selectedSplitType === "unit" &&
            "Distribution across hospital facilities"}
          {selectedSplitType === "doctor" &&
            "Distribution by physician specialty"}
          {selectedSplitType === "procedure" &&
            "Distribution by procedure classification"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 dark:bg-slate-800/50">
              <TableHead className="w-[250px]">Name</TableHead>
              <TableHead>Allocation</TableHead>
              <TableHead>Percentage</TableHead>
              <TableHead>YoY Change</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {splitData.map((unit) => {
              const yearOverYearChange =
                ((unit.allocation - unit.previousAllocation) /
                  unit.previousAllocation) *
                100

              return (
                <TableRow key={unit.id}>
                  <TableCell className="font-medium">{unit.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Input
                        type="text"
                        value={formatKPIValue(
                          unit.allocation,
                          selectedKPI.unit
                        )}
                        onChange={(e) =>
                          handleAllocationChange(unit.id, e.target.value)
                        }
                        disabled={unit.locked || calculationMode === "auto"}
                        className="h-8 w-32"
                      />
                      <span className="text-muted-foreground text-sm tabular-nums">
                        {((unit.allocation / selectedKPI.target) * 100).toFixed(
                          1
                        )}
                        %
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      value={unit.percentage}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value)
                        if (isNaN(value)) return
                        handleAllocationChange(
                          unit.id,
                          ((value / 100) * selectedKPI.target).toString()
                        )
                      }}
                      disabled={unit.locked || calculationMode === "auto"}
                      className="h-8 w-24"
                      min={0}
                      max={100}
                      step={0.1}
                    />
                  </TableCell>
                  <TableCell>
                    <div
                      className={`flex items-center ${yearOverYearChange >= 0 ? "text-emerald-600" : "text-rose-600"}`}
                    >
                      {yearOverYearChange >= 0 ? (
                        <ArrowUp className="mr-1 h-3 w-3" />
                      ) : (
                        <ArrowDown className="mr-1 h-3 w-3" />
                      )}
                      <span className="tabular-nums">
                        {Math.abs(yearOverYearChange).toFixed(1)}%
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleLockToggle(unit.id)}
                      disabled={calculationMode === "auto"}
                    >
                      {unit.locked ? (
                        <Lock className="h-4 w-4" />
                      ) : (
                        <LockOpen className="h-4 w-4" />
                      )}
                    </Button>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
