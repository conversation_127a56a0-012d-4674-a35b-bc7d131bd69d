import React from "react"
import { Download, Plus, Share2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface KPISummaryHeaderProps {
  showDistribution: boolean
  onAddKPI: () => void
}

export function KPISummaryHeader({
  showDistribution,
  onAddKPI,
}: KPISummaryHeaderProps) {
  if (showDistribution) return null

  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>KPI Planning</CardTitle>
          <CardDescription>
            Define and distribute KPI targets across the organization
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-8">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button
            variant="default"
            size="sm"
            className="h-8"
            onClick={onAddKPI}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add KPI
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="text-sm">
          <p>
            Plan and analyze KPIs for the current fiscal year. Use this
            dashboard to set organizational targets and distribute allocations
            across units.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
