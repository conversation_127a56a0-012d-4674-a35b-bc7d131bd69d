import React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

import { CustomUnitDefinition } from "../types"

interface CustomCategoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (category: Omit<CustomUnitDefinition, "id" | "createdAt">) => void
}

export function CustomCategoryDialog({
  open,
  onOpenChange,
  onSave,
}: CustomCategoryDialogProps) {
  const [name, setName] = React.useState("")
  const [type, setType] = React.useState("")
  const [description, setDescription] = React.useState("")

  const handleSave = () => {
    if (!name || !type) return

    onSave({
      name,
      type,
      description,
      createdBy: "Current User", // In a real app, would come from auth context
    })

    // Reset form
    setName("")
    setType("")
    setDescription("")
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Custom Category</DialogTitle>
          <DialogDescription>
            Define a new category for KPI distribution analysis.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
              placeholder="e.g., By Revenue Source"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="type" className="text-right">
              Type ID
            </Label>
            <Input
              id="type"
              value={type}
              onChange={(e) => setType(e.target.value)}
              className="col-span-3"
              placeholder="e.g., revenue_source"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              placeholder="Describe the purpose of this category"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button type="button" onClick={handleSave}>
            Save Category
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
