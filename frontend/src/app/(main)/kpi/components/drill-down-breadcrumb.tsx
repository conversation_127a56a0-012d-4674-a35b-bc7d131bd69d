import React from "react"
import { ChevronRight } from "lucide-react"

import { NavigationState } from "../types"

interface DrillDownBreadcrumbProps {
  navigationState: NavigationState
  onNavigate: (path: string[], depth: number) => void
  items: {
    id: string
    name: string
    type?: string
  }[]
}

export function DrillDownBreadcrumb({
  navigationState,
  onNavigate,
  items,
}: DrillDownBreadcrumbProps) {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 text-sm font-medium">
        <li className="inline-flex items-center">
          <button
            onClick={() => onNavigate([], 0)}
            className="inline-flex items-center text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-neutral-100"
          >
            KPI Planning
          </button>
        </li>

        {navigationState.path.map((path, index) => {
          const item = items.find((i) => i.id === path)
          const isLast = index === navigationState.path.length - 1

          if (!item) return null

          return (
            <React.Fragment key={path}>
              <li className="flex items-center">
                <ChevronRight className="h-4 w-4 text-neutral-400" />
              </li>
              <li>
                <button
                  onClick={() =>
                    onNavigate(
                      navigationState.path.slice(0, index + 1),
                      index + 1
                    )
                  }
                  className={`inline-flex items-center ${
                    isLast
                      ? "text-neutral-900 dark:text-neutral-100"
                      : "text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-neutral-100"
                  }`}
                  aria-current={isLast ? "page" : undefined}
                >
                  {item.name}
                </button>
              </li>
            </React.Fragment>
          )
        })}
      </ol>
    </nav>
  )
}
