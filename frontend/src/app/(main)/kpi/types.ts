// Basic category types (system provided)
export type CategoryType = "unit" | "doctor" | "procedure" | string

// KPI data structure
export interface KPI {
  id: string
  name: string
  description: string
  target: number
  actual?: number
  unit: string // CURRENCY, NUMBER, PERCENTAGE
  previousTarget?: number
  status?: "pending" | "approved" | "rejected"
  lastUpdated?: string
}

// Distribution unit definition (generic for any level)
export interface DistributionUnit {
  id: string
  name: string
  allocation: number
  percentage: number
  previousAllocation: number
  locked: boolean
  type: CategoryType
  // Support for drill-down to deeper levels
  children?: DistributionUnit[]
  // Track the path for navigation
  parentId?: string
  path?: string[]
  depth?: number
}

// Category definition with metadata
export interface CategoryDefinition {
  type: CategoryType
  label: string
  description?: string
  isCustom?: boolean
  createdBy?: string
  createdAt?: string
  icon?: string
}

// Category and its totals
export interface CategoryTotal {
  type: CategoryType
  label: string
  total: number
  percentage: number
}

// Full KPI distribution data
export interface KPIDistribution {
  kpiId: string
  units: DistributionUnit[]
}

// Navigation state for breadcrumb tracking
export interface NavigationState {
  kpiId: string | null
  unitId: string | null
  type: CategoryType | null
  path: string[]
  depth: number
  // Track node-specific values for better context awareness
  currentNodeName?: string
  currentNodeAllocation?: number
  currentNodeUnit?: string
}

// User-defined filter/view
export interface CustomUnitDefinition {
  id: string
  name: string
  type: string
  description: string
  createdBy: string
  createdAt: string
  rules?: Array<{
    field: string
    operator: "equals" | "contains" | "greater_than" | "less_than"
    value: string | number
  }> // Rules for unit membership
}
