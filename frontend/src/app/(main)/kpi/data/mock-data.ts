import { CategoryTotal, DistributionUnit, KPI } from "../types"

// Mock KPI data
export const mockKPIs: KPI[] = [
  {
    id: "revenue",
    name: "Total Revenue",
    description: "Annual revenue across all hospital units",
    target: 10000000,
    actual: 6750000,
    previousTarget: 9500000,
    unit: "CURRENCY",
    status: "approved",
    lastUpdated: "2025-04-15",
  },
  {
    id: "patients",
    name: "Patient Volume",
    description: "Total patients treated annually",
    target: 100000,
    actual: 62500,
    previousTarget: 97000,
    unit: "NUMBER",
    status: "pending",
    lastUpdated: "2025-05-01",
  },
  {
    id: "processing",
    name: "Processing Rate",
    description: "Case processing efficiency rate",
    target: 95,
    actual: 91,
    previousTarget: 92,
    unit: "PERCENTAGE",
    status: "approved",
    lastUpdated: "2025-04-30",
  },
]

// Hospital Units
export const mockHospitalUnits: DistributionUnit[] = [
  {
    id: "general",
    name: "General Campus",
    allocation: 4000000,
    percentage: 40,
    previousAllocation: 3800000,
    locked: false,
    type: "unit",
  },
  {
    id: "east",
    name: "East Wing",
    allocation: 2000000,
    percentage: 20,
    previousAllocation: 1950000,
    locked: false,
    type: "unit",
  },
  {
    id: "west",
    name: "West Wing",
    allocation: 1500000,
    percentage: 15,
    previousAllocation: 1400000,
    locked: false,
    type: "unit",
  },
  {
    id: "specialty",
    name: "Specialty Center",
    allocation: 1500000,
    percentage: 15,
    previousAllocation: 1350000,
    locked: false,
    type: "unit",
  },
  {
    id: "research",
    name: "Research Division",
    allocation: 1000000,
    percentage: 10,
    previousAllocation: 950000,
    locked: false,
    type: "unit",
  },
]

// Doctor splits
export const mockDoctorSplits: DistributionUnit[] = [
  {
    id: "dr-smith",
    name: "Dr. Smith (Cardiology)",
    allocation: 2500000,
    percentage: 25,
    previousAllocation: 2300000,
    locked: false,
    type: "doctor",
  },
  {
    id: "dr-patel",
    name: "Dr. Patel (Oncology)",
    allocation: 2000000,
    percentage: 20,
    previousAllocation: 1900000,
    locked: false,
    type: "doctor",
  },
  {
    id: "dr-johnson",
    name: "Dr. Johnson (Neurology)",
    allocation: 1800000,
    percentage: 18,
    previousAllocation: 1700000,
    locked: false,
    type: "doctor",
  },
  {
    id: "dr-williams",
    name: "Dr. Williams (Pediatrics)",
    allocation: 1500000,
    percentage: 15,
    previousAllocation: 1400000,
    locked: false,
    type: "doctor",
  },
  {
    id: "other-doctors",
    name: "Other Physicians",
    allocation: 2200000,
    percentage: 22,
    previousAllocation: 2200000,
    locked: false,
    type: "doctor",
  },
]

// Procedure Type splits
export const mockProcedureSplits: DistributionUnit[] = [
  {
    id: "type1",
    name: "Type 1 Procedures",
    allocation: 3500000,
    percentage: 35,
    previousAllocation: 3250000,
    locked: false,
    type: "procedure",
  },
  {
    id: "type2",
    name: "Type 2 Procedures",
    allocation: 2800000,
    percentage: 28,
    previousAllocation: 2600000,
    locked: false,
    type: "procedure",
  },
  {
    id: "type3",
    name: "Type 3 Procedures",
    allocation: 2200000,
    percentage: 22,
    previousAllocation: 2100000,
    locked: false,
    type: "procedure",
  },
  {
    id: "emergency",
    name: "Emergency Procedures",
    allocation: 1500000,
    percentage: 15,
    previousAllocation: 1550000,
    locked: false,
    type: "procedure",
  },
]

// Calculate totals for each category
export const categoryTotals: CategoryTotal[] = [
  {
    type: "unit",
    label: "Hospital Units",
    total: mockHospitalUnits.reduce((sum, unit) => sum + unit.allocation, 0),
    percentage: 100,
  },
  {
    type: "doctor",
    label: "Physician Distribution",
    total: mockDoctorSplits.reduce((sum, unit) => sum + unit.allocation, 0),
    percentage: 100,
  },
  {
    type: "procedure",
    label: "Procedure Types",
    total: mockProcedureSplits.reduce((sum, unit) => sum + unit.allocation, 0),
    percentage: 100,
  },
]
