import { CategoryDefinition, DistributionUnit, KPI } from "../types"

// Mock KPI data
export const mockKPIs: KPI[] = [
  {
    id: "revenue",
    name: "Total Revenue",
    description: "Annual revenue across all hospital units",
    target: 10000000,
    actual: 6750000,
    previousTarget: 9500000,
    unit: "CURRENCY",
    status: "approved",
    lastUpdated: "2025-04-15",
  },
  {
    id: "patients",
    name: "Patient Volume",
    description: "Total patients treated annually",
    target: 100000,
    actual: 62500,
    previousTarget: 97000,
    unit: "NUMBER",
    status: "pending",
    lastUpdated: "2025-05-01",
  },
  {
    id: "processing",
    name: "Processing Rate",
    description: "Case processing efficiency rate",
    target: 95,
    actual: 91,
    previousTarget: 92,
    unit: "PERCENTAGE",
    status: "approved",
    lastUpdated: "2025-04-30",
  },
]

// System provided category definitions
export const systemCategories: CategoryDefinition[] = [
  {
    type: "unit",
    label: "Hospital Units",
    description: "Distribution across hospital facilities and campuses",
    icon: "building",
  },
  {
    type: "doctor",
    label: "Physician Distribution",
    description: "Distribution by physician specialty and practice",
    icon: "stethoscope",
  },
  {
    type: "procedure",
    label: "Procedure Types",
    description: "Distribution by medical procedure classification",
    icon: "activity",
  },
]

// Custom user-defined categories
export const customCategories: CategoryDefinition[] = [
  {
    type: "department",
    label: "Departments",
    description: "Distribution by hospital departments",
    isCustom: true,
    createdBy: "Admin",
    createdAt: "2025-03-15",
    icon: "folder",
  },
  {
    type: "payment",
    label: "Payment Sources",
    description: "Distribution by payment and insurance types",
    isCustom: true,
    createdBy: "Financial Manager",
    createdAt: "2025-04-02",
    icon: "credit-card",
  },
]

// All categories combined
export const allCategories = [...systemCategories, ...customCategories]

// Hospital Units with nested structure for drill-down
export const nestedUnits: DistributionUnit[] = [
  {
    id: "general",
    name: "General Campus",
    allocation: 4000000,
    percentage: 40,
    previousAllocation: 3800000,
    locked: false,
    type: "unit",
    path: ["general"],
    depth: 1,
    children: [
      {
        id: "general-emergency",
        name: "Emergency Department",
        allocation: 1500000,
        percentage: 37.5,
        previousAllocation: 1400000,
        locked: false,
        type: "unit",
        parentId: "general",
        path: ["general", "general-emergency"],
        depth: 2,
        children: [
          {
            id: "general-emergency-trauma",
            name: "Trauma Center",
            allocation: 900000,
            percentage: 60,
            previousAllocation: 850000,
            locked: false,
            type: "unit",
            parentId: "general-emergency",
            path: ["general", "general-emergency", "general-emergency-trauma"],
            depth: 3,
          },
          {
            id: "general-emergency-triage",
            name: "Triage Unit",
            allocation: 600000,
            percentage: 40,
            previousAllocation: 550000,
            locked: false,
            type: "unit",
            parentId: "general-emergency",
            path: ["general", "general-emergency", "general-emergency-triage"],
            depth: 3,
          },
        ],
      },
      {
        id: "general-surgery",
        name: "Surgery Wing",
        allocation: 1600000,
        percentage: 40,
        previousAllocation: 1500000,
        locked: false,
        type: "unit",
        parentId: "general",
        path: ["general", "general-surgery"],
        depth: 2,
      },
      {
        id: "general-outpatient",
        name: "Outpatient Services",
        allocation: 900000,
        percentage: 22.5,
        previousAllocation: 900000,
        locked: false,
        type: "unit",
        parentId: "general",
        path: ["general", "general-outpatient"],
        depth: 2,
      },
    ],
  },
  {
    id: "east",
    name: "East Wing",
    allocation: 2000000,
    percentage: 20,
    previousAllocation: 1950000,
    locked: false,
    type: "unit",
    path: ["east"],
    depth: 1,
    children: [
      {
        id: "east-cardiology",
        name: "Cardiology Department",
        allocation: 1200000,
        percentage: 60,
        previousAllocation: 1150000,
        locked: false,
        type: "unit",
        parentId: "east",
        path: ["east", "east-cardiology"],
        depth: 2,
      },
      {
        id: "east-neurology",
        name: "Neurology Department",
        allocation: 800000,
        percentage: 40,
        previousAllocation: 800000,
        locked: false,
        type: "unit",
        parentId: "east",
        path: ["east", "east-neurology"],
        depth: 2,
      },
    ],
  },
  {
    id: "west",
    name: "West Wing",
    allocation: 1500000,
    percentage: 15,
    previousAllocation: 1400000,
    locked: false,
    type: "unit",
    path: ["west"],
    depth: 1,
  },
  {
    id: "specialty",
    name: "Specialty Center",
    allocation: 1500000,
    percentage: 15,
    previousAllocation: 1350000,
    locked: false,
    type: "unit",
    path: ["specialty"],
    depth: 1,
  },
  {
    id: "research",
    name: "Research Division",
    allocation: 1000000,
    percentage: 10,
    previousAllocation: 950000,
    locked: false,
    type: "unit",
    path: ["research"],
    depth: 1,
  },
]

// Helper function to flatten the nested structure for easier lookups
export function flattenUnits(units: DistributionUnit[]): DistributionUnit[] {
  let result: DistributionUnit[] = []

  for (const unit of units) {
    result.push(unit)
    if (unit.children && unit.children.length > 0) {
      result = [...result, ...flattenUnits(unit.children)]
    }
  }

  return result
}

// Flattened units for lookup
export const allUnits = flattenUnits(nestedUnits)

// Generate breadcrumb items from units
export const breadcrumbItems = allUnits.map((unit) => ({
  id: unit.id,
  name: unit.name,
  type: unit.type,
}))
