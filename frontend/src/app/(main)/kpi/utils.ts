// Format KPI values based on their unit type
export const formatKPIValue = (value: number, unit: string): string => {
  switch (unit) {
    case "CURRENCY":
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        maximumFractionDigits: 0,
      }).format(value)
    case "PERCENTAGE":
      return `${value}%`
    case "NUMBER":
    default:
      return new Intl.NumberFormat("en-US").format(value)
  }
}

// Calculate percentage change between two numbers
export const calculatePercentageChange = (
  current: number,
  previous: number
): number => {
  return ((current - previous) / previous) * 100
}
