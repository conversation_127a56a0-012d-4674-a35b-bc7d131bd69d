"use client"

import React, { useState } from "react"
import {
  Banknote,
  BarChart2,
  DollarSign,
  // Per<PERSON>,
  TrendingDown,
} from "lucide-react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  Treemap,
  XAxis,
  <PERSON>A<PERSON>s,
} from "recharts"

import { cn } from "@/lib/utils"
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import CashFlowDrilldownModal from "@/components/CashFlowDrilldownModal"
import DrilldownModal from "@/components/DrilldownModal"
import InfoTooltip from "@/components/InfoTooltip"
import TreemapDrilldownModal from "@/components/TreemapDrilldownModal"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Dashboard = () => (
  <>
    <Breadcrumb links={[{ label: "Dashboard" }]} />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Dashboard</CardTitle>
      <CardDescription>
        Welcome to OneFlow, your workflow management solution
      </CardDescription>
    </div>

    <div className="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-4">
      <MetricCard
        title="Cash Position"
        value="$3.3M"
        momChange="8.2%"
        ytdChange="15.6%"
        description="Cash position strengthened by 8.2% MoM, driven by improved collections and working capital management."
        icon={<DollarSign className="size-4 text-blue-600" />}
        iconColor="bg-blue-100"
        progressPercent={93}
        isPositive
      />
      <MetricCard
        title="Working Capital"
        value="$2.9M"
        momChange="5.4%"
        ytdChange="12.8%"
        description="Working capital efficiency improved. Inventory turnover increased to 8.2x."
        icon={<BarChart2 className="size-4 text-blue-600" />}
        iconColor="bg-blue-100"
        progressPercent={95}
        isPositive
      />
      <MetricCard
        title="Accounts Receivable"
        value="$1.9M"
        momChange="3.2%"
        ytdChange="8.5%"
        description="AR decreased through improved collection. DSO reduced to 38 days."
        icon={<TrendingDown className="size-4 text-blue-600" />}
        iconColor="bg-blue-100"
        progressPercent={109}
        isPositive={false}
      />
      <MetricCard
        title="Accounts Payable"
        value="$1.3M"
        momChange="2.1%"
        ytdChange="5.4%"
        description="AP well-managed with strategic payment timing. Discounts worth $25K captured."
        icon={<Banknote className="size-4 text-blue-600" />}
        iconColor="bg-blue-100"
        progressPercent={96}
        isPositive
      />
      <MetricCard
        title="Long-term Bank Debt"
        value="$4.8M"
        momChange="2.1%"
        ytdChange="8.4%"
        description="Debt reduced ahead of schedule. Coverage ratio improved to 3.2x."
        icon={<Banknote className="size-4 text-blue-600" />}
        iconColor="bg-blue-100"
        progressPercent={107}
        isPositive={false}
      />
      {/* <MetricCard
        title="Debt-to-Equity Ratio"
        value="42%"
        momChange="1.2%"
        ytdChange="4.5%"
        description="Improved to 0.42, approaching target. Credit rating outlook positive."
        icon={<Percent className="size-4 text-blue-600" />}
        iconColor="bg-blue-100"
        progressPercent={105}
        isPositive
      /> */}
    </div>

    <div className="grid grid-cols-1 items-start gap-4 lg:grid-cols-3">
      <IncomeStatementCard />

      <div className="flex flex-col gap-4 lg:col-span-2">
        <OperatingIncomeTrendCard />
        <TreemapCard />
        <CashFlowCard />
      </div>
    </div>
  </>
)

export default Dashboard

type MetricCardProps = {
  title: string
  value: string
  momChange: string
  ytdChange: string
  description: string
  icon: React.ReactNode
  iconColor: string
  progressPercent: number
  progressColor?: string
  isPositive?: boolean
}

const MetricCard = ({
  title,
  value,
  momChange,
  ytdChange,
  description,
  icon,
  iconColor,
  progressPercent,
  isPositive = true,
}: MetricCardProps) => {
  return (
    <Card>
      <CardContent className="flex flex-1 flex-col gap-2">
        <div className="flex items-start justify-between">
          <div className="flex flex-col gap-1">
            <CardDescription>{title}</CardDescription>
            <CardTitle className="text-2xl tabular-nums">{value}</CardTitle>
          </div>
          <div className={cn("rounded-md p-2", iconColor)}>{icon}</div>
        </div>

        <div className="flex items-center gap-2 text-sm">
          <span
            className={cn(
              "text-xs",
              isPositive ? "text-green-600" : "text-red-600"
            )}
          >
            {isPositive ? "▲" : "▼"} {momChange} MoM
          </span>
          <span
            className={cn(
              "text-xs",
              isPositive ? "text-green-600" : "text-red-600"
            )}
          >
            {isPositive ? "▲" : "▼"} {ytdChange} YTD
          </span>
        </div>

        <p className="text-muted-foreground text-xs">{description}</p>

        <div className="mt-auto flex items-center justify-between">
          <Progress value={progressPercent} className="h-2 flex-1" />
          <span className="text-muted-foreground ml-2 text-xs tabular-nums">
            {progressPercent}%
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

const IncomeStatementCard = () => {
  const revenueLastYear = 2.2 // in million dollars
  const revenueTarget = 2.7 // in million dollars
  const cogsLastYear = 1.0 // in million dollars
  const cogsTarget = 1.2 // in million dollars
  const operatingExpensesLastYear = 0.8 // in million dollars
  const operatingExpensesTarget = 0.9 // in million dollars
  const ebitdaLastYear = 1.5 // in million dollars
  const ebitdaTarget = 1.8 // in million dollars

  // Revenue changes (YoY & Target Comparison)
  const revenueGrowth = ((2.5 - revenueLastYear) / revenueLastYear) * 100
  const revenueVsTarget = ((2.5 - revenueTarget) / revenueTarget) * 100

  // Cost of Goods Sold changes (YoY & Target Comparison)
  const cogsGrowth = ((1.1 - cogsLastYear) / cogsLastYear) * 100
  const cogsVsTarget = ((1.1 - cogsTarget) / cogsTarget) * 100

  // Operating Expenses changes (YoY & Target Comparison)
  const operatingExpensesGrowth =
    ((0.856 - operatingExpensesLastYear) / operatingExpensesLastYear) * 100
  const operatingExpensesVsTarget =
    ((0.856 - operatingExpensesTarget) / operatingExpensesTarget) * 100

  // EBITDA changes (YoY & Target Comparison)
  const ebitdaGrowth = ((1.6 - ebitdaLastYear) / ebitdaLastYear) * 100
  const ebitdaVsTarget = ((1.6 - ebitdaTarget) / ebitdaTarget) * 100

  return (
    <Card>
      <CardContent>
        <div className="mb-4 text-lg font-bold">Income Statement Breakdown</div>

        {/* Revenue Section */}
        <div className="mb-2 flex items-center justify-between">
          <div className="font-semibold">Revenue</div>
          <div className="text-xl font-bold">$2.5M</div>
        </div>
        <div className="text-muted-foreground mb-4 ml-2 text-sm">
          <div className="mb-2 flex justify-between gap-2">
            <span>Consultation</span>
            <span>$983.2K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>Treatment</span>
            <span>$860.3K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>Drugs & Medicine / Consumables</span>
            <span>$491.6K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>Medical Report</span>
            <span>$122.9K</span>
          </div>
        </div>

        <div className="text-primary flex items-center justify-between gap-2 text-sm font-medium">
          <p>{`YoY Growth: ${revenueGrowth.toFixed(2)}%`}</p>
          <p>{`Vs Target: ${revenueVsTarget.toFixed(2)}%`}</p>
        </div>

        {/* Cost of Goods Sold Section */}
        <div className="mt-4 mb-2 flex items-center justify-between">
          <div className="font-semibold">Cost of Goods Sold</div>
          <div className="font-bold">$1.1M</div>
        </div>
        <div className="text-muted-foreground mb-4 ml-2 text-sm">
          <div className="mb-2 flex justify-between gap-2">
            <span>Consultation Fee</span>
            <span>$425.6K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>Consumables</span>
            <span>$336.4K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>Doctors Fee</span>
            <span>$358K</span>
          </div>
        </div>
        <div className="text-primary flex items-center justify-between gap-2 text-sm font-medium">
          <p>{`YoY Growth: ${cogsGrowth.toFixed(2)}%`}</p>
          <p>{`Vs Target: ${cogsVsTarget.toFixed(2)}%`}</p>
        </div>

        {/* Operating Expenses Section */}
        <div className="mt-4 mb-2 flex items-center justify-between">
          <div className="font-semibold">Operating Expenses</div>
          <div className="font-bold">$856K</div>
        </div>
        <div className="text-muted-foreground mb-4 ml-2 text-sm">
          <div className="mb-2 flex justify-between gap-2">
            <span>Salaries & Benefits</span>
            <span>$420K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>Marketing & Sales</span>
            <span>$156K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>R&amp;D</span>
            <span>$180K</span>
          </div>
          <div className="mb-2 flex justify-between gap-2">
            <span>General & Admin</span>
            <span>$100K</span>
          </div>
        </div>
        <div className="text-primary flex items-center justify-between gap-2 text-sm font-medium">
          <p>{`YoY Growth: ${operatingExpensesGrowth.toFixed(2)}%`}</p>
          <p>{`Vs Target: ${operatingExpensesVsTarget.toFixed(2)}%`}</p>
        </div>

        {/* EBITDA Section */}
        <div className="dark:bg-primary/20 mt-4 rounded-lg bg-blue-50 p-4">
          <div className="mb-2 flex items-center justify-between">
            <div className="font-semibold">EBITDA</div>
            <div className="text-xl font-bold">$1.6M</div>
          </div>
          <div className="mb-2 flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Operating Income</span>
            <span className="font-semibold">$1.6M</span>
          </div>
          <div className="mb-2 flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Depreciation</span>
            <span className="font-semibold text-red-600">- $150K</span>
          </div>
          <div className="mb-2 flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Amortization</span>
            <span className="font-semibold text-red-600">- $80K</span>
          </div>
          <div className="text-primary flex items-center justify-between gap-2 text-sm font-medium">
            <p>{`YoY Growth: ${ebitdaGrowth.toFixed(2)}%`}</p>
            <p>{`Vs Target: ${ebitdaVsTarget.toFixed(2)}%`}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const OperatingIncomeTrendCard = () => {
  const [drilldownOpen, setDrilldownOpen] = useState(false)

  const data = [
    {
      month: "January",
      current: 1450,
      budget: 1550,
      previous: 1300,
    },
    {
      month: "February",
      current: 1500,
      budget: 1570,
      previous: 1350,
    },
    {
      month: "March",
      current: 1530,
      budget: 1590,
      previous: 1400,
    },
    {
      month: "April",
      current: 1560,
      budget: 1600,
      previous: 1450,
    },
    {
      month: "May",
      current: 1600,
      budget: 1620,
      previous: 1500,
    },
    {
      month: "June",
      current: 1620,
      budget: 1650,
      previous: 1550,
    },
  ]

  // Handle click on chart elements
  const handleChartClick = () => {
    setDrilldownOpen(true)
  }

  return (
    <Card>
      <CardContent>
        <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
          EBITDA margin expanded to <span className="font-semibold">65.2%</span>
          , reflecting operational leverage.{" "}
          <span className="font-semibold">18.3%</span> MoM growth driven by
          revenue scaling and cost optimization. Depreciation impact remains
          stable at <span className="font-semibold">9.4%</span> of revenue.
        </div>

        <CardTitle className="mb-4 flex items-center justify-center gap-2 text-center text-base font-semibold text-gray-900 dark:text-white">
          Operating Income Trend Analysis
          <InfoTooltip>
            This chart displays{" "}
            <span className="text-primary font-semibold">
              monthly operating income trends
            </span>{" "}
            comparing{" "}
            <span className="text-primary font-semibold">
              current year performance (blue bars)
            </span>{" "}
            against{" "}
            <span className="text-primary font-semibold">
              budget targets (orange line)
            </span>{" "}
            and{" "}
            <span className="text-primary font-semibold">
              previous year results (green line)
            </span>
            . The visualization helps identify{" "}
            <span className="text-primary font-semibold">
              seasonal patterns
            </span>
            ,{" "}
            <span className="text-primary font-semibold">
              year-over-year growth
            </span>
            , and{" "}
            <span className="text-primary font-semibold">
              performance against financial goals
            </span>
            .
          </InfoTooltip>
        </CardTitle>

        <div onClick={handleChartClick} style={{ cursor: "pointer" }}>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" tick={{ fontSize: 12 }} />
              <YAxis
                domain={[1200, 1800]}
                tickFormatter={(v) => `$${(v / 1000).toFixed(1)}K`}
              />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend verticalAlign="bottom" />
              <Bar
                dataKey="current"
                name="Current Year"
                fill="var(--chart-1)"
                barSize={32}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Budget"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        <DrilldownModal
          open={drilldownOpen}
          onOpenChange={setDrilldownOpen}
          initialData={data}
          title="Operating Income Analysis"
          description="Detailed analysis of operating income with different views"
        />
      </CardContent>
    </Card>
  )
}

// Define the data point type for the cash flow chart
interface CashFlowDataPoint {
  month: string
  inflow: number
  outflow: number
  net: number
}

const CashFlowCard = () => {
  const [drilldownOpen, setDrilldownOpen] = useState(false)

  const cashFlowData: CashFlowDataPoint[] = [
    { month: "JUL", inflow: 8500, outflow: 3362, net: 5138 },
    { month: "AUG", inflow: 8700, outflow: 4298, net: 4402 },
    { month: "SEP", inflow: 9900, outflow: 4901, net: 4099 },
    { month: "OCT", inflow: 8500, outflow: 10500, net: -2000 },
    { month: "NOV", inflow: 10900, outflow: 4750, net: 6150 },
    { month: "DEC", inflow: 8600, outflow: 4250, net: 4350 },
    { month: "JAN", inflow: 8734, outflow: 3866, net: 4868 },
    { month: "FEB", inflow: 9023, outflow: 4977, net: 4046 },
    { month: "MAR", inflow: 8500, outflow: 4650, net: 3850 },
    { month: "APR", inflow: 9701, outflow: 4299, net: 5402 },
    { month: "MAY", inflow: 8500, outflow: 4484, net: 4016 },
    { month: "JUN", inflow: 9900, outflow: 4592, net: 5308 },
  ]

  // Handle click on chart
  const handleChartClick = () => {
    setDrilldownOpen(true)
  }

  return (
    <Card>
      <CardContent>
        <CardTitle className="mb-4 flex items-center justify-center gap-2 text-center text-base font-semibold text-gray-900 dark:text-white">
          Cash Flow
          <InfoTooltip>
            This chart presents a{" "}
            <span className="text-primary font-semibold">
              12-month analysis of cash flow dynamics
            </span>{" "}
            with{" "}
            <span className="text-primary font-semibold">
              monthly inflows (blue bars)
            </span>{" "}
            and{" "}
            <span className="text-primary font-semibold">
              outflows (orange bars)
            </span>
            , while the{" "}
            <span className="text-primary font-semibold">green line</span>{" "}
            tracks{" "}
            <span className="text-primary font-semibold">
              net operating cash flow
            </span>
            . The visualization highlights{" "}
            <span className="text-primary font-semibold">
              liquidity patterns
            </span>
            , identifies months with{" "}
            <span className="text-primary font-semibold">
              cash deficits (like October)
            </span>
            , and shows overall{" "}
            <span className="text-primary font-semibold">
              cash management effectiveness
            </span>
            .
          </InfoTooltip>
        </CardTitle>

        <div onClick={handleChartClick} style={{ cursor: "pointer" }}>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={cashFlowData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" tick={{ fontSize: 12 }} />

              {/* Left Y-Axis for Bar */}
              <YAxis yAxisId="left" tickFormatter={(v) => v.toLocaleString()} />

              {/* Right Y-Axis for Line */}
              <YAxis
                yAxisId="right"
                orientation="right"
                tickFormatter={(v) => v.toLocaleString()}
                stroke="#222"
              />

              <Tooltip formatter={(value: number) => value.toLocaleString()} />
              <Legend verticalAlign="bottom" />
              <Bar
                yAxisId="left"
                dataKey="inflow"
                name="Operating Cash Inflow"
                fill="var(--chart-1)"
                barSize={28}
                label={{
                  position: "top",
                  fill: "#222",
                  fontSize: 11,
                  formatter: (v: number) => v.toLocaleString(),
                }}
              />
              <Bar
                yAxisId="left"
                dataKey="outflow"
                name="Operating Cash Outflow"
                fill="var(--chart-3)"
                barSize={28}
                label={{
                  position: "top",
                  fill: "#222",
                  fontSize: 11,
                  formatter: (v: number) => v.toLocaleString(),
                }}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="net"
                name="Net Operating Cash Flow"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        <CashFlowDrilldownModal
          open={drilldownOpen}
          onOpenChange={setDrilldownOpen}
          initialData={cashFlowData}
          title="Cash Flow Analysis"
          description="Detailed analysis of cash flow with different views"
        />
      </CardContent>
    </Card>
  )
}

// Define the treemap data structure
interface TreemapChild {
  name: string
  size: number
}

interface TreemapItem {
  name: string
  size: number
  children?: TreemapChild[]
}

// Define the treemap node structure for rendering
interface TreemapNode {
  x: number
  y: number
  width: number
  height: number
  name: string
  size?: number
  value?: number
  depth: number
  payload?: TreemapItem
}

const TreemapCard = () => {
  const [drilldownOpen, setDrilldownOpen] = useState(false)

  const treemapData: TreemapItem[] = [
    {
      name: "Retail & Services",
      size: 1638.4,
      children: [
        { name: "Retail & Services", size: 675.2 },
        { name: "High Tech", size: 141.0 },
        { name: "Life Sciences", size: 107.4 },
      ],
    },
    { name: "Manufacturing", size: 363.6 },
    { name: "Healthcare", size: 300.2 },
    { name: "Public Sector", size: 232.1 },
    { name: "Financial Services", size: 204.9 },
  ]

  const COLORS = [
    "var(--chart-1)",
    "var(--chart-2)",
    "var(--chart-3)",
    "var(--chart-4)",
    "var(--chart-5)",
  ]

  // We'll handle clicks directly in the CustomizedContent component

  const CustomizedContent = (props: TreemapNode) => {
    const { x, y, width, height, name, size, depth } = props
    const value = size || props.value
    const color = COLORS[depth % COLORS.length]

    return (
      <g>
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          style={{
            fill: color,
            stroke: "#fff",
            strokeWidth: 2,
          }}
        />
        {width > 60 && height > 32 && (
          <text x={x + 8} y={y + 20} fontSize={12} fill="#fff">
            {name}
          </text>
        )}
        {width > 60 && height > 32 && (
          <text x={x + 8} y={y + 38} fontSize={12} fill="#fff">
            ${value?.toLocaleString()}
          </text>
        )}
      </g>
    )
  }

  return (
    <Card>
      <CardContent>
        <CardTitle className="mb-4 flex items-center justify-center gap-2 text-center text-base font-semibold text-gray-900 dark:text-white">
          Industry Breakdown by Operating Income
          <InfoTooltip>
            This treemap visualization proportionally represents{" "}
            <span className="text-primary font-semibold">operating income</span>{" "}
            across different{" "}
            <span className="text-primary font-semibold">industry sectors</span>
            , with box size indicating{" "}
            <span className="text-primary font-semibold">
              relative contribution{" "}
            </span>
            . The hierarchical structure shows both{" "}
            <span className="text-primary font-semibold">major sectors</span>{" "}
            (like{" "}
            <span className="text-primary font-semibold">
              Retail & Services
            </span>{" "}
            at <span className="text-primary font-semibold">$1.6M</span>) and
            their{" "}
            <span className="text-primary font-semibold">subsegments</span>,
            allowing for quick identification of the most{" "}
            <span className="text-primary font-semibold">
              financially significant business areas
            </span>
            .
          </InfoTooltip>
        </CardTitle>

        <div
          onClick={() => setDrilldownOpen(true)}
          style={{ cursor: "pointer" }}
        >
          <ResponsiveContainer width="100%" height={300}>
            <Treemap
              isAnimationActive={false}
              data={treemapData}
              dataKey="size"
              stroke="#fff"
              content={React.createElement(CustomizedContent)}
            />
          </ResponsiveContainer>
        </div>

        <TreemapDrilldownModal
          open={drilldownOpen}
          onOpenChange={setDrilldownOpen}
          selectedItem={treemapData[0]} // Default to first item
          title="Industry Operating Income Analysis"
          description="Detailed breakdown of operating income by industry sector"
        />
      </CardContent>
    </Card>
  )
}
