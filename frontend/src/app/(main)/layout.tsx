import React from "react"
import { cookies } from "next/headers"

import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import AppSidebar from "@/components/layouts/AppSidebar"
import Header from "@/components/layouts/Header"
import { BreadcrumbProvider } from "@/contexts/breadcrumb"

const Layout = async ({ children }: { children: React.ReactNode }) => {
  const cookieStore = await cookies()
  const sidebarState = cookieStore.get("sidebar_state")?.value ?? "true"
  const defaultOpen = sidebarState === "true"

  return (
    <BreadcrumbProvider>
      <SidebarProvider defaultOpen={defaultOpen}>
        <AppSidebar />

        <SidebarInset className="min-w-0 bg-transparent">
          <Header />

          <div className="relative flex flex-1 flex-col gap-4 p-4">
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </BreadcrumbProvider>
  )
}

export default Layout
