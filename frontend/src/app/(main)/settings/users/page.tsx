"use client"

import React from "react"
import {
  <PERSON>,
  Edit,
  Eye,
  Lock,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  Trash,
  UserPlus,
  X,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for users
const usersList = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    role: "Administrator",
    department: "Cardiology",
    status: "Active",
    lastActive: "Today at 10:23 AM",
  },
  {
    id: 2,
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    role: "Physician",
    department: "Neurology",
    status: "Active",
    lastActive: "Today at 09:45 AM",
  },
  {
    id: 3,
    name: "Nurse Emma Rodriguez",
    email: "<EMAIL>",
    role: "Nurse",
    department: "Emergency",
    status: "Active",
    lastActive: "Yesterday at 04:30 PM",
  },
  {
    id: 4,
    name: "James Wilson",
    email: "<EMAIL>",
    role: "Analyst",
    department: "Finance",
    status: "Inactive",
    lastActive: "May 10, 2024",
  },
  {
    id: 5,
    name: "Lisa Patel",
    email: "<EMAIL>",
    role: "Manager",
    department: "Operations",
    status: "Active",
    lastActive: "Today at 11:15 AM",
  },
  {
    id: 6,
    name: "Robert Thompson",
    email: "<EMAIL>",
    role: "Physician",
    department: "Orthopedics",
    status: "Active",
    lastActive: "Yesterday at 02:10 PM",
  },
  {
    id: 7,
    name: "Jennifer Garcia",
    email: "<EMAIL>",
    role: "Nurse",
    department: "Pediatrics",
    status: "Pending",
    lastActive: "Never",
  },
]

// Mock data for roles
const rolesList = [
  {
    id: 1,
    name: "Administrator",
    description: "Full access to all system features and settings",
    usersCount: 3,
    permissions: [
      "User Management",
      "Role Management",
      "System Configuration",
      "Data Management",
      "Report Generation",
      "Dashboard Access",
      "Audit Logs",
    ],
  },
  {
    id: 2,
    name: "Physician",
    description: "Access to patient data, analytics, and clinical dashboards",
    usersCount: 12,
    permissions: [
      "Dashboard Access",
      "Patient Data Access",
      "Clinical Analytics",
      "Report Generation",
    ],
  },
  {
    id: 3,
    name: "Nurse",
    description: "Limited access to patient data and basic analytics",
    usersCount: 28,
    permissions: [
      "Dashboard Access",
      "Patient Data Access (Limited)",
      "Basic Analytics",
    ],
  },
  {
    id: 4,
    name: "Analyst",
    description: "Access to analytics and reporting features",
    usersCount: 8,
    permissions: [
      "Dashboard Access",
      "Data Analytics",
      "Report Generation",
      "Data Export",
    ],
  },
  {
    id: 5,
    name: "Manager",
    description: "Access to department-specific data and reporting",
    usersCount: 15,
    permissions: [
      "Dashboard Access",
      "Department Data Access",
      "Report Generation",
      "Team Management",
    ],
  },
]

// Mock data for pending invitations
const pendingInvitations = [
  {
    id: 1,
    email: "<EMAIL>",
    role: "Nurse",
    department: "Pediatrics",
    invitedBy: "Dr. Sarah Johnson",
    invitedOn: "May 15, 2024",
    expiresOn: "May 22, 2024",
  },
  {
    id: 2,
    email: "<EMAIL>",
    role: "Physician",
    department: "Cardiology",
    invitedBy: "Dr. Sarah Johnson",
    invitedOn: "May 14, 2024",
    expiresOn: "May 21, 2024",
  },
]

const Users = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Settings", href: "/settings" },
        { label: "Users & Roles" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Users & Roles</CardTitle>
      <CardDescription>Manage users and their permissions</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="relative max-w-sm flex-1">
        <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
        <Input type="search" placeholder="Search users..." className="pl-8" />
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Eye className="mr-2 h-4 w-4" />
          View Audit Log
        </Button>
        <Button size="sm">
          <UserPlus className="mr-2 h-4 w-4" />
          Invite User
        </Button>
      </div>
    </div>

    <Tabs defaultValue="users" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="users">Users</TabsTrigger>
        <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
        <TabsTrigger value="invitations">Pending Invitations</TabsTrigger>
      </TabsList>
      <TabsContent value="users" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
            <CardDescription>
              View and manage user accounts and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Name</th>
                    <th className="p-2 text-left font-medium">Email</th>
                    <th className="p-2 text-left font-medium">Role</th>
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-center font-medium">Status</th>
                    <th className="p-2 text-left font-medium">Last Active</th>
                    <th className="p-2 text-center font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {usersList.map((user) => (
                    <tr key={user.id} className="border-b">
                      <td className="p-2 font-medium">{user.name}</td>
                      <td className="p-2">{user.email}</td>
                      <td className="p-2">{user.role}</td>
                      <td className="p-2">{user.department}</td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            user.status === "Active"
                              ? "bg-green-100 text-green-700"
                              : user.status === "Inactive"
                                ? "bg-gray-100 text-gray-700"
                                : "bg-amber-100 text-amber-700"
                          }`}
                        >
                          {user.status}
                        </span>
                      </td>
                      <td className="p-2">{user.lastActive}</td>
                      <td className="p-2 text-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Shield className="mr-2 h-4 w-4" />
                              Change Role
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {user.status === "Active" ? (
                              <DropdownMenuItem className="text-amber-600">
                                <Lock className="mr-2 h-4 w-4" />
                                Deactivate
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem className="text-green-600">
                                <Check className="mr-2 h-4 w-4" />
                                Activate
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-muted-foreground text-sm">
              Showing {usersList.length} users
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="roles" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Roles & Permissions</CardTitle>
            <CardDescription>
              Manage user roles and their associated permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              {rolesList.map((role) => (
                <div key={role.id} className="rounded-md border p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">{role.name}</h3>
                      <p className="text-muted-foreground text-sm">
                        {role.description}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground text-sm">
                        {role.usersCount} users
                      </span>
                      <Button variant="outline" size="sm">
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </Button>
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className="mb-2 text-sm font-medium">Permissions</h4>
                    <div className="flex flex-wrap gap-2">
                      {role.permissions.map((permission, index) => (
                        <span
                          key={index}
                          className="bg-primary/10 text-primary inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
                        >
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create New Role
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="invitations" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Pending Invitations</CardTitle>
            <CardDescription>
              Manage invitations that have been sent to new users
            </CardDescription>
          </CardHeader>
          <CardContent>
            {pendingInvitations.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="p-2 text-left font-medium">Email</th>
                      <th className="p-2 text-left font-medium">Role</th>
                      <th className="p-2 text-left font-medium">Department</th>
                      <th className="p-2 text-left font-medium">Invited By</th>
                      <th className="p-2 text-left font-medium">Invited On</th>
                      <th className="p-2 text-left font-medium">Expires On</th>
                      <th className="p-2 text-center font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pendingInvitations.map((invitation) => (
                      <tr key={invitation.id} className="border-b">
                        <td className="p-2 font-medium">{invitation.email}</td>
                        <td className="p-2">{invitation.role}</td>
                        <td className="p-2">{invitation.department}</td>
                        <td className="p-2">{invitation.invitedBy}</td>
                        <td className="p-2">{invitation.invitedOn}</td>
                        <td className="p-2">{invitation.expiresOn}</td>
                        <td className="p-2 text-center">
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0 text-amber-600"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="py-8 text-center">
                <p className="text-muted-foreground">No pending invitations</p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Send New Invitation
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default Users
