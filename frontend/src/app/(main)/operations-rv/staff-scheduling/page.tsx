"use client"

import React from "react"
import { Calendar, Clock, Download, Filter, Plus, Users } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for staff schedule
const staffSchedule = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    role: "Cardiologist",
    department: "Cardiology",
    schedule: [
      { day: "Monday", shifts: ["9:00 AM - 5:00 PM"] },
      { day: "Tuesday", shifts: ["9:00 AM - 5:00 PM"] },
      { day: "Wednesday", shifts: ["9:00 AM - 1:00 PM"] },
      { day: "Thursday", shifts: ["9:00 AM - 5:00 PM"] },
      { day: "Friday", shifts: ["9:00 AM - 5:00 PM"] },
      { day: "Saturday", shifts: [] },
      { day: "Sunday", shifts: [] },
    ],
    hoursScheduled: 36,
    status: "Confirmed",
  },
  {
    id: 2,
    name: "Dr. Michael Chen",
    role: "Neurologist",
    department: "Neurology",
    schedule: [
      { day: "Monday", shifts: ["8:00 AM - 4:00 <PERSON>"] },
      { day: "Tuesday", shifts: ["8:00 AM - 4:00 PM"] },
      { day: "Wednesday", shifts: ["8:00 AM - 4:00 PM"] },
      { day: "Thursday", shifts: ["8:00 AM - 12:00 PM"] },
      { day: "Friday", shifts: ["8:00 AM - 4:00 PM"] },
      { day: "Saturday", shifts: [] },
      { day: "Sunday", shifts: [] },
    ],
    hoursScheduled: 36,
    status: "Confirmed",
  },
  {
    id: 3,
    name: "Nurse Emma Rodriguez",
    role: "Registered Nurse",
    department: "Emergency",
    schedule: [
      { day: "Monday", shifts: ["7:00 AM - 7:00 PM"] },
      { day: "Tuesday", shifts: ["7:00 AM - 7:00 PM"] },
      { day: "Wednesday", shifts: [] },
      { day: "Thursday", shifts: [] },
      { day: "Friday", shifts: ["7:00 AM - 7:00 PM"] },
      { day: "Saturday", shifts: [] },
      { day: "Sunday", shifts: [] },
    ],
    hoursScheduled: 36,
    status: "Confirmed",
  },
  {
    id: 4,
    name: "Nurse David Wilson",
    role: "Registered Nurse",
    department: "Emergency",
    schedule: [
      { day: "Monday", shifts: [] },
      { day: "Tuesday", shifts: [] },
      { day: "Wednesday", shifts: ["7:00 AM - 7:00 PM"] },
      { day: "Thursday", shifts: ["7:00 AM - 7:00 PM"] },
      { day: "Friday", shifts: [] },
      { day: "Saturday", shifts: ["7:00 AM - 7:00 PM"] },
      { day: "Sunday", shifts: [] },
    ],
    hoursScheduled: 36,
    status: "Pending",
  },
  {
    id: 5,
    name: "Dr. Lisa Patel",
    role: "Pediatrician",
    department: "Pediatrics",
    schedule: [
      { day: "Monday", shifts: ["8:00 AM - 6:00 PM"] },
      { day: "Tuesday", shifts: ["8:00 AM - 6:00 PM"] },
      { day: "Wednesday", shifts: ["8:00 AM - 12:00 PM"] },
      { day: "Thursday", shifts: ["8:00 AM - 6:00 PM"] },
      { day: "Friday", shifts: [] },
      { day: "Saturday", shifts: [] },
      { day: "Sunday", shifts: [] },
    ],
    hoursScheduled: 34,
    status: "Confirmed",
  },
]

// Mock data for department coverage
const departmentCoverage = [
  {
    department: "Emergency",
    required: 12,
    scheduled: 10,
    coverage: 83,
    status: "At Risk",
  },
  {
    department: "Cardiology",
    required: 8,
    scheduled: 8,
    coverage: 100,
    status: "Optimal",
  },
  {
    department: "Neurology",
    required: 6,
    scheduled: 7,
    coverage: 117,
    status: "Optimal",
  },
  {
    department: "Pediatrics",
    required: 10,
    scheduled: 9,
    coverage: 90,
    status: "Adequate",
  },
  {
    department: "Surgery",
    required: 15,
    scheduled: 12,
    coverage: 80,
    status: "At Risk",
  },
]

const StaffScheduling = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Operations", href: "/operations" },
        { label: "Staff Scheduling" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Staff Scheduling</CardTitle>
      <CardDescription>
        Optimize staff scheduling and management to maximize efficiency
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          May 13 - May 19, 2024
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button size="sm">
          <Plus className="mr-2 h-4 w-4" />
          New Schedule
        </Button>
      </div>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Staff Scheduled</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">48/52</div>
          <p className="text-muted-foreground text-xs">
            92% of required staff scheduled
          </p>
          <Progress value={92} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1,824</div>
          <p className="text-muted-foreground text-xs">
            38 hours average per staff
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Coverage Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">88%</div>
          <p className="text-muted-foreground text-xs">+3% from last week</p>
          <Progress value={88} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Schedule Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            <span className="text-amber-500">At Risk</span>
          </div>
          <p className="text-muted-foreground text-xs">
            2 departments need attention
          </p>
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="schedule" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="schedule">Weekly Schedule</TabsTrigger>
        <TabsTrigger value="coverage">Department Coverage</TabsTrigger>
        <TabsTrigger value="requests">Time-Off Requests</TabsTrigger>
      </TabsList>
      <TabsContent value="schedule" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Staff Schedule</CardTitle>
            <CardDescription>
              Weekly schedule for all staff members
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Staff Member</th>
                    <th className="p-2 text-left font-medium">Role</th>
                    <th className="p-2 text-center font-medium">Mon</th>
                    <th className="p-2 text-center font-medium">Tue</th>
                    <th className="p-2 text-center font-medium">Wed</th>
                    <th className="p-2 text-center font-medium">Thu</th>
                    <th className="p-2 text-center font-medium">Fri</th>
                    <th className="p-2 text-center font-medium">Sat</th>
                    <th className="p-2 text-center font-medium">Sun</th>
                    <th className="p-2 text-center font-medium">Hours</th>
                    <th className="p-2 text-center font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {staffSchedule.map((staff) => (
                    <tr key={staff.id} className="border-b">
                      <td className="p-2 font-medium">{staff.name}</td>
                      <td className="text-muted-foreground p-2">
                        {staff.role}
                      </td>
                      {staff.schedule.map((day, index) => (
                        <td key={index} className="p-2 text-center">
                          {day.shifts.length > 0 ? (
                            <div className="flex flex-col items-center">
                              <span className="text-xs">{day.shifts[0]}</span>
                              {day.shifts.length > 1 && (
                                <span className="text-muted-foreground text-xs">
                                  +{day.shifts.length - 1} more
                                </span>
                              )}
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-xs">
                              Off
                            </span>
                          )}
                        </td>
                      ))}
                      <td className="p-2 text-center">
                        {staff.hoursScheduled}
                      </td>
                      <td className="p-2 text-center">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            staff.status === "Confirmed"
                              ? "bg-green-100 text-green-700"
                              : "bg-amber-100 text-amber-700"
                          }`}
                        >
                          {staff.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="coverage" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Department Coverage</CardTitle>
            <CardDescription>
              Staff coverage analysis by department
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-center font-medium">
                      Required Staff
                    </th>
                    <th className="p-2 text-center font-medium">
                      Scheduled Staff
                    </th>
                    <th className="p-2 text-center font-medium">Coverage</th>
                    <th className="p-2 text-left font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {departmentCoverage.map((dept, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{dept.department}</td>
                      <td className="p-2 text-center">{dept.required}</td>
                      <td className="p-2 text-center">{dept.scheduled}</td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{dept.coverage}%</span>
                          <Progress
                            value={dept.coverage}
                            className={`h-1.5 w-16 ${
                              dept.coverage < 85
                                ? "bg-red-100"
                                : dept.coverage < 95
                                  ? "bg-amber-100"
                                  : "bg-green-100"
                            }`}
                          />
                        </div>
                      </td>
                      <td className="p-2">
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                            dept.status === "Optimal"
                              ? "bg-green-100 text-green-700"
                              : dept.status === "Adequate"
                                ? "bg-blue-100 text-blue-700"
                                : "bg-red-100 text-red-700"
                          }`}
                        >
                          {dept.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Coverage Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Users className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      Emergency department is understaffed by 2 nurses for the
                      night shift on Wednesday and Thursday.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Users className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      Surgery department needs 3 additional staff members for
                      Monday morning procedures.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Users className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Neurology department has optimal coverage with 1
                      additional staff member available for flexibility.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Recommendations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Clock className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Reassign Nurse Wilson from Thursday to Monday to support
                      Surgery department.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Clock className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Approve overtime for 2 Emergency nurses on Wednesday and
                      Thursday night shifts.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Clock className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Consider adjusting Dr. {`Patel's`} schedule to provide
                      support in Pediatrics on Friday morning.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="requests" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Time-Off Requests</CardTitle>
            <CardDescription>
              Manage and approve staff time-off requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Staff Member</th>
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-left font-medium">Request Type</th>
                    <th className="p-2 text-center font-medium">Start Date</th>
                    <th className="p-2 text-center font-medium">End Date</th>
                    <th className="p-2 text-center font-medium">Duration</th>
                    <th className="p-2 text-left font-medium">Status</th>
                    <th className="p-2 text-center font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-2 font-medium">Dr. Sarah Johnson</td>
                    <td className="p-2">Cardiology</td>
                    <td className="p-2">Vacation</td>
                    <td className="p-2 text-center">May 20, 2024</td>
                    <td className="p-2 text-center">May 27, 2024</td>
                    <td className="p-2 text-center">7 days</td>
                    <td className="p-2">
                      <span className="inline-flex items-center rounded-full bg-amber-100 px-2 py-1 text-xs text-amber-700">
                        Pending
                      </span>
                    </td>
                    <td className="p-2 text-center">
                      <div className="flex justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-2"
                        >
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-2"
                        >
                          Deny
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-medium">Nurse Emma Rodriguez</td>
                    <td className="p-2">Emergency</td>
                    <td className="p-2">Sick Leave</td>
                    <td className="p-2 text-center">May 15, 2024</td>
                    <td className="p-2 text-center">May 16, 2024</td>
                    <td className="p-2 text-center">2 days</td>
                    <td className="p-2">
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs text-green-700">
                        Approved
                      </span>
                    </td>
                    <td className="p-2 text-center">
                      <Button variant="outline" size="sm" className="h-8 px-2">
                        View
                      </Button>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-medium">Dr. Michael Chen</td>
                    <td className="p-2">Neurology</td>
                    <td className="p-2">Conference</td>
                    <td className="p-2 text-center">June 5, 2024</td>
                    <td className="p-2 text-center">June 8, 2024</td>
                    <td className="p-2 text-center">4 days</td>
                    <td className="p-2">
                      <span className="inline-flex items-center rounded-full bg-amber-100 px-2 py-1 text-xs text-amber-700">
                        Pending
                      </span>
                    </td>
                    <td className="p-2 text-center">
                      <div className="flex justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-2"
                        >
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-2"
                        >
                          Deny
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-medium">Dr. Lisa Patel</td>
                    <td className="p-2">Pediatrics</td>
                    <td className="p-2">Personal</td>
                    <td className="p-2 text-center">May 19, 2024</td>
                    <td className="p-2 text-center">May 19, 2024</td>
                    <td className="p-2 text-center">1 day</td>
                    <td className="p-2">
                      <span className="inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs text-red-700">
                        Denied
                      </span>
                    </td>
                    <td className="p-2 text-center">
                      <Button variant="outline" size="sm" className="h-8 px-2">
                        View
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default StaffScheduling
