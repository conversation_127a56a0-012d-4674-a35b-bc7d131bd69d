"use client"

import React, { useEffect, useRef, useState } from "react"
import dynamic from "next/dynamic"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Tree = dynamic(() => import("react-d3-tree"), { ssr: false })

const Operations = () => {
  const [selectedEntity, setSelectedEntity] = useState("hospital")

  return (
    <>
      <Breadcrumb links={[{ label: "Operations" }]} />

      <div className="flex flex-col gap-1.5">
        <CardTitle>Operations</CardTitle>
        <CardDescription>Company-wide operations management</CardDescription>
      </div>

      <EntitySelector
        selectedEntity={selectedEntity}
        onEntityChange={setSelectedEntity}
      />

      <OrgChart
        entityType={selectedEntity}
        data={orgChartData[selectedEntity]}
      />
    </>
  )
}

export default Operations

const entities = [
  {
    value: "hospital",
    label: "Main Hospital",
  },
  {
    value: "clinics",
    label: "Outpatient Clinics",
  },
  {
    value: "research",
    label: "Research Division",
  },
  {
    value: "administration",
    label: "Administration",
  },
]

interface EntitySelectorProps {
  selectedEntity: string
  onEntityChange: (value: string) => void
}

function EntitySelector({
  selectedEntity,
  onEntityChange,
}: EntitySelectorProps) {
  const [open, setOpen] = React.useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="bg-card w-full justify-between md:w-[280px]"
        >
          {selectedEntity
            ? entities.find((entity) => entity.value === selectedEntity)?.label
            : "Select entity..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0 md:w-[280px]">
        <Command>
          <CommandInput placeholder="Search entity..." />
          <CommandList>
            <CommandEmpty>No entity found.</CommandEmpty>
            <CommandGroup>
              {entities.map((entity) => (
                <CommandItem
                  key={entity.value}
                  value={entity.value}
                  onSelect={(currentValue) => {
                    onEntityChange(currentValue)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedEntity === entity.value
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {entity.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

const orgChartData: Record<string, OrgChartNode> = {
  hospital: {
    id: "ceo",
    name: "Dr. Sarah Johnson",
    title: "Chief Executive Officer",
    children: [
      {
        id: "cmo",
        name: "Dr. Michael Chen",
        title: "Chief Medical Officer",
        department: "Medical Affairs",
        children: [
          {
            id: "emergency",
            name: "Dr. Emily Rodriguez",
            title: "Emergency Department Head",
            department: "Emergency",
            children: [
              {
                id: "emergency-staff1",
                name: "Dr. James Wilson",
                title: "Emergency Physician",
                department: "Emergency",
              },
              {
                id: "emergency-staff2",
                name: "Dr. Lisa Park",
                title: "Emergency Physician",
                department: "Emergency",
              },
            ],
          },
          {
            id: "surgery",
            name: "Dr. Robert Kim",
            title: "Surgery Department Head",
            department: "Surgery",
            children: [
              {
                id: "surgery-staff1",
                name: "Dr. Maria Garcia",
                title: "General Surgeon",
                department: "Surgery",
              },
              {
                id: "surgery-staff2",
                name: "Dr. David Lee",
                title: "Orthopedic Surgeon",
                department: "Surgery",
              },
            ],
          },
        ],
      },
      {
        id: "cno",
        name: "Amanda Thompson",
        title: "Chief Nursing Officer",
        department: "Nursing",
        children: [
          {
            id: "nursing-director",
            name: "Jennifer Williams",
            title: "Nursing Director",
            department: "Nursing",
            children: [
              {
                id: "head-nurse1",
                name: "Patricia Brown",
                title: "Head Nurse - Floor 1",
                department: "Nursing",
              },
              {
                id: "head-nurse2",
                name: "Richard Davis",
                title: "Head Nurse - Floor 2",
                department: "Nursing",
              },
            ],
          },
        ],
      },
      {
        id: "cfo",
        name: "Thomas Anderson",
        title: "Chief Financial Officer",
        department: "Finance",
        children: [
          {
            id: "finance-director",
            name: "Jessica Martinez",
            title: "Finance Director",
            department: "Finance",
          },
          {
            id: "billing-manager",
            name: "Kevin Taylor",
            title: "Billing Manager",
            department: "Finance",
          },
        ],
      },
    ],
  },
  clinics: {
    id: "clinic-director",
    name: "Dr. Elizabeth Wright",
    title: "Clinics Director",
    children: [
      {
        id: "primary-care",
        name: "Dr. John Miller",
        title: "Primary Care Lead",
        department: "Primary Care",
        children: [
          {
            id: "family-medicine",
            name: "Dr. Susan Clark",
            title: "Family Medicine Head",
            department: "Primary Care",
            children: [
              {
                id: "family-med1",
                name: "Dr. Andrew White",
                title: "Family Physician",
                department: "Primary Care",
              },
              {
                id: "family-med2",
                name: "Dr. Nancy Lopez",
                title: "Family Physician",
                department: "Primary Care",
              },
            ],
          },
        ],
      },
      {
        id: "specialty-care",
        name: "Dr. William Turner",
        title: "Specialty Care Lead",
        department: "Specialty Care",
        children: [
          {
            id: "cardiology",
            name: "Dr. Rebecca Adams",
            title: "Cardiology Head",
            department: "Cardiology",
          },
          {
            id: "dermatology",
            name: "Dr. Daniel Evans",
            title: "Dermatology Head",
            department: "Dermatology",
          },
        ],
      },
    ],
  },
  research: {
    id: "research-director",
    name: "Dr. Katherine Nelson",
    title: "Research Director",
    children: [
      {
        id: "clinical-research",
        name: "Dr. Alexander Scott",
        title: "Clinical Research Lead",
        department: "Clinical Research",
        children: [
          {
            id: "trial-coordinator",
            name: "Dr. Olivia Green",
            title: "Clinical Trials Coordinator",
            department: "Clinical Research",
          },
          {
            id: "data-analyst",
            name: "Dr. Ryan Baker",
            title: "Research Data Analyst",
            department: "Clinical Research",
          },
        ],
      },
      {
        id: "lab-research",
        name: "Dr. Sophia King",
        title: "Laboratory Research Lead",
        department: "Laboratory Research",
        children: [
          {
            id: "molecular-biology",
            name: "Dr. Christopher Hill",
            title: "Molecular Biology Head",
            department: "Laboratory Research",
          },
          {
            id: "genetics",
            name: "Dr. Michelle Carter",
            title: "Genetics Research Head",
            department: "Laboratory Research",
          },
        ],
      },
    ],
  },
  administration: {
    id: "admin-director",
    name: "Jonathan Phillips",
    title: "Administrative Director",
    children: [
      {
        id: "hr-director",
        name: "Stephanie Lewis",
        title: "HR Director",
        department: "Human Resources",
        children: [
          {
            id: "recruitment",
            name: "Brian Mitchell",
            title: "Recruitment Manager",
            department: "Human Resources",
          },
          {
            id: "employee-relations",
            name: "Laura Wilson",
            title: "Employee Relations Manager",
            department: "Human Resources",
          },
        ],
      },
      {
        id: "it-director",
        name: "Mark Robinson",
        title: "IT Director",
        department: "Information Technology",
        children: [
          {
            id: "systems-admin",
            name: "Paul Harris",
            title: "Systems Administrator",
            department: "Information Technology",
          },
          {
            id: "helpdesk",
            name: "Sarah Young",
            title: "Helpdesk Manager",
            department: "Information Technology",
          },
        ],
      },
      {
        id: "facilities",
        name: "George Thompson",
        title: "Facilities Manager",
        department: "Facilities",
        children: [
          {
            id: "maintenance",
            name: "Robert Allen",
            title: "Maintenance Supervisor",
            department: "Facilities",
          },
          {
            id: "security",
            name: "Michael Walker",
            title: "Security Manager",
            department: "Facilities",
          },
        ],
      },
    ],
  },
}

interface OrgChartNode {
  id: string
  name: string
  title: string
  department?: string
  children?: OrgChartNode[]
}

interface OrgChartProps {
  entityType: string
  data: OrgChartNode
}

interface TreeNode {
  name: string
  attributes?: Record<string, string | number | boolean>
  children: TreeNode[]
}

// Transform our data structure to react-d3-tree format
const transformData = (node: OrgChartNode): TreeNode => {
  return {
    name: node.name,
    attributes: {
      title: node.title,
      department: node.department || "",
      id: node.id,
    },
    children: node.children ? node.children.map(transformData) : [],
  }
}

function OrgChart({ entityType, data }: OrgChartProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 })
  const [translate, setTranslate] = useState({ x: 0, y: 0 })

  useEffect(() => {
    if (containerRef.current) {
      const { width, height } = containerRef.current.getBoundingClientRect()
      setDimensions({ width, height })
      setTranslate({ x: width / 2, y: 80 })
    }
  }, [containerRef, entityType])

  // Custom node renderer
  const renderCustomNodeElement = ({
    nodeDatum,
    toggleNode,
  }: {
    nodeDatum: {
      name: string
      attributes?: Record<string, string | number | boolean>
    }
    toggleNode: () => void
  }) => (
    <g onClick={toggleNode}>
      <rect
        x="-80"
        y="-25"
        width="160"
        height="65"
        fill="white"
        stroke="#222"
        strokeWidth="1"
        className="shadow-sm"
      />
      <text
        fill="#1e293b"
        strokeWidth="0"
        x="0"
        y="-5"
        textAnchor="middle"
        dominantBaseline="middle"
        style={{ fontSize: "12px", fontWeight: "500" }}
      >
        {nodeDatum.name}
      </text>
      <text
        fill="#64748b"
        strokeWidth="0"
        x="0"
        y="12"
        textAnchor="middle"
        dominantBaseline="middle"
        style={{ fontSize: "10px" }}
      >
        {nodeDatum.attributes?.title}
      </text>
      {nodeDatum.attributes?.department && (
        <text
          fill="#6b7280"
          strokeWidth="0"
          x="0"
          y="25"
          textAnchor="middle"
          dominantBaseline="middle"
          style={{ fontSize: "9px", fontStyle: "italic" }}
        >
          {nodeDatum.attributes.department}
        </text>
      )}
    </g>
  )

  return (
    <Card className="w-full">
      <CardContent className="overflow-hidden p-0">
        <div ref={containerRef} className="h-[600px] w-full">
          {dimensions.width > 0 && (
            <Tree
              data={transformData(data)}
              orientation="vertical"
              translate={translate}
              nodeSize={{ x: 200, y: 100 }}
              renderCustomNodeElement={renderCustomNodeElement}
              pathFunc="step"
              separation={{ siblings: 1, nonSiblings: 1.2 }}
              zoomable={true}
              collapsible={true}
              initialDepth={3}
            />
          )}
        </div>
      </CardContent>
    </Card>
  )
}
