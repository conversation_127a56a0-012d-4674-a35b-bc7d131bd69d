"use client"

import React from "react"
import {
  Calendar,
  Clock,
  Download,
  Filter,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for department metrics
const departmentMetrics = [
  {
    department: "Cardiology",
    appointments: 245,
    noShows: 18,
    cancellations: 12,
    waitTime: 8.5,
    utilization: 92,
    trend: "****%",
  },
  {
    department: "Neurology",
    appointments: 180,
    noShows: 15,
    cancellations: 9,
    waitTime: 12.3,
    utilization: 85,
    trend: "****%",
  },
  {
    department: "Pediatrics",
    appointments: 310,
    noShows: 22,
    cancellations: 18,
    waitTime: 6.2,
    utilization: 88,
    trend: "****%",
  },
  {
    department: "Orthopedics",
    appointments: 195,
    noShows: 14,
    cancellations: 11,
    waitTime: 10.8,
    utilization: 90,
    trend: "****%",
  },
  {
    department: "Dermatology",
    appointments: 220,
    noShows: 20,
    cancellations: 15,
    waitTime: 7.5,
    utilization: 86,
    trend: "-1.2%",
  },
]

// Mock data for provider metrics
const providerMetrics = [
  {
    provider: "Dr. Sarah Johnson",
    department: "Cardiology",
    appointments: 85,
    noShowRate: 5.2,
    avgDuration: 22,
    utilization: 94,
  },
  {
    provider: "Dr. Michael Chen",
    department: "Neurology",
    appointments: 72,
    noShowRate: 8.3,
    avgDuration: 35,
    utilization: 88,
  },
  {
    provider: "Dr. Lisa Patel",
    department: "Pediatrics",
    appointments: 95,
    noShowRate: 6.8,
    avgDuration: 18,
    utilization: 92,
  },
  {
    provider: "Dr. James Wilson",
    department: "Orthopedics",
    appointments: 68,
    noShowRate: 7.2,
    avgDuration: 28,
    utilization: 85,
  },
  {
    provider: "Dr. Emily Rodriguez",
    department: "Dermatology",
    appointments: 78,
    noShowRate: 9.1,
    avgDuration: 20,
    utilization: 82,
  },
]

// Mock data for appointment types
const appointmentTypes = [
  { type: "New Patient", count: 320, percentage: 28, avgDuration: 35 },
  { type: "Follow-up", count: 485, percentage: 42, avgDuration: 20 },
  { type: "Consultation", count: 175, percentage: 15, avgDuration: 45 },
  { type: "Procedure", count: 120, percentage: 10, avgDuration: 60 },
  { type: "Urgent Care", count: 50, percentage: 5, avgDuration: 25 },
]

const AppointmentMetrics = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Operations", href: "/operations" },
        { label: "Appointment Metrics" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Appointment Metrics</CardTitle>
      <CardDescription>
        Analyze and optimize your appointment scheduling and management
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 30 Days
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Total Appointments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1,150</div>
          <div className="mt-1 flex items-center">
            <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">+2.8% from last month</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">No-Show Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">7.3%</div>
          <div className="mt-1 flex items-center">
            <TrendingDown className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">-1.2% from last month</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Avg. Wait Time</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">8.5 min</div>
          <div className="mt-1 flex items-center">
            <TrendingDown className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">-2.1 min from last month</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Schedule Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">88.2%</div>
          <div className="mt-1 flex items-center">
            <TrendingUp className="mr-1 h-4 w-4 text-green-500" />
            <p className="text-xs text-green-500">+3.5% from last month</p>
          </div>
          <Progress value={88.2} className="mt-2" />
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="departments" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="departments">Department Metrics</TabsTrigger>
        <TabsTrigger value="providers">Provider Metrics</TabsTrigger>
        <TabsTrigger value="types">Appointment Types</TabsTrigger>
      </TabsList>
      <TabsContent value="departments" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Department Metrics</CardTitle>
            <CardDescription>
              Appointment metrics broken down by department
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-center font-medium">
                      Appointments
                    </th>
                    <th className="p-2 text-center font-medium">No-Shows</th>
                    <th className="p-2 text-center font-medium">
                      Cancellations
                    </th>
                    <th className="p-2 text-center font-medium">
                      Avg. Wait Time
                    </th>
                    <th className="p-2 text-center font-medium">Utilization</th>
                    <th className="p-2 text-right font-medium">Trend</th>
                  </tr>
                </thead>
                <tbody>
                  {departmentMetrics.map((dept, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{dept.department}</td>
                      <td className="p-2 text-center">{dept.appointments}</td>
                      <td className="p-2 text-center">
                        {dept.noShows} (
                        {((dept.noShows / dept.appointments) * 100).toFixed(1)}
                        %)
                      </td>
                      <td className="p-2 text-center">
                        {dept.cancellations} (
                        {(
                          (dept.cancellations / dept.appointments) *
                          100
                        ).toFixed(1)}
                        %)
                      </td>
                      <td className="p-2 text-center">{dept.waitTime} min</td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{dept.utilization}%</span>
                          <Progress
                            value={dept.utilization}
                            className="h-1.5 w-16"
                          />
                        </div>
                      </td>
                      <td
                        className={`p-2 text-right ${dept.trend.startsWith("+") ? "text-green-500" : "text-red-500"}`}
                      >
                        {dept.trend}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Key Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <TrendingUp className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>
                      Pediatrics department shows the highest growth in
                      appointments (****%) due to seasonal flu vaccinations.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingDown className="mt-0.5 h-4 w-4 text-red-500" />
                    <span>
                      Dermatology department experienced a slight decline
                      (-1.2%) in appointments, potentially due to provider
                      vacation periods.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Clock className="mt-0.5 h-4 w-4 text-amber-500" />
                    <span>
                      Neurology has the highest average wait time (12.3 min),
                      suggesting potential scheduling optimization
                      opportunities.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">Recommendations</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Clock className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Implement 10-minute buffer periods between Neurology
                      appointments to reduce wait times.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Users className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Introduce automated appointment reminders for Dermatology
                      to reduce the 9.1% no-show rate.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Calendar className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Extend Cardiology evening hours on Tuesdays and Thursdays
                      to accommodate increased demand.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="providers" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Provider Metrics</CardTitle>
            <CardDescription>
              Appointment metrics broken down by healthcare provider
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Provider</th>
                    <th className="p-2 text-left font-medium">Department</th>
                    <th className="p-2 text-center font-medium">
                      Appointments
                    </th>
                    <th className="p-2 text-center font-medium">
                      No-Show Rate
                    </th>
                    <th className="p-2 text-center font-medium">
                      Avg. Duration
                    </th>
                    <th className="p-2 text-center font-medium">Utilization</th>
                  </tr>
                </thead>
                <tbody>
                  {providerMetrics.map((provider, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{provider.provider}</td>
                      <td className="p-2">{provider.department}</td>
                      <td className="p-2 text-center">
                        {provider.appointments}
                      </td>
                      <td className="p-2 text-center">
                        {provider.noShowRate}%
                      </td>
                      <td className="p-2 text-center">
                        {provider.avgDuration} min
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{provider.utilization}%</span>
                          <Progress
                            value={provider.utilization}
                            className={`h-1.5 w-16 ${
                              provider.utilization < 80
                                ? "bg-red-100"
                                : provider.utilization < 90
                                  ? "bg-amber-100"
                                  : "bg-green-100"
                            }`}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-primary/10 mt-6 rounded-md p-4">
              <h3 className="mb-2 text-sm font-medium">
                Provider Efficiency Analysis
              </h3>
              <p className="text-muted-foreground mb-4 text-sm">
                Analysis of provider scheduling patterns and efficiency metrics
                reveals several opportunities for optimization:
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <Users className="text-primary mt-0.5 h-4 w-4" />
                  <span>
                    Dr. Sarah Johnson (Cardiology) demonstrates the highest
                    utilization rate (94%) while maintaining the lowest no-show
                    rate (5.2%), suggesting effective patient engagement
                    practices that could be shared with other providers.
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Clock className="text-primary mt-0.5 h-4 w-4" />
                  <span>
                    Dr. Michael Chen (Neurology) has the longest average
                    appointment duration (35 min), which contributes to the
                    {`department's`} higher wait times. Consider reviewing
                    appointment types and potentially adjusting scheduling
                    templates.
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Calendar className="text-primary mt-0.5 h-4 w-4" />
                  <span>
                    Dr. Emily Rodriguez (Dermatology) has the highest no-show
                    rate (9.1%) and lowest utilization (82%). Implementing a
                    targeted reminder system and overbooking strategy could
                    improve these metrics.
                  </span>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="types" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Appointment Types</CardTitle>
            <CardDescription>
              Analysis of different appointment types and their metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">
                      Appointment Type
                    </th>
                    <th className="p-2 text-center font-medium">Count</th>
                    <th className="p-2 text-center font-medium">Percentage</th>
                    <th className="p-2 text-center font-medium">
                      Avg. Duration
                    </th>
                    <th className="p-2 text-left font-medium">Distribution</th>
                  </tr>
                </thead>
                <tbody>
                  {appointmentTypes.map((type, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{type.type}</td>
                      <td className="p-2 text-center">{type.count}</td>
                      <td className="p-2 text-center">{type.percentage}%</td>
                      <td className="p-2 text-center">
                        {type.avgDuration} min
                      </td>
                      <td className="p-2">
                        <div className="w-full">
                          <Progress value={type.percentage} className="h-2" />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">
                  Appointment Type Insights
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Clock className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Follow-up appointments represent the largest category
                      (42%) with relatively short duration (20 min), making them
                      ideal for optimizing provider schedules.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Clock className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Procedure appointments have the longest average duration
                      (60 min) and should be scheduled during periods of lower
                      demand to minimize impact on wait times.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Users className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      New Patient appointments (28%) require more time (35 min)
                      and resources, suggesting potential for dedicated new
                      patient slots in the schedule.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="rounded-md border p-4">
                <h3 className="mb-2 text-sm font-medium">
                  Scheduling Recommendations
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Calendar className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Reserve morning slots for longer appointments (Procedures
                      and Consultations) when providers are typically more
                      efficient.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Calendar className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Group similar appointment types together to improve
                      provider workflow and reduce context-switching.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Calendar className="text-primary mt-0.5 h-4 w-4" />
                    <span>
                      Implement {`"buffer appointments"`} after procedures to
                      accommodate potential overruns without impacting
                      subsequent appointments.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default AppointmentMetrics
