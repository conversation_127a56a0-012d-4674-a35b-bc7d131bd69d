"use client"

import React from "react"
import { Calendar, Download, Filter, Users } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for clinic utilization
const clinicData = [
  {
    name: "Main Campus",
    roomUtilization: 87,
    staffUtilization: 92,
    equipmentUtilization: 78,
    waitTime: 12,
    patientVolume: 145,
    trend: "+5%",
  },
  {
    name: "North Wing",
    roomUtilization: 92,
    staffUtilization: 88,
    equipmentUtilization: 85,
    waitTime: 8,
    patientVolume: 120,
    trend: "+8%",
  },
  {
    name: "South Wing",
    roomUtilization: 75,
    staffUtilization: 82,
    equipmentUtilization: 70,
    waitTime: 18,
    patientVolume: 95,
    trend: "-2%",
  },
  {
    name: "Specialty Center",
    roomUtilization: 95,
    staffUtilization: 96,
    equipmentUtilization: 90,
    waitTime: 5,
    patientVolume: 85,
    trend: "+12%",
  },
]

const ClinicUtilization = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Operations", href: "/operations" },
        { label: "Clinic Utilization" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Clinic Utilization</CardTitle>
      <CardDescription>
        Maximize efficiency and resource allocation in your clinical operations
      </CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          Last 30 Days
        </Button>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>
      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>

    <div className="mt-4 grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Overall Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">86.5%</div>
          <p className="text-muted-foreground text-xs">+4.3% from last month</p>
          <Progress value={86.5} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Room Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">82.3%</div>
          <p className="text-muted-foreground text-xs">+2.1% from last month</p>
          <Progress value={82.3} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Staff Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">89.5%</div>
          <p className="text-muted-foreground text-xs">+5.2% from last month</p>
          <Progress value={89.5} className="mt-2" />
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Equipment Utilization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">80.8%</div>
          <p className="text-muted-foreground text-xs">+3.7% from last month</p>
          <Progress value={80.8} className="mt-2" />
        </CardContent>
      </Card>
    </div>

    <Tabs defaultValue="utilization" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="utilization">Utilization Metrics</TabsTrigger>
        <TabsTrigger value="capacity">Capacity Planning</TabsTrigger>
        <TabsTrigger value="trends">Utilization Trends</TabsTrigger>
      </TabsList>
      <TabsContent value="utilization" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Clinic Utilization by Location</CardTitle>
            <CardDescription>
              Detailed breakdown of utilization metrics across all clinic
              locations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-2 text-left font-medium">Location</th>
                    <th className="p-2 text-center font-medium">
                      Room Utilization
                    </th>
                    <th className="p-2 text-center font-medium">
                      Staff Utilization
                    </th>
                    <th className="p-2 text-center font-medium">
                      Equipment Utilization
                    </th>
                    <th className="p-2 text-center font-medium">
                      Avg. Wait Time
                    </th>
                    <th className="p-2 text-center font-medium">
                      Patient Volume
                    </th>
                    <th className="p-2 text-center font-medium">Trend</th>
                  </tr>
                </thead>
                <tbody>
                  {clinicData.map((clinic, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{clinic.name}</td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{clinic.roomUtilization}%</span>
                          <Progress
                            value={clinic.roomUtilization}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{clinic.staffUtilization}%</span>
                          <Progress
                            value={clinic.staffUtilization}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">
                        <div className="flex flex-col items-center">
                          <span>{clinic.equipmentUtilization}%</span>
                          <Progress
                            value={clinic.equipmentUtilization}
                            className="h-1 w-16"
                          />
                        </div>
                      </td>
                      <td className="p-2 text-center">{clinic.waitTime} min</td>
                      <td className="p-2 text-center">
                        {clinic.patientVolume}/day
                      </td>
                      <td className="p-2 text-center">
                        <span
                          className={
                            clinic.trend.startsWith("+")
                              ? "text-green-500"
                              : "text-red-500"
                          }
                        >
                          {clinic.trend}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="capacity" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Capacity Planning</CardTitle>
            <CardDescription>
              Optimize resource allocation based on historical and projected
              demand
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="mb-2 text-lg font-medium">
                  Resource Allocation Recommendations
                </h3>
                <div className="space-y-4">
                  <div className="rounded-md border p-3">
                    <div className="mb-1 flex items-center gap-2">
                      <Users className="text-primary h-4 w-4" />
                      <span className="font-medium">Staff Reallocation</span>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      Shift 2 nurses from South Wing to North Wing during peak
                      hours (10am-2pm) to address increased patient volume.
                    </p>
                  </div>
                  <div className="rounded-md border p-3">
                    <div className="mb-1 flex items-center gap-2">
                      <Calendar className="text-primary h-4 w-4" />
                      <span className="font-medium">Schedule Optimization</span>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      Extend Specialty Center hours by 2 hours on Tuesdays and
                      Thursdays to accommodate 15% increase in appointment
                      requests.
                    </p>
                  </div>
                  <div className="rounded-md border p-3">
                    <div className="mb-1 flex items-center gap-2">
                      <Users className="text-primary h-4 w-4" />
                      <span className="font-medium">Equipment Utilization</span>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      Move one MRI machine from South Wing to Main Campus to
                      increase overall utilization by an estimated 12%.
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="mb-2 text-lg font-medium">Projected Impact</h3>
                <div className="space-y-3">
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">Overall Utilization</span>
                      <span className="text-sm font-medium">+8.2%</span>
                    </div>
                    <Progress value={82} className="h-2" />
                  </div>
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">Patient Throughput</span>
                      <span className="text-sm font-medium">+12.5%</span>
                    </div>
                    <Progress value={75} className="h-2" />
                  </div>
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">Wait Time Reduction</span>
                      <span className="text-sm font-medium">-15.3%</span>
                    </div>
                    <Progress value={65} className="h-2" />
                  </div>
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">Revenue Impact</span>
                      <span className="text-sm font-medium">+9.7%</span>
                    </div>
                    <Progress value={70} className="h-2" />
                  </div>
                </div>
                <div className="bg-primary/10 mt-4 rounded-md p-3">
                  <h4 className="mb-1 font-medium">Implementation Timeline</h4>
                  <p className="text-muted-foreground text-sm">
                    Recommended changes can be implemented within 2 weeks with
                    minimal disruption to current operations. Full impact
                    expected within 30 days of implementation.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="trends" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Utilization Trends</CardTitle>
            <CardDescription>
              Historical utilization patterns and future projections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted flex h-[300px] items-center justify-center rounded-md">
              <p className="text-muted-foreground">
                Utilization trend chart will be displayed here
              </p>
            </div>
            <div className="mt-4 grid gap-4 md:grid-cols-3">
              <div className="rounded-md border p-3">
                <h3 className="mb-1 font-medium">Peak Utilization Times</h3>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center justify-between">
                    <span>Monday</span>
                    <span>10:00 AM - 2:00 PM</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Tuesday</span>
                    <span>9:00 AM - 1:00 PM</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Wednesday</span>
                    <span>11:00 AM - 3:00 PM</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Thursday</span>
                    <span>9:00 AM - 12:00 PM</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Friday</span>
                    <span>8:00 AM - 11:00 AM</span>
                  </li>
                </ul>
              </div>
              <div className="rounded-md border p-3">
                <h3 className="mb-1 font-medium">Seasonal Patterns</h3>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center justify-between">
                    <span>Winter</span>
                    <span>+15% utilization</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Spring</span>
                    <span>+5% utilization</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Summer</span>
                    <span>-10% utilization</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Fall</span>
                    <span>+8% utilization</span>
                  </li>
                </ul>
              </div>
              <div className="rounded-md border p-3">
                <h3 className="mb-1 font-medium">Year-over-Year Growth</h3>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center justify-between">
                    <span>2023</span>
                    <span>+7.2% utilization</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>2022</span>
                    <span>+5.8% utilization</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>2021</span>
                    <span>+3.5% utilization</span>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>2020</span>
                    <span>-12.3% utilization</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </>
)

export default ClinicUtilization
