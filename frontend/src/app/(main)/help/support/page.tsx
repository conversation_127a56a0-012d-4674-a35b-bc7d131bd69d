"use client"

import React from "react"
import { LifeBuoy, Mail, MessageSquare, Phone } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Support = () => (
  <>
    <Breadcrumb
      links={[{ label: "Help", href: "/help" }, { label: "Support" }]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Support</CardTitle>
      <CardDescription>Get support with OneFlow platform</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Phone className="text-primary h-5 w-5" />
            Phone Support
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4 text-sm">
            Speak directly with our support team for immediate assistance.
          </p>
          <div className="font-medium">+****************</div>
          <div className="text-muted-foreground mt-1 text-xs">
            Available Monday-Friday, 8am-6pm EST
          </div>
          <Button className="mt-4 w-full" variant="outline">
            Request Callback
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MessageSquare className="text-primary h-5 w-5" />
            Live Chat
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4 text-sm">
            Chat with our support team in real-time for quick answers.
          </p>
          <div className="font-medium">Average response time: 2 minutes</div>
          <div className="text-muted-foreground mt-1 text-xs">
            Available 24/7 for premium users
          </div>
          <Button className="mt-4 w-full" variant="outline">
            Start Chat
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Mail className="text-primary h-5 w-5" />
            Email Support
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4 text-sm">
            Send us an email and {`we'll`} respond within 24 hours.
          </p>
          <div className="font-medium"><EMAIL></div>
          <div className="text-muted-foreground mt-1 text-xs">
            Typical response time: 4-6 hours
          </div>
          <Button className="mt-4 w-full" variant="outline">
            Send Email
          </Button>
        </CardContent>
      </Card>
    </div>

    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <LifeBuoy className="text-primary h-5 w-5" />
          Frequently Asked Questions
        </CardTitle>
        <CardDescription>
          Quick answers to common questions about OneFlow
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="border-b pb-3">
            <h3 className="mb-1 font-medium">How do I reset my password?</h3>
            <p className="text-muted-foreground text-sm">
              You can reset your password by clicking on the{" "}
              {`"Forgot Password"`}
              link on the login page. {`You'll`} receive an email with
              instructions to create a new password.
            </p>
          </div>
          <div className="border-b pb-3">
            <h3 className="mb-1 font-medium">
              How do I connect a new data source?
            </h3>
            <p className="text-muted-foreground text-sm">
              Navigate to Settings {`>`} Data Sources and click on{" "}
              {`"Add New Data
              Source"`}
              . Follow the step-by-step wizard to connect your data.
            </p>
          </div>
          <div className="border-b pb-3">
            <h3 className="mb-1 font-medium">Can I export reports to PDF?</h3>
            <p className="text-muted-foreground text-sm">
              Yes, you can export any report by clicking the {`"Export"`} button
              in the top-right corner of the report view and selecting {`"PDF"`}{" "}
              from the dropdown menu.
            </p>
          </div>
          <div>
            <h3 className="mb-1 font-medium">
              How do I add a new user to my account?
            </h3>
            <p className="text-muted-foreground text-sm">
              Go to Settings {`>`} Users & Roles, then click {`"Add User"`}.
              Enter their email address and select the appropriate role.{" "}
              {`They'll`}
              receive an invitation email.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </>
)

export default Support
