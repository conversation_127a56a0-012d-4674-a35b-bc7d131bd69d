"use client"

import React from "react"
import {
  BookOpen,
  FileText,
  HelpCircle,
  LifeBuoy,
  MessageSquare,
  Search,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Help = () => (
  <>
    <Breadcrumb links={[{ label: "Help" }]} />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Help</CardTitle>
      <CardDescription>Get help with OneFlow platform</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center gap-2">
      <div className="relative flex-1">
        <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
        <Input
          type="search"
          placeholder="Search for help..."
          className="pl-8"
        />
      </div>
      <Button variant="outline">Search</Button>
    </div>

    <div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="text-primary h-5 w-5" />
            Documentation
          </CardTitle>
          <CardDescription>
            Browse comprehensive guides and tutorials
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4 text-sm">
            Access detailed documentation on how to use all features of the
            OneFlow platform.
          </p>
          <Button className="w-full" variant="outline" asChild>
            <a href="/help/documentation">View Documentation</a>
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LifeBuoy className="text-primary h-5 w-5" />
            Support
          </CardTitle>
          <CardDescription>Get help from our support team</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4 text-sm">
            Contact our support team via phone, email, or live chat for
            personalized assistance.
          </p>
          <Button className="w-full" variant="outline" asChild>
            <a href="/help/support">Contact Support</a>
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="text-primary h-5 w-5" />
            Community Forum
          </CardTitle>
          <CardDescription>Connect with other OneFlow users</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4 text-sm">
            Join discussions, share insights, and learn from other healthcare
            professionals using OneFlow.
          </p>
          <Button className="w-full" variant="outline">
            Visit Forum
          </Button>
        </CardContent>
      </Card>
    </div>

    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Frequently Asked Questions</CardTitle>
        <CardDescription>Quick answers to common questions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="border-b pb-3">
            <h3 className="mb-1 flex items-center gap-2 font-medium">
              <HelpCircle className="text-primary h-4 w-4" />
              How do I get started with OneFlow?
            </h3>
            <p className="text-muted-foreground text-sm">
              We recommend starting with our onboarding guide that walks you
              through the initial setup process. Visit the Documentation section
              and look for {`"Getting Started"`} guide.
            </p>
          </div>
          <div className="border-b pb-3">
            <h3 className="mb-1 flex items-center gap-2 font-medium">
              <HelpCircle className="text-primary h-4 w-4" />
              How can I connect my existing healthcare systems?
            </h3>
            <p className="text-muted-foreground text-sm">
              OneFlow supports integration with most major healthcare systems
              through our API and dedicated connectors. Check the{" "}
              {`"Data
              Integration"`}{" "}
              section in our documentation or contact support for assistance.
            </p>
          </div>
          <div className="border-b pb-3">
            <h3 className="mb-1 flex items-center gap-2 font-medium">
              <HelpCircle className="text-primary h-4 w-4" />
              Is my data secure with OneFlow?
            </h3>
            <p className="text-muted-foreground text-sm">
              Yes, OneFlow is HIPAA compliant and uses industry-leading
              encryption and security practices. All data is encrypted both in
              transit and at rest, and we maintain strict access controls.
            </p>
          </div>
          <div>
            <h3 className="mb-1 flex items-center gap-2 font-medium">
              <HelpCircle className="text-primary h-4 w-4" />
              How can I request a new feature?
            </h3>
            <p className="text-muted-foreground text-sm">
              We welcome feature requests! You can submit them through our
              support portal or discuss them in our community forum. Our product
              team regularly reviews user suggestions for future updates.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>

    <div className="mt-6 grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Video Tutorials</CardTitle>
          <CardDescription>
            Learn through step-by-step video guides
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            <li className="flex items-center gap-2">
              <FileText className="text-muted-foreground h-4 w-4" />
              <a href="#" className="text-sm hover:underline">
                Getting Started with OneFlow (12:34)
              </a>
            </li>
            <li className="flex items-center gap-2">
              <FileText className="text-muted-foreground h-4 w-4" />
              <a href="#" className="text-sm hover:underline">
                Data Integration Guide (18:22)
              </a>
            </li>
            <li className="flex items-center gap-2">
              <FileText className="text-muted-foreground h-4 w-4" />
              <a href="#" className="text-sm hover:underline">
                Creating Custom Reports (15:47)
              </a>
            </li>
            <li className="flex items-center gap-2">
              <FileText className="text-muted-foreground h-4 w-4" />
              <a href="#" className="text-sm hover:underline">
                Advanced Analytics Features (20:15)
              </a>
            </li>
          </ul>
          <Button variant="link" className="mt-2 h-auto p-0">
            View All Videos
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Latest Updates</CardTitle>
          <CardDescription>Recent changes and improvements</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium">
                Version 2.4.0 - Released May 15, 2024
              </h4>
              <ul className="text-muted-foreground mt-1 list-disc space-y-1 pl-5 text-sm">
                <li>Added new patient retention analysis tools</li>
                <li>Improved dashboard performance by 35%</li>
                <li>Enhanced data visualization options</li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-medium">
                Version 2.3.5 - Released April 2, 2024
              </h4>
              <ul className="text-muted-foreground mt-1 list-disc space-y-1 pl-5 text-sm">
                <li>Fixed issue with financial report exports</li>
                <li>Added support for custom date ranges</li>
                <li>New integration with Epic EHR system</li>
              </ul>
            </div>
          </div>
          <Button variant="link" className="mt-2 h-auto p-0">
            View All Updates
          </Button>
        </CardContent>
      </Card>
    </div>
  </>
)

export default Help
