"use client"

import React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>old<PERSON>, <PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Documentation = () => (
  <>
    <Breadcrumb
      links={[{ label: "Help", href: "/help" }, { label: "Documentation" }]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Documentation</CardTitle>
      <CardDescription>Learn how to use OneFlow platform</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center gap-2">
      <div className="relative flex-1">
        <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
        <Input
          type="search"
          placeholder="Search documentation..."
          className="pl-8"
        />
      </div>
      <Button variant="outline">Search</Button>
    </div>

    <Tabs defaultValue="guides" className="mt-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="guides">User Guides</TabsTrigger>
        <TabsTrigger value="api">API Reference</TabsTrigger>
        <TabsTrigger value="videos">Video Tutorials</TabsTrigger>
      </TabsList>
      <TabsContent value="guides" className="mt-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg">
                <BookOpen className="text-primary h-5 w-5" />
                Getting Started
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Platform Overview
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Account Setup
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Navigation Guide
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    User Roles & Permissions
                  </a>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Folder className="text-primary h-5 w-5" />
                Data Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Connecting Data Sources
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Data Import/Export
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    ETL Process Guide
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Data Quality Management
                  </a>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Folder className="text-primary h-5 w-5" />
                Analytics & Reporting
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Dashboard Customization
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Creating Reports
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    KPI Configuration
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Data Visualization Guide
                  </a>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Folder className="text-primary h-5 w-5" />
                Financial Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Revenue Analysis
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Expense Management
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Budget Planning
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Financial Reporting
                  </a>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Folder className="text-primary h-5 w-5" />
                Operations Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Staff Scheduling
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Clinic Utilization
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Appointment Management
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Resource Optimization
                  </a>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Folder className="text-primary h-5 w-5" />
                Marketing Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Campaign Management
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Patient Acquisition
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    ROI Tracking
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <FileText className="text-muted-foreground h-4 w-4" />
                  <a href="#" className="hover:underline">
                    Marketing Analytics
                  </a>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      <TabsContent value="api" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>API Documentation</CardTitle>
            <CardDescription>
              Integrate with OneFlow using our comprehensive API
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4 text-sm">
              Our RESTful API allows you to programmatically access and manage
              your OneFlow data. Use the documentation below to get started with
              API integration.
            </p>
            <div className="space-y-4">
              <div className="rounded-md border p-4">
                <h3 className="mb-2 font-medium">Authentication</h3>
                <p className="text-muted-foreground text-sm">
                  Learn how to authenticate your API requests using OAuth 2.0 or
                  API keys.
                </p>
                <Button variant="link" className="mt-2 h-auto p-0">
                  View Documentation
                </Button>
              </div>
              <div className="rounded-md border p-4">
                <h3 className="mb-2 font-medium">Endpoints Reference</h3>
                <p className="text-muted-foreground text-sm">
                  Complete reference of all available API endpoints, parameters,
                  and response formats.
                </p>
                <Button variant="link" className="mt-2 h-auto p-0">
                  View Documentation
                </Button>
              </div>
              <div className="rounded-md border p-4">
                <h3 className="mb-2 font-medium">Code Examples</h3>
                <p className="text-muted-foreground text-sm">
                  Sample code in multiple languages to help you get started
                  quickly.
                </p>
                <Button variant="link" className="mt-2 h-auto p-0">
                  View Examples
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="videos" className="mt-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <div className="bg-muted mb-2 aspect-video rounded-md"></div>
              <CardTitle className="text-lg">
                Getting Started with OneFlow
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-2 text-sm">
                A comprehensive overview of the OneFlow platform and its key
                features.
              </p>
              <div className="text-muted-foreground text-xs">
                Duration: 12:34 • Updated: 2 months ago
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <div className="bg-muted mb-2 aspect-video rounded-md"></div>
              <CardTitle className="text-lg">Data Integration Guide</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-2 text-sm">
                Learn how to connect and integrate your existing data sources
                with OneFlow.
              </p>
              <div className="text-muted-foreground text-xs">
                Duration: 18:22 • Updated: 1 month ago
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <div className="bg-muted mb-2 aspect-video rounded-md"></div>
              <CardTitle className="text-lg">Creating Custom Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-2 text-sm">
                Step-by-step tutorial on creating and customizing reports in
                OneFlow.
              </p>
              <div className="text-muted-foreground text-xs">
                Duration: 15:47 • Updated: 3 weeks ago
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  </>
)

export default Documentation
