"use client"

import React from "react"

import ClinicPatientInsights from "@/components/summary/ClinicPatientInsights"
import DateFilter from "@/components/summary/DateFilter"
import FinancialPNL from "@/components/summary/FinancialPNL"
import IVFInsights from "@/components/summary/IVFInsights"
import OperationsHR from "@/components/summary/OperationsHR"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Summary = () => {
  return (
    <>
      <Breadcrumb links={[{ label: "Summary" }]} />

      <DateFilter />

      <FinancialPNL />

      <IVFInsights />

      <ClinicPatientInsights />

      <OperationsHR />
    </>
  )
}

export default Summary
