import React, { useState } from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON>s,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define the base data point interface with common properties
interface BaseDataPoint {
  current: number
  budget: number
  previous: number
}

// Define specific data point types for different views
interface MonthDataPoint extends BaseDataPoint {
  month: string
}

interface DoctorDataPoint extends BaseDataPoint {
  doctor: string
}

interface DepartmentDataPoint extends BaseDataPoint {
  department: string
}

interface ProcedureDataPoint extends BaseDataPoint {
  procedure: string
}

// Define the view types
type ViewType = "month" | "doctor" | "department" | "procedure"

// Define the props for the DrilldownModal
interface DrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: MonthDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const doctorData: DoctorDataPoint[] = [
  {
    doctor: "Dr. <PERSON>",
    current: 1200,
    budget: 1300,
    previous: 1100,
  },
  {
    doctor: "Dr. <PERSON>",
    current: 1400,
    budget: 1450,
    previous: 1250,
  },
  {
    doctor: "Dr. Williams",
    current: 1600,
    budget: 1550,
    previous: 1400,
  },
  {
    doctor: "Dr. <PERSON>",
    current: 1300,
    budget: 1400,
    previous: 1200,
  },
  {
    doctor: "Dr. Davis",
    current: 1500,
    budget: 1600,
    previous: 1350,
  },
]

const departmentData: DepartmentDataPoint[] = [
  {
    department: "Cardiology",
    current: 1800,
    budget: 1700,
    previous: 1600,
  },
  {
    department: "Neurology",
    current: 1500,
    budget: 1600,
    previous: 1400,
  },
  {
    department: "Oncology",
    current: 1700,
    budget: 1650,
    previous: 1500,
  },
  {
    department: "Pediatrics",
    current: 1400,
    budget: 1500,
    previous: 1300,
  },
]

const procedureData: ProcedureDataPoint[] = [
  {
    procedure: "Consultation",
    current: 900,
    budget: 950,
    previous: 850,
  },
  {
    procedure: "Surgery",
    current: 2100,
    budget: 2000,
    previous: 1900,
  },
  {
    procedure: "Diagnostic",
    current: 1300,
    budget: 1350,
    previous: 1200,
  },
  {
    procedure: "Therapy",
    current: 1100,
    budget: 1150,
    previous: 1000,
  },
]

const DrilldownModal: React.FC<DrilldownModalProps> = ({
  open,
  onOpenChange,
  initialData,
  title,
  description,
}) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("month")

  // Get the appropriate data based on the view type
  const getDataForView = () => {
    switch (viewType) {
      case "doctor":
        return doctorData as unknown as BaseDataPoint[]
      case "department":
        return departmentData as unknown as BaseDataPoint[]
      case "procedure":
        return procedureData as unknown as BaseDataPoint[]
      default:
        return initialData as unknown as BaseDataPoint[]
    }
  }

  // Get the appropriate x-axis key based on the view type
  const getXAxisKey = () => {
    switch (viewType) {
      case "doctor":
        return "doctor"
      case "department":
        return "department"
      case "procedure":
        return "procedure"
      default:
        return "month"
    }
  }

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "doctor":
        return "Operating Income by Doctor"
      case "department":
        return "Operating Income by Department"
      case "procedure":
        return "Operating Income by Procedure"
      default:
        return "Operating Income Trend Analysis"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "doctor":
        return "This chart displays operating income by doctor comparing current year performance (blue bars) against budget targets (orange line) and previous year results (green line)."
      case "department":
        return "This chart displays operating income by department comparing current year performance (blue bars) against budget targets (orange line) and previous year results (green line)."
      case "procedure":
        return "This chart displays operating income by procedure comparing current year performance (blue bars) against budget targets (orange line) and previous year results (green line)."
      default:
        return "This chart displays monthly operating income trends comparing current year performance (blue bars) against budget targets (orange line) and previous year results (green line). The visualization helps identify seasonal patterns, year-over-year growth, and performance against financial goals."
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">Month</SelectItem>
                <SelectItem value="doctor">Doctor</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="procedure">Procedure</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            <ResponsiveContainer width="100%" height={400}>
              <ComposedChart data={getDataForView()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={getXAxisKey()} tick={{ fontSize: 12 }} />
                <YAxis
                  domain={[1000, 2200]}
                  tickFormatter={(v) => `$${(v / 1000).toFixed(1)}K`}
                />
                <Tooltip
                  formatter={(value: number) => `$${value.toLocaleString()}`}
                />
                <Legend verticalAlign="bottom" />
                <Bar
                  dataKey="current"
                  name="Current Year"
                  fill="var(--chart-1)"
                  barSize={32}
                />
                <Line
                  type="monotone"
                  dataKey="budget"
                  name="Budget"
                  stroke="var(--chart-3)"
                  strokeWidth={3}
                  dot={{ stroke: "var(--chart-3)" }}
                />
                <Line
                  type="monotone"
                  dataKey="previous"
                  name="Previous Year"
                  stroke="var(--chart-5)"
                  strokeWidth={3}
                  dot={{ stroke: "var(--chart-5)" }}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default DrilldownModal
