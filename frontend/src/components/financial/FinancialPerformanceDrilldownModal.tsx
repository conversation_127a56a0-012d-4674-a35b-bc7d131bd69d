import React, { useState } from "react"
import {
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface FinancialPerformanceDataPoint {
  unit: string
  revenue: number
  expenses: number
  profit: number
  target: number
  profitMargin: number
  roi: number
}

// Define the view types
type ViewType =
  | "combined"
  | "revenue-expenses"
  | "profitability"
  | "trend"
  | "comparison"

// Define the props for the FinancialPerformanceDrilldownModal
interface FinancialPerformanceDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: FinancialPerformanceDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const trendData = [
  {
    month: "Jan",
    revenue: 1200000,
    expenses: 800000,
    profit: 400000,
    profitMargin: 33.3,
    roi: 50.0,
  },
  {
    month: "Feb",
    revenue: 1250000,
    expenses: 820000,
    profit: 430000,
    profitMargin: 34.4,
    roi: 52.4,
  },
  {
    month: "Mar",
    revenue: 1300000,
    expenses: 840000,
    profit: 460000,
    profitMargin: 35.4,
    roi: 54.8,
  },
  {
    month: "Apr",
    revenue: 1350000,
    expenses: 860000,
    profit: 490000,
    profitMargin: 36.3,
    roi: 57.0,
  },
  {
    month: "May",
    revenue: 1400000,
    expenses: 880000,
    profit: 520000,
    profitMargin: 37.1,
    roi: 59.1,
  },
  {
    month: "Jun",
    revenue: 1450000,
    expenses: 900000,
    profit: 550000,
    profitMargin: 37.9,
    roi: 61.1,
  },
]

const comparisonData = [
  {
    year: "2021",
    revenue: 1100000,
    expenses: 750000,
    profit: 350000,
    profitMargin: 31.8,
    roi: 46.7,
  },
  {
    year: "2022",
    revenue: 1250000,
    expenses: 820000,
    profit: 430000,
    profitMargin: 34.4,
    roi: 52.4,
  },
  {
    year: "2023",
    revenue: 1400000,
    expenses: 880000,
    profit: 520000,
    profitMargin: 37.1,
    roi: 59.1,
  },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const FinancialPerformanceDrilldownModal: React.FC<
  FinancialPerformanceDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("combined")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "revenue-expenses":
        return "Revenue vs Expenses by Business Unit"
      case "profitability":
        return "Profitability Metrics by Business Unit"
      case "trend":
        return "Financial Performance Trends"
      case "comparison":
        return "Year-over-Year Financial Comparison"
      default:
        return "Financial Performance Analysis"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "revenue-expenses":
        return "This chart compares revenue and expenses across different business units, showing the absolute financial performance of each unit with Gastroenterology generating the highest revenue at $550K and Cardiology the lowest at $300K."
      case "profitability":
        return "This chart focuses on profitability metrics (profit margin and ROI) across business units, revealing that Gastroenterology leads with 41.82% profit margin and 71.88% ROI, while Cardiology shows the lowest metrics but still maintains healthy 33.33% profit margin and 50% ROI."
      case "trend":
        return "This chart tracks monthly financial performance metrics over the past six months, showing consistent growth in revenue, expenses, profit, profit margin, and ROI across the organization."
      case "comparison":
        return "This chart compares key financial metrics across three years, demonstrating consistent improvement with revenue growing by 27.3%, profit by 48.6%, profit margin by 5.3 percentage points, and ROI by 12.4 percentage points from 2021 to 2023."
      default:
        return "This chart provides a comprehensive view of financial performance across business units, showing revenue and expenses as bars on the left axis and profit margin/ROI as lines on the right axis."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "revenue-expenses":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="unit" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="revenue" name="Revenue" fill={COLORS[0]} />
              <Bar dataKey="expenses" name="Expenses" fill={COLORS[1]} />
              <Bar dataKey="profit" name="Profit" fill={COLORS[2]} />
            </BarChart>
          </ResponsiveContainer>
        )
      case "profitability":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="unit" />
              <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
              <Tooltip formatter={(value: number) => `${value}%`} />
              <Legend />
              <Bar
                dataKey="profitMargin"
                name="Profit Margin"
                fill={COLORS[0]}
              />
              <Bar dataKey="roi" name="ROI" fill={COLORS[1]} />
            </BarChart>
          </ResponsiveContainer>
        )
      case "trend":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis
                yAxisId="left"
                tickFormatter={(value) => `$${value / 1000}k`}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "profitMargin" || name === "roi")
                    return [`${value}%`, name]
                  return [`$${(value as number).toLocaleString()}`, name]
                }}
              />
              <Legend />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="revenue"
                name="Revenue"
                stroke={COLORS[0]}
                strokeWidth={2}
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="expenses"
                name="Expenses"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="profit"
                name="Profit"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="profitMargin"
                name="Profit Margin"
                stroke={COLORS[3]}
                strokeWidth={2}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="roi"
                name="ROI"
                stroke={COLORS[4]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                yAxisId="left"
                tickFormatter={(value) => `$${value / 1000}k`}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "profitMargin" || name === "roi")
                    return [`${value}%`, name]
                  return [`$${(value as number).toLocaleString()}`, name]
                }}
              />
              <Legend />
              <Bar
                yAxisId="left"
                dataKey="revenue"
                name="Revenue"
                fill={COLORS[0]}
              />
              <Bar
                yAxisId="left"
                dataKey="expenses"
                name="Expenses"
                fill={COLORS[1]}
              />
              <Bar
                yAxisId="left"
                dataKey="profit"
                name="Profit"
                fill={COLORS[2]}
              />
              <Bar
                yAxisId="right"
                dataKey="profitMargin"
                name="Profit Margin"
                fill={COLORS[3]}
              />
              <Bar yAxisId="right" dataKey="roi" name="ROI" fill={COLORS[4]} />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="unit" />
              <YAxis
                yAxisId="left"
                tickFormatter={(value) => `$${value / 1000}k`}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "profitMargin" || name === "roi")
                    return [`${value}%`, name]
                  return [`$${(value as number).toLocaleString()}`, name]
                }}
              />
              <Legend />
              <Bar
                yAxisId="left"
                dataKey="revenue"
                stackId="a"
                fill={COLORS[0]}
                name="Revenue"
              />
              <Bar
                yAxisId="left"
                dataKey="expenses"
                stackId="a"
                fill={COLORS[1]}
                name="Expenses"
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="profitMargin"
                stroke={COLORS[3]}
                name="Profit Margin"
                strokeWidth={2}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="roi"
                stroke={COLORS[4]}
                name="ROI"
                strokeWidth={2}
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="target"
                stroke={COLORS[2]}
                name="Target"
                strokeWidth={2}
              />
            </ComposedChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="combined">Combined View</SelectItem>
                <SelectItem value="revenue-expenses">
                  Revenue & Expenses
                </SelectItem>
                <SelectItem value="profitability">
                  Profitability Metrics
                </SelectItem>
                <SelectItem value="trend">Monthly Trends</SelectItem>
                <SelectItem value="comparison">Yearly Comparison</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default FinancialPerformanceDrilldownModal
