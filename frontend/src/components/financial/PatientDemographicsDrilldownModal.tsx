import React, { useState } from "react"
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON>s,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface PatientDemographicsDataPoint {
  name: string
  value: number
  revenue: number
  visits: number
}

// Define the view types
type ViewType = "pie" | "revenue" | "visits" | "comparison"

// Define the props for the PatientDemographicsDrilldownModal
interface PatientDemographicsDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: PatientDemographicsDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const revenueData = [
  { name: "65+", value: 380000, revenuePerPatient: 950 },
  { name: "45-64", value: 320000, revenuePerPatient: 800 },
  { name: "30-44", value: 220000, revenuePerPatient: 550 },
  { name: "18-29", value: 180000, revenuePerPatient: 450 },
  { name: "0-17", value: 150000, revenuePerPatient: 375 },
]

const visitsData = [
  { name: "65+", value: 400, visitsPerPatient: 8.0 },
  { name: "45-64", value: 400, visitsPerPatient: 6.5 },
  { name: "30-44", value: 400, visitsPerPatient: 4.0 },
  { name: "18-29", value: 400, visitsPerPatient: 3.0 },
  { name: "0-17", value: 400, visitsPerPatient: 3.5 },
]

const comparisonData = [
  { name: "65+", patients: 400, revenue: 380000, visits: 3200 },
  { name: "45-64", patients: 400, revenue: 320000, visits: 2600 },
  { name: "30-44", patients: 400, revenue: 220000, visits: 1600 },
  { name: "18-29", patients: 400, revenue: 180000, visits: 1200 },
  { name: "0-17", patients: 400, revenue: 150000, visits: 1400 },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const PatientDemographicsDrilldownModal: React.FC<
  PatientDemographicsDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("pie")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "revenue":
        return "Revenue by Age Group"
      case "visits":
        return "Visits by Age Group"
      case "comparison":
        return "Comprehensive Demographic Comparison"
      default:
        return "Patient Distribution by Age Group"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "revenue":
        return "This chart shows revenue distribution across age groups, with the 65+ demographic generating the highest revenue at $380K (30.4% of total) and highest revenue per patient at $950, while the 0-17 group generates the lowest at $150K (12% of total) and $375 per patient."
      case "visits":
        return "This chart analyzes visit patterns across age groups, showing that the 65+ demographic has the highest visit frequency at 8.0 visits per patient annually, while the 18-29 group has the lowest at 3.0 visits per patient."
      case "comparison":
        return "This comprehensive chart compares patient count, revenue, and visits across age groups, revealing that while each group has the same number of patients (400), the 65+ group generates 2.5x more revenue and 2.7x more visits than the 18-29 group."
      default:
        return "This pie chart shows the distribution of patients across five age groups, with each segment representing 20% of the patient population, indicating an even distribution across demographics."
    }
  }

  // Calculate total for percentages
  const total = initialData.reduce((sum, item) => sum + item.value, 0)

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "revenue":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={revenueData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                type="number"
                tickFormatter={(value) => `$${value / 1000}k`}
              />
              <YAxis type="category" dataKey="name" width={80} />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "revenuePerPatient")
                    return [`$${value}`, "Revenue Per Patient"]
                  return [
                    `$${(value as number).toLocaleString()}`,
                    "Total Revenue",
                  ]
                }}
              />
              <Legend />
              <Bar dataKey="value" name="Revenue" fill={COLORS[0]} />
              <Bar
                dataKey="revenuePerPatient"
                name="Revenue Per Patient"
                fill={COLORS[1]}
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "visits":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={visitsData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis type="category" dataKey="name" width={80} />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" name="Patients" fill={COLORS[0]} />
              <Bar
                dataKey="visitsPerPatient"
                name="Visits Per Patient"
                fill={COLORS[1]}
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" />
              <YAxis
                yAxisId="right"
                orientation="right"
                tickFormatter={(value) => `$${value / 1000}k`}
              />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "revenue")
                    return [`$${(value as number).toLocaleString()}`, name]
                  return [value, name]
                }}
              />
              <Legend />
              <Bar
                yAxisId="left"
                dataKey="patients"
                name="Patients"
                fill={COLORS[0]}
              />
              <Bar
                yAxisId="left"
                dataKey="visits"
                name="Visits"
                fill={COLORS[1]}
              />
              <Bar
                yAxisId="right"
                dataKey="revenue"
                name="Revenue"
                fill={COLORS[2]}
              />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={initialData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={150}
                fill="#8884d8"
                label={({ name, value }) =>
                  `${name}: ${((value / total) * 100).toFixed(1)}%`
                }
              >
                {initialData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) =>
                  `${value} patients (${(((value as number) / total) * 100).toFixed(1)}%)`
                }
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pie">Patient Distribution</SelectItem>
                <SelectItem value="revenue">Revenue Analysis</SelectItem>
                <SelectItem value="visits">Visit Patterns</SelectItem>
                <SelectItem value="comparison">Comprehensive View</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default PatientDemographicsDrilldownModal
