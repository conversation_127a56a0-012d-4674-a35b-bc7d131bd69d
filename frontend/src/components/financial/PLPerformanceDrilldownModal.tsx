import React, { useState } from "react"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface PLPerformanceDataPoint {
  metric: string
  actual: number
  forecast: number
  target: number
}

// Define the view types
type ViewType = "bar" | "variance" | "trend" | "comparison"

// Define the props for the PLPerformanceDrilldownModal
interface PLPerformanceDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: PLPerformanceDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const varianceData = [
  {
    metric: "Revenue",
    actual: 9800,
    target: 10000,
    variance: -2.0,
    status: "Below Target",
  },
  {
    metric: "Gross Profit",
    actual: 6800,
    target: 7000,
    variance: -2.9,
    status: "Below Target",
  },
  {
    metric: "EBITDA",
    actual: 2040,
    target: 3000,
    variance: -32.0,
    status: "Critical",
  },
  {
    metric: "Net Income",
    actual: 2156,
    target: 2200,
    variance: -2.0,
    status: "Below Target",
  },
]

const trendData = [
  {
    month: "Jan",
    revenue: 9200,
    grossProfit: 6400,
    ebitda: 1900,
    netIncome: 2000,
  },
  {
    month: "Feb",
    revenue: 9400,
    grossProfit: 6500,
    ebitda: 1950,
    netIncome: 2050,
  },
  {
    month: "Mar",
    revenue: 9500,
    grossProfit: 6600,
    ebitda: 2000,
    netIncome: 2100,
  },
  {
    month: "Apr",
    revenue: 9600,
    grossProfit: 6700,
    ebitda: 2020,
    netIncome: 2120,
  },
  {
    month: "May",
    revenue: 9800,
    grossProfit: 6800,
    ebitda: 2040,
    netIncome: 2156,
  },
]

const comparisonData = [
  {
    year: "2021",
    revenue: 8500,
    grossProfit: 5900,
    ebitda: 1700,
    netIncome: 1800,
  },
  {
    year: "2022",
    revenue: 9200,
    grossProfit: 6400,
    ebitda: 1900,
    netIncome: 2000,
  },
  {
    year: "2023",
    revenue: 9800,
    grossProfit: 6800,
    ebitda: 2040,
    netIncome: 2156,
  },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const PLPerformanceDrilldownModal: React.FC<
  PLPerformanceDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("bar")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "variance":
        return "P&L Performance Variance Analysis"
      case "trend":
        return "P&L Performance Trend Analysis"
      case "comparison":
        return "Year-over-Year P&L Comparison"
      default:
        return "P&L Performance vs Targets"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "variance":
        return "This chart analyzes the variance between actual performance and targets across key P&L metrics, highlighting that EBITDA is significantly below target (-32%) while other metrics show smaller variances around -2%."
      case "trend":
        return "This chart tracks monthly performance across key P&L metrics over the past five months, showing consistent growth in all areas with Revenue increasing by 6.5%, Gross Profit by 6.3%, EBITDA by 7.4%, and Net Income by 7.8%."
      case "comparison":
        return "This chart compares P&L performance across three years, demonstrating consistent improvement with Revenue growing by 15.3%, Gross Profit by 15.3%, EBITDA by 20%, and Net Income by 19.8% from 2021 to 2023."
      default:
        return "This chart compares actual financial performance (blue bars) against forecasted projections (orange bars) and established targets (green bars) across key P&L metrics."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "variance":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={varianceData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                type="number"
                domain={[-40, 10]}
                tickFormatter={(v) => `${v}%`}
              />
              <YAxis type="category" dataKey="metric" width={100} />
              <Tooltip formatter={(value: number) => `${value}%`} />
              <Legend />
              <Bar dataKey="variance" name="Variance %" fill={COLORS[0]}>
                {varianceData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={
                      entry.variance < -20
                        ? "var(--chart-4)"
                        : entry.variance < 0
                          ? "var(--chart-3)"
                          : "var(--chart-1)"
                    }
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )
      case "trend":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="revenue"
                name="Revenue"
                stroke={COLORS[0]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="grossProfit"
                name="Gross Profit"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="ebitda"
                name="EBITDA"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="netIncome"
                name="Net Income"
                stroke={COLORS[3]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="revenue" name="Revenue" fill={COLORS[0]} />
              <Bar dataKey="grossProfit" name="Gross Profit" fill={COLORS[1]} />
              <Bar dataKey="ebitda" name="EBITDA" fill={COLORS[2]} />
              <Bar dataKey="netIncome" name="Net Income" fill={COLORS[3]} />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="metric" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value) => `$${(value as number).toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="actual" fill={COLORS[0]} name="Actual" />
              <Bar dataKey="forecast" fill={COLORS[1]} name="Forecast" />
              <Bar dataKey="target" fill={COLORS[2]} name="Target" />
            </ComposedChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">Bar Chart</SelectItem>
                <SelectItem value="variance">Variance Analysis</SelectItem>
                <SelectItem value="trend">Monthly Trends</SelectItem>
                <SelectItem value="comparison">Yearly Comparison</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default PLPerformanceDrilldownModal
