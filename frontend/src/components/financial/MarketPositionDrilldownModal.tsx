import React, { useState } from "react"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON>hart,
  Tooltip,
  XAxis,
  YAxis,
  ZAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface MarketPositionDataPoint {
  name: string
  value: number
  growth: number
  marketShare: number
}

// Define the view types
type ViewType = "pie" | "growth" | "scatter" | "trend"

// Define the props for the MarketPositionDrilldownModal
interface MarketPositionDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: MarketPositionDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const growthData = [
  { name: "Your Hospital", marketShare: 28, growth: 8.5 },
  { name: "Memorial Health", marketShare: 22, growth: 5.2 },
  { name: "City Medical", marketShare: 18, growth: 3.8 },
  { name: "University Hospital", marketShare: 15, growth: 6.5 },
  { name: "Regional Care", marketShare: 12, growth: 4.2 },
  { name: "Others", marketShare: 5, growth: 2.0 },
]

const trendData = [
  {
    quarter: "Q1 2022",
    yourHospital: 24,
    memorial: 20,
    city: 16,
    university: 12,
    regional: 10,
  },
  {
    quarter: "Q2 2022",
    yourHospital: 25,
    memorial: 20,
    city: 17,
    university: 13,
    regional: 10,
  },
  {
    quarter: "Q3 2022",
    yourHospital: 26,
    memorial: 21,
    city: 17,
    university: 13,
    regional: 11,
  },
  {
    quarter: "Q4 2022",
    yourHospital: 26,
    memorial: 21,
    city: 18,
    university: 14,
    regional: 11,
  },
  {
    quarter: "Q1 2023",
    yourHospital: 27,
    memorial: 21,
    city: 18,
    university: 14,
    regional: 12,
  },
  {
    quarter: "Q2 2023",
    yourHospital: 28,
    memorial: 22,
    city: 18,
    university: 15,
    regional: 12,
  },
]

const scatterData = [
  { name: "Your Hospital", x: 28, y: 8.5, z: 250 },
  { name: "Memorial Health", x: 22, y: 5.2, z: 200 },
  { name: "City Medical", x: 18, y: 3.8, z: 180 },
  { name: "University Hospital", x: 15, y: 6.5, z: 150 },
  { name: "Regional Care", x: 12, y: 4.2, z: 120 },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
  "var(--chart-6)",
]

const MarketPositionDrilldownModal: React.FC<
  MarketPositionDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("pie")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "growth":
        return "Market Share vs Growth Rate"
      case "scatter":
        return "Competitive Positioning Analysis"
      case "trend":
        return "Market Share Trend Analysis"
      default:
        return "Market Share Distribution"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "growth":
        return "This chart compares market share and growth rates across competitors, showing that Your Hospital leads with 28% market share and 8.5% growth rate, significantly outperforming Memorial Health (22% share, 5.2% growth) and other competitors."
      case "scatter":
        return "This scatter plot positions competitors based on market share (x-axis), growth rate (y-axis), and revenue volume (bubble size), highlighting Your Hospital's dominant position with the largest market share, highest growth rate, and largest revenue volume."
      case "trend":
        return "This chart tracks market share trends over six quarters, showing Your Hospital consistently increasing its lead from 24% to 28%, while competitors show more modest growth or stagnation, indicating effective competitive strategy."
      default:
        return "This pie chart shows the distribution of market share across major competitors, with Your Hospital leading at 28%, followed by Memorial Health at 22%, City Medical at 18%, University Hospital at 15%, Regional Care at 12%, and Others at 5%."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "growth":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={growthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis
                yAxisId="left"
                label={{
                  value: "Market Share (%)",
                  angle: -90,
                  position: "insideLeft",
                }}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                label={{
                  value: "Growth Rate (%)",
                  angle: 90,
                  position: "insideRight",
                }}
              />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar
                yAxisId="left"
                dataKey="marketShare"
                name="Market Share"
                fill={COLORS[0]}
              />
              <Bar
                yAxisId="right"
                dataKey="growth"
                name="Growth Rate"
                fill={COLORS[1]}
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "scatter":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <ScatterChart>
              <CartesianGrid />
              <XAxis
                type="number"
                dataKey="x"
                name="Market Share"
                unit="%"
                domain={[0, 30]}
              />
              <YAxis
                type="number"
                dataKey="y"
                name="Growth Rate"
                unit="%"
                domain={[0, 10]}
              />
              <ZAxis type="number" dataKey="z" range={[100, 500]} />
              <Tooltip
                cursor={{ strokeDasharray: "3 3" }}
                formatter={(value, name) => {
                  if (name === "x") return [`${value}%`, "Market Share"]
                  if (name === "y") return [`${value}%`, "Growth Rate"]
                  if (name === "z") return [`$${value}M`, "Revenue"]
                  return [value, name]
                }}
              />
              <Legend />
              <Scatter name="Hospitals" data={scatterData} fill={COLORS[0]} />
            </ScatterChart>
          </ResponsiveContainer>
        )
      case "trend":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="quarter" />
              <YAxis domain={[0, 30]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="yourHospital"
                name="Your Hospital"
                stroke={COLORS[0]}
                strokeWidth={3}
              />
              <Line
                type="monotone"
                dataKey="memorial"
                name="Memorial Health"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="city"
                name="City Medical"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="university"
                name="University Hospital"
                stroke={COLORS[3]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="regional"
                name="Regional Care"
                stroke={COLORS[4]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={initialData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={150}
                fill="#8884d8"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {initialData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pie">Market Share</SelectItem>
                <SelectItem value="growth">Share vs Growth</SelectItem>
                <SelectItem value="scatter">Competitive Position</SelectItem>
                <SelectItem value="trend">Trend Analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default MarketPositionDrilldownModal
