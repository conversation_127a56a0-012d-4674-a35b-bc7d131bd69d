import React, { useState } from "react"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface OperationalMetricsDataPoint {
  name: string
  value: number
  target: number
  industry: number
}

// Define the view types
type ViewType = "bar" | "radar" | "trend" | "comparison"

// Define the props for the OperationalMetricsDrilldownModal
interface OperationalMetricsDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: OperationalMetricsDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const trendData = [
  {
    month: "Jan",
    costPerVisit: 125,
    bedOccupancy: 78,
    staffUtilization: 82,
    patientWait: 42,
  },
  {
    month: "Feb",
    costPerVisit: 123,
    bedOccupancy: 79,
    staffUtilization: 83,
    patientWait: 40,
  },
  {
    month: "Mar",
    costPerVisit: 120,
    bedOccupancy: 80,
    staffUtilization: 84,
    patientWait: 38,
  },
  {
    month: "Apr",
    costPerVisit: 118,
    bedOccupancy: 82,
    staffUtilization: 85,
    patientWait: 36,
  },
  {
    month: "May",
    costPerVisit: 115,
    bedOccupancy: 83,
    staffUtilization: 86,
    patientWait: 34,
  },
  {
    month: "Jun",
    costPerVisit: 112,
    bedOccupancy: 85,
    staffUtilization: 88,
    patientWait: 32,
  },
]

const comparisonData = [
  {
    metric: "Cost Per Visit",
    current: 112,
    target: 110,
    industry: 120,
    unit: "$",
  },
  {
    metric: "Bed Occupancy",
    current: 85,
    target: 90,
    industry: 82,
    unit: "%",
  },
  {
    metric: "Staff Utilization",
    current: 88,
    target: 90,
    industry: 85,
    unit: "%",
  },
  {
    metric: "Patient Wait Time",
    current: 32,
    target: 30,
    industry: 38,
    unit: "min",
  },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
]

const OperationalMetricsDrilldownModal: React.FC<
  OperationalMetricsDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("bar")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "radar":
        return "Operational Metrics Radar Analysis"
      case "trend":
        return "Operational Metrics Trend Analysis"
      case "comparison":
        return "Operational Metrics Benchmark Comparison"
      default:
        return "Operational Metrics Performance"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "radar":
        return "This radar chart provides a holistic view of operational performance across four key metrics, showing how current performance (blue) compares to targets (orange) and industry averages (green) on a normalized scale."
      case "trend":
        return "This chart tracks monthly trends across four key operational metrics over the past six months, showing consistent improvement with Cost Per Visit decreasing by 10.4%, Patient Wait Time by 23.8%, and both Bed Occupancy and Staff Utilization increasing by 9.0% and 7.3% respectively."
      case "comparison":
        return "This chart directly compares current performance against targets and industry benchmarks for each metric, showing that the organization outperforms industry averages in all metrics but still has room for improvement to reach internal targets."
      default:
        return "This chart compares current performance (blue bars) against target benchmarks (orange bars) and industry averages (green bars) across four key operational metrics."
    }
  }

  // Normalize data for radar chart
  const normalizeData = (data: OperationalMetricsDataPoint[]) => {
    return data.map((item) => {
      // For cost and wait time, lower is better, so invert the normalization
      if (item.name === "Cost Per Visit" || item.name === "Patient Wait Time") {
        const maxValue = Math.max(item.value, item.target, item.industry)
        return {
          ...item,
          valueNorm: 100 - (item.value / maxValue) * 100,
          targetNorm: 100 - (item.target / maxValue) * 100,
          industryNorm: 100 - (item.industry / maxValue) * 100,
        }
      }
      // For occupancy and utilization, higher is better
      const maxValue = Math.max(item.value, item.target, item.industry)
      return {
        ...item,
        valueNorm: (item.value / maxValue) * 100,
        targetNorm: (item.target / maxValue) * 100,
        industryNorm: (item.industry / maxValue) * 100,
      }
    })
  }

  // Prepare data for radar chart
  const radarData = [
    { metric: "Cost Per Visit", current: 0, target: 0, industry: 0 },
    { metric: "Bed Occupancy", current: 0, target: 0, industry: 0 },
    { metric: "Staff Utilization", current: 0, target: 0, industry: 0 },
    { metric: "Patient Wait Time", current: 0, target: 0, industry: 0 },
  ]

  // Fill in normalized values
  normalizeData(initialData).forEach((item, index) => {
    radarData[index].current = item.valueNorm
    radarData[index].target = item.targetNorm
    radarData[index].industry = item.industryNorm
  })

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "radar":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="metric" />
              <PolarRadiusAxis angle={30} domain={[0, 100]} />
              <Radar
                name="Current"
                dataKey="current"
                stroke={COLORS[0]}
                fill={COLORS[0]}
                fillOpacity={0.6}
              />
              <Radar
                name="Target"
                dataKey="target"
                stroke={COLORS[1]}
                fill={COLORS[1]}
                fillOpacity={0.6}
              />
              <Radar
                name="Industry Avg"
                dataKey="industry"
                stroke={COLORS[2]}
                fill={COLORS[2]}
                fillOpacity={0.6}
              />
              <Legend />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        )
      case "trend":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "costPerVisit")
                    return [`$${value}`, "Cost Per Visit"]
                  if (name === "bedOccupancy" || name === "staffUtilization")
                    return [`${value}%`, name]
                  if (name === "patientWait")
                    return [`${value} min`, "Patient Wait Time"]
                  return [value, name]
                }}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="costPerVisit"
                name="Cost Per Visit"
                stroke={COLORS[0]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="bedOccupancy"
                name="Bed Occupancy"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="staffUtilization"
                name="Staff Utilization"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="patientWait"
                name="Patient Wait Time"
                stroke={COLORS[3]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis type="category" dataKey="metric" width={120} />
              <Tooltip
                formatter={(value, name, props) => {
                  const unit = props.payload.unit
                  return [`${value}${unit}`, name]
                }}
              />
              <Legend />
              <Bar dataKey="current" name="Current" fill={COLORS[0]} />
              <Bar dataKey="target" name="Target" fill={COLORS[1]} />
              <Bar dataKey="industry" name="Industry Avg" fill={COLORS[2]} />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip
                formatter={(value, name, props) => {
                  const metricName = props.payload.name
                  if (metricName === "Cost Per Visit")
                    return [`$${value}`, name]
                  if (
                    metricName === "Bed Occupancy" ||
                    metricName === "Staff Utilization"
                  )
                    return [`${value}%`, name]
                  if (metricName === "Patient Wait Time")
                    return [`${value} min`, name]
                  return [value, name]
                }}
              />
              <Legend />
              <Bar dataKey="value" name="Current" fill={COLORS[0]} />
              <Bar dataKey="target" name="Target" fill={COLORS[1]} />
              <Bar dataKey="industry" name="Industry Avg" fill={COLORS[2]} />
            </BarChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">Bar Chart</SelectItem>
                <SelectItem value="radar">Radar Chart</SelectItem>
                <SelectItem value="trend">Trend Analysis</SelectItem>
                <SelectItem value="comparison">Benchmark Comparison</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default OperationalMetricsDrilldownModal
