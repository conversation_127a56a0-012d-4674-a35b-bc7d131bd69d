import React, { useState } from "react"
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface RevenueStreamDataPoint {
  name: string
  value: number
  median: number
  percentile: number
}

// Define the view types
type ViewType = "bar" | "pie" | "trend" | "comparison"

// Define the props for the RevenueStreamsDrilldownModal
interface RevenueStreamsDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: RevenueStreamDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const trendData = [
  {
    month: "Jan",
    primary: 220000,
    specialty: 300000,
    emergency: 160000,
    procedures: 380000,
    labs: 110000,
  },
  {
    month: "Feb",
    primary: 225000,
    specialty: 305000,
    emergency: 165000,
    procedures: 385000,
    labs: 115000,
  },
  {
    month: "Mar",
    primary: 230000,
    specialty: 310000,
    emergency: 170000,
    procedures: 390000,
    labs: 120000,
  },
  {
    month: "Apr",
    primary: 235000,
    specialty: 315000,
    emergency: 175000,
    procedures: 400000,
    labs: 125000,
  },
  {
    month: "May",
    primary: 240000,
    specialty: 320000,
    emergency: 180000,
    procedures: 420000,
    labs: 140000,
  },
]

const comparisonData = [
  {
    year: "2021",
    primary: 200000,
    specialty: 280000,
    emergency: 150000,
    procedures: 360000,
    labs: 100000,
  },
  {
    year: "2022",
    primary: 220000,
    specialty: 300000,
    emergency: 160000,
    procedures: 380000,
    labs: 110000,
  },
  {
    year: "2023",
    primary: 240000,
    specialty: 320000,
    emergency: 180000,
    procedures: 420000,
    labs: 140000,
  },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const RevenueStreamsDrilldownModal: React.FC<
  RevenueStreamsDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("bar")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "pie":
        return "Revenue Distribution by Service Line"
      case "trend":
        return "Revenue Streams Trend Analysis"
      case "comparison":
        return "Year-over-Year Revenue Comparison"
      default:
        return "Revenue Streams Analysis"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "pie":
        return "This chart shows the distribution of revenue across different service lines, with Procedures generating the largest portion at 32% of total revenue, followed by Specialty at 25%, Primary Care at 18%, Emergency at 14%, and Labs at 11%."
      case "trend":
        return "This chart tracks monthly revenue trends across five service lines over the past five months, showing consistent growth in all areas with Procedures maintaining the highest revenue and Labs the lowest."
      case "comparison":
        return "This chart compares revenue across service lines for the past three years, showing year-over-year growth in all areas with the most significant increases in Procedures (17% growth from 2021 to 2023)."
      default:
        return "This chart compares revenue values across five key service lines, showing actual performance against median benchmarks and 25th-75th percentile ranges."
    }
  }

  // Calculate total revenue for pie chart
  const totalRevenue = initialData.reduce((sum, item) => sum + item.value, 0)

  // Prepare data for pie chart
  const pieData = initialData.map((item) => ({
    name: item.name,
    value: item.value,
    percentage: ((item.value / totalRevenue) * 100).toFixed(1),
  }))

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "pie":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={pieData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={150}
                fill="#8884d8"
                label={({ name, percentage }) => `${name}: ${percentage}%`}
              >
                {pieData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )
      case "trend":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="primary"
                name="Primary Care"
                stroke={COLORS[0]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="specialty"
                name="Specialty"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="emergency"
                name="Emergency"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="procedures"
                name="Procedures"
                stroke={COLORS[3]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="labs"
                name="Labs"
                stroke={COLORS[4]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="primary" name="Primary Care" fill={COLORS[0]} />
              <Bar dataKey="specialty" name="Specialty" fill={COLORS[1]} />
              <Bar dataKey="emergency" name="Emergency" fill={COLORS[2]} />
              <Bar dataKey="procedures" name="Procedures" fill={COLORS[3]} />
              <Bar dataKey="labs" name="Labs" fill={COLORS[4]} />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={initialData}>
              <CartesianGrid
                vertical
                horizontal={false}
                strokeDasharray="3 3"
              />
              <XAxis dataKey="name" tickLine={false} axisLine={false} />
              <YAxis
                tickLine={false}
                axisLine={false}
                domain={[0, 500000]}
                tickFormatter={(value) => `$${value / 1000}k`}
              />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="value" name="Value" fill={COLORS[0]} />
              <Bar dataKey="median" name="Median" fill={COLORS[1]} />
              <Bar
                dataKey="percentile"
                name="25th-75th Percentile"
                fill={COLORS[2]}
              />
            </BarChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">Bar Chart</SelectItem>
                <SelectItem value="pie">Pie Chart</SelectItem>
                <SelectItem value="trend">Trend Analysis</SelectItem>
                <SelectItem value="comparison">Year Comparison</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default RevenueStreamsDrilldownModal
