import React, { useState } from "react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface RevenueLineDataPoint {
  month: string
  primary: number
  specialty: number
  emergency: number
}

// Define the view types
type ViewType = "line" | "area" | "bar" | "growth" | "comparison"

// Define the props for the RevenueLineDrilldownModal
interface RevenueLineDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: RevenueLineDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const growthData = [
  { month: "Jan", primary: 5.2, specialty: 6.8, emergency: 4.1, total: 5.5 },
  { month: "Feb", primary: 5.5, specialty: 7.2, emergency: 4.3, total: 5.8 },
  { month: "Mar", primary: 5.8, specialty: 7.5, emergency: 4.5, total: 6.1 },
  { month: "Apr", primary: 6.0, specialty: 7.8, emergency: 4.8, total: 6.3 },
  { month: "May", primary: 6.2, specialty: 8.0, emergency: 5.0, total: 6.5 },
  { month: "Jun", primary: 6.5, specialty: 8.2, emergency: 5.2, total: 6.8 },
  { month: "Jul", primary: 6.8, specialty: 8.5, emergency: 5.5, total: 7.1 },
  { month: "Aug", primary: 7.0, specialty: 8.8, emergency: 5.8, total: 7.3 },
  { month: "Sep", primary: 7.2, specialty: 9.0, emergency: 6.0, total: 7.5 },
  { month: "Oct", primary: 7.5, specialty: 9.2, emergency: 6.2, total: 7.8 },
  { month: "Nov", primary: 7.8, specialty: 9.5, emergency: 6.5, total: 8.0 },
  { month: "Dec", primary: 8.0, specialty: 9.8, emergency: 6.8, total: 8.3 },
]

const comparisonData = [
  {
    year: "2021",
    primary: 28000,
    specialty: 38000,
    emergency: 18000,
    total: 84000,
  },
  {
    year: "2022",
    primary: 32000,
    specialty: 42000,
    emergency: 22000,
    total: 96000,
  },
  {
    year: "2023",
    primary: 36000,
    specialty: 46000,
    emergency: 26000,
    total: 108000,
  },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
]

const RevenueLineDrilldownModal: React.FC<RevenueLineDrilldownModalProps> = ({
  open,
  onOpenChange,
  initialData,
  title,
  description,
}) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("line")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "area":
        return "Revenue Streams Stacked Area Chart"
      case "bar":
        return "Revenue Streams Bar Comparison"
      case "growth":
        return "Monthly Revenue Growth Rates"
      case "comparison":
        return "Year-over-Year Revenue Comparison"
      default:
        return "Revenue Trends by Service Category"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "area":
        return "This stacked area chart shows the cumulative revenue across service categories, highlighting the total revenue trend and the relative contribution of each category, with Specialty care consistently providing the largest portion."
      case "bar":
        return "This bar chart compares monthly revenue across service categories, making it easier to see the absolute differences between categories each month, with Specialty care generating the highest revenue and Emergency services the lowest."
      case "growth":
        return "This chart tracks monthly growth rates across service categories, showing that Specialty care consistently has the highest growth rate (9.8% in December), followed by Primary Care (8.0%) and Emergency services (6.8%)."
      case "comparison":
        return "This chart compares revenue across service categories for the past three years, showing consistent year-over-year growth in all areas with total revenue increasing from $84K in 2021 to $108K in 2023, a 28.6% increase."
      default:
        return "This line chart tracks monthly revenue trends across three service categories (Primary Care, Specialty, and Emergency) over a 12-month period, showing consistent growth in all three areas."
    }
  }

  // Calculate total revenue for each month
  const dataWithTotal = initialData.map((item) => ({
    ...item,
    total: item.primary + item.specialty + item.emergency,
  }))

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "area":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={dataWithTotal}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="emergency"
                name="Emergency"
                stackId="1"
                fill={COLORS[2]}
                stroke={COLORS[2]}
              />
              <Area
                type="monotone"
                dataKey="primary"
                name="Primary Care"
                stackId="1"
                fill={COLORS[0]}
                stroke={COLORS[0]}
              />
              <Area
                type="monotone"
                dataKey="specialty"
                name="Specialty"
                stackId="1"
                fill={COLORS[1]}
                stroke={COLORS[1]}
              />
            </AreaChart>
          </ResponsiveContainer>
        )
      case "bar":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={dataWithTotal}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="primary" name="Primary Care" fill={COLORS[0]} />
              <Bar dataKey="specialty" name="Specialty" fill={COLORS[1]} />
              <Bar dataKey="emergency" name="Emergency" fill={COLORS[2]} />
            </BarChart>
          </ResponsiveContainer>
        )
      case "growth":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={growthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[0, 12]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value: number) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="primary"
                name="Primary Care"
                stroke={COLORS[0]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="specialty"
                name="Specialty"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="emergency"
                name="Emergency"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="total"
                name="Total"
                stroke={COLORS[3]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Bar dataKey="primary" name="Primary Care" fill={COLORS[0]} />
              <Bar dataKey="specialty" name="Specialty" fill={COLORS[1]} />
              <Bar dataKey="emergency" name="Emergency" fill={COLORS[2]} />
              <Bar dataKey="total" name="Total" fill={COLORS[3]} />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
              <Tooltip
                formatter={(value: number) => `$${value.toLocaleString()}`}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="primary"
                name="Primary Care"
                stroke={COLORS[0]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="specialty"
                name="Specialty"
                stroke={COLORS[1]}
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="emergency"
                name="Emergency"
                stroke={COLORS[2]}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">Line Chart</SelectItem>
                <SelectItem value="area">Area Chart</SelectItem>
                <SelectItem value="bar">Bar Chart</SelectItem>
                <SelectItem value="growth">Growth Rates</SelectItem>
                <SelectItem value="comparison">Yearly Comparison</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default RevenueLineDrilldownModal
