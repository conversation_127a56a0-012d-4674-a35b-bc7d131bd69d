"use client"

import React from "react"
import { BriefcaseBusiness, LayoutGrid, TrendingUp } from "lucide-react"
import { ResponsiveContainer, Treemap } from "recharts"

import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  CardContainer,
  CardLabel,
  CardTitleWithIcon,
  Title,
} from "@/components/summary/Components"

interface TreemapChild {
  name: string
  size: number
}

interface TreemapItem {
  name: string
  size: number
  children?: TreemapChild[]
}

interface TreemapNode {
  x: number
  y: number
  width: number
  height: number
  name: string
  size?: number
  value?: number
  depth: number
  payload?: TreemapItem
}

const treemapData: TreemapItem[] = [
  {
    name: "Retail & Services",
    size: 1638.4,
    children: [
      { name: "Retail & Services", size: 675.2 },
      { name: "High Tech", size: 141.0 },
      { name: "Life Sciences", size: 107.4 },
    ],
  },
  { name: "Manufacturing", size: 363.6 },
  { name: "Healthcare", size: 300.2 },
  { name: "Public Sector", size: 232.1 },
  { name: "Financial Services", size: 204.9 },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const CustomizedContent = (props: TreemapNode) => {
  const { x, y, width, height, name, size, depth } = props
  const value = size || props.value
  const color = COLORS[depth % COLORS.length]

  return (
    <g>
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        style={{
          fill: color,
          stroke: "#fff",
          strokeWidth: 2,
        }}
      />
      {width > 60 && height > 32 && (
        <text x={x + 8} y={y + 20} fontSize={12} fill="#fff">
          {name}
        </text>
      )}
      {width > 60 && height > 32 && (
        <text x={x + 8} y={y + 38} fontSize={12} fill="#fff">
          ${value?.toLocaleString()}
        </text>
      )}
    </g>
  )
}

const OperationsHR = () => (
  <>
    <Title title="Operations / HR" />

    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card className="md:col-span-2">
        <CardTitleWithIcon
          icon={BriefcaseBusiness}
          title="Workforce Distribution"
        />

        <CardContainer>
          <ResponsiveContainer width="100%" height={175}>
            <Treemap
              isAnimationActive={false}
              data={treemapData}
              dataKey="size"
              stroke="#fff"
              content={React.createElement(CustomizedContent)}
            />
          </ResponsiveContainer>
        </CardContainer>
      </Card>

      <Card>
        <CardTitleWithIcon icon={LayoutGrid} title="Providers" />

        <CardContainer>
          <div className="-mb-1 flex items-baseline justify-between gap-1.5">
            <p className="text-2xl font-bold">500</p>
            <p className="inline-flex items-center gap-1.5 text-green-600">
              <TrendingUp className="size-3.5" />
              5.4%
            </p>
          </div>

          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between gap-2 text-sm">
              <p className="text-muted-foreground">Target: $180</p>
              <p className="font-medium text-green-600">102.8% of target</p>
            </div>

            <Progress value={100} className="[&>div]:bg-green-600" />
          </div>

          <div className="flex flex-col gap-1">
            <CardLabel label="Country Breakdown" />

            <p className="flex items-center gap-1.5">
              <span>🇦🇺</span>
              <span className="text-sm">CFC: 250</span>
              <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
                <TrendingUp className="size-3" />
                5.4%
              </span>
            </p>

            <p className="flex items-center gap-1.5">
              <span>🇸🇬</span>
              <span className="text-sm">SMG: 250</span>
              <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
                <TrendingUp className="size-3" />
                5.4%
              </span>
            </p>
          </div>
        </CardContainer>
      </Card>
    </div>
  </>
)

export default OperationsHR
