import { LucideIcon } from "lucide-react"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"

export const Title = ({ title }: { title: string }) => (
  <div className="bg-card">
    <div className="bg-primary/10 text-primary px-4 py-2">
      <h2 className="text-base font-medium">{title}</h2>
    </div>
  </div>
)

export const CardTitleWithIcon = ({
  icon: Icon,
  title,
  children,
}: {
  icon: LucideIcon | React.ElementType
  title: string
  children?: React.ReactNode
}) => (
  <CardHeader className="flex items-center gap-3">
    <div className="bg-primary/10 text-primary p-2">
      <Icon className="size-4" />
    </div>

    <CardTitle>{title}</CardTitle>

    {children}
  </CardHeader>
)

export const CardContainer = ({ children }: { children: React.ReactNode }) => (
  <CardContent className="flex flex-col gap-4">{children}</CardContent>
)

export const CardLabel = ({ label }: { label: string }) => (
  <Label className="text-base font-semibold">{label}</Label>
)
