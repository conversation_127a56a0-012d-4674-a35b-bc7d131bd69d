"use client"

import React from "react"
import {
  BarChart3,
  Building2,
  CircleDollarSign,
  Coins,
  DollarSign,
  LucideIcon,
  PiggyBank,
  Receipt,
  TrendingUp,
  Wallet,
} from "lucide-react"

import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  CardContainer,
  CardLabel,
  CardTitleWithIcon,
  Title,
} from "@/components/summary/Components"

const CARDS = [
  {
    icon: DollarSign,
    title: "Revenue",
  },
  {
    icon: PiggyBank,
    title: "Gross Profit",
  },
  {
    icon: Wallet,
    title: "Net Profit",
  },
  {
    icon: Receipt,
    title: "Operating Expenses",
  },
  {
    icon: Coins,
    title: "Cash Balance",
  },
  {
    icon: Building2,
    title: "Long Term Bank Debt",
  },
  {
    icon: CircleDollarSign,
    title: "Net Debt",
  },
  {
    icon: BarChart3,
    title: "CAPEX",
  },
]

const FinancialPNL = () => (
  <>
    <Title title="Financial P&L" />

    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {CARDS.map((card, index) => (
        <PNLCard key={index} icon={card.icon} title={card.title} />
      ))}
    </div>
  </>
)

export default FinancialPNL

const PNLCard = ({
  icon,
  title,
}: {
  icon: LucideIcon | React.ElementType
  title: string
}) => (
  <Card>
    <CardTitleWithIcon icon={icon} title={title} />

    <CardContainer>
      <div className="-mb-1 flex items-baseline justify-between gap-1.5">
        <p className="text-2xl font-bold">$100M</p>
        <p className="inline-flex items-center gap-1.5 text-green-600">
          <TrendingUp className="size-3.5" />
          5.4%
        </p>
      </div>

      <div className="flex flex-col gap-1">
        <div className="flex items-center justify-between gap-2 text-sm">
          <p className="text-muted-foreground">Target: $180</p>
          <p className="font-medium text-green-600">102.8% of target</p>
        </div>

        <Progress value={100} className="[&>div]:bg-green-600" />
      </div>

      <div className="flex flex-col gap-1">
        <CardLabel label="Country Breakdown" />

        <p className="flex items-center gap-1.5">
          <span>🇦🇺</span>
          <span className="text-sm">CFC: $50M</span>
          <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
            <TrendingUp className="size-3" />
            5.4%
          </span>
        </p>

        <p className="flex items-center gap-1.5">
          <span>🇸🇬</span>
          <span className="text-sm">SMG: $50M</span>
          <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
            <TrendingUp className="size-3" />
            5.4%
          </span>
        </p>
      </div>

      <div className="flex flex-col gap-1">
        <CardLabel label="Analysis" />

        <p className="text-sm">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </p>
      </div>
    </CardContainer>
  </Card>
)
