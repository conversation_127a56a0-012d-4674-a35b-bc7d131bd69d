"use client"

import React from "react"
import { Hospital, TrendingUp, Users } from "lucide-react"

import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  CardContainer,
  CardLabel,
  CardTitleWithIcon,
  Title,
} from "@/components/summary/Components"

const ClinicPatientInsights = () => (
  <>
    <Title title="Clinic / Patient Insights" />

    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardTitleWithIcon icon={Hospital} title="Visits" />

        <CardContainer>
          <div className="-mb-1 flex items-baseline justify-between gap-1.5">
            <p className="text-2xl font-bold">500</p>
            <p className="inline-flex items-center gap-1.5 text-green-600">
              <TrendingUp className="size-3.5" />
              5.4%
            </p>
          </div>

          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between gap-2 text-sm">
              <p className="text-muted-foreground">Target: $180</p>
              <p className="font-medium text-green-600">102.8% of target</p>
            </div>

            <Progress value={100} className="[&>div]:bg-green-600" />
          </div>

          <div className="flex flex-col gap-1">
            <CardLabel label="Country Breakdown" />

            <p className="flex items-center gap-1.5">
              <span>🇦🇺</span>
              <span className="text-sm">CFC: 250</span>
              <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
                <TrendingUp className="size-3" />
                5.4%
              </span>
            </p>

            <p className="flex items-center gap-1.5">
              <span>🇸🇬</span>
              <span className="text-sm">SMG: 250</span>
              <span className="ml-auto inline-flex items-center gap-1.5 text-sm text-green-600">
                <TrendingUp className="size-3" />
                5.4%
              </span>
            </p>
          </div>
        </CardContainer>
      </Card>

      <Card>
        <CardTitleWithIcon icon={Users} title="Patients" />

        <CardContainer>
          <div className="flex items-baseline justify-between gap-1.5">
            <p className="text-2xl font-bold">500</p>
            <p className="inline-flex items-center gap-1.5">$100/patient</p>
          </div>

          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between gap-2 text-sm">
              <p className="text-muted-foreground">Target: $180</p>
              <p className="font-medium text-green-600">102.8% of target</p>
            </div>

            <Progress value={100} className="[&>div]:bg-green-600" />
          </div>

          <div className="flex flex-col gap-1">
            <CardLabel label="Type Breakdown" />

            <p className="flex items-center gap-1.5">
              <span className="text-sm">New: 250</span>
              <span className="ml-auto text-sm">$200/patient</span>
            </p>

            <p className="flex items-center gap-1.5">
              <span className="text-sm">Existing: 250</span>
              <span className="ml-auto text-sm">$800/patient</span>
            </p>
          </div>
        </CardContainer>
      </Card>
    </div>
  </>
)

export default ClinicPatientInsights
