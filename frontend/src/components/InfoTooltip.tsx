import React from "react"
import { Info } from "lucide-react"

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const InfoTooltip = ({ children }: { children: React.ReactNode }) => (
  <Tooltip>
    <TooltipTrigger className="text-primary cursor-help">
      <Info className="size-4" />
    </TooltipTrigger>

    <TooltipContent
      className="bg-card text-card-foreground mt-2 max-w-md border px-4 py-3 text-sm text-wrap shadow-sm"
      arrowClassName="hidden!"
      side="bottom"
    >
      {children}
    </TooltipContent>
  </Tooltip>
)

export default InfoTooltip
