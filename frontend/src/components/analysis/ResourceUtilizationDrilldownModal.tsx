import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON>s,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface EquipmentDataPoint {
  equipment: string
  utilization: number
  target: number
}

// Define the view types
type ViewType = "trend" | "equipment" | "hourly" | "cost-impact"

// Define the props for the ResourceUtilizationDrilldownModal
interface ResourceUtilizationDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const equipmentData: EquipmentDataPoint[] = [
  { equipment: "MRI Scanner", utilization: 65, target: 80 },
  { equipment: "CT Scanner", utilization: 72, target: 80 },
  { equipment: "X-Ray Machine", utilization: 78, target: 80 },
  { equipment: "Ultrasound", utilization: 82, target: 85 },
  { equipment: "Operating Room 1", utilization: 75, target: 85 },
  { equipment: "Operating Room 2", utilization: 68, target: 85 },
  { equipment: "Operating Room 3", utilization: 72, target: 85 },
]

const hourlyData = [
  { hour: "8 AM", utilization: 45 },
  { hour: "9 AM", utilization: 65 },
  { hour: "10 AM", utilization: 85 },
  { hour: "11 AM", utilization: 90 },
  { hour: "12 PM", utilization: 75 },
  { hour: "1 PM", utilization: 70 },
  { hour: "2 PM", utilization: 80 },
  { hour: "3 PM", utilization: 85 },
  { hour: "4 PM", utilization: 75 },
  { hour: "5 PM", utilization: 55 },
]

const costImpactData = [
  { category: "Current Cost", value: 100 },
  { category: "Potential Savings", value: 25 },
  { category: "Optimized Cost", value: 75 },
]

const ResourceUtilizationDrilldownModal: React.FC<
  ResourceUtilizationDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "equipment":
        return "Resource Utilization by Equipment"
      case "hourly":
        return "Hourly Resource Utilization"
      case "cost-impact":
        return "Cost Impact of Utilization Optimization"
      default:
        return "Resource Utilization Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "equipment":
        return "This chart compares utilization rates across different equipment types against their target benchmarks. Ultrasound has the highest utilization at 82% (target: 85%), while MRI Scanner has the lowest at 65% (target: 80%)."
      case "hourly":
        return "This chart shows how resource utilization varies throughout the day, with peak utilization during mid-morning (11 AM: 90%) and lowest utilization in early morning and late afternoon."
      case "cost-impact":
        return "This chart illustrates the potential cost impact of optimizing resource utilization, showing a potential 25% reduction in operational costs through improved scheduling and shared resource models."
      default:
        return "This chart tracks monthly resource utilization trends, comparing current performance against target benchmarks and previous year results."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "equipment":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={equipmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="equipment" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar
                dataKey="utilization"
                name="Current Utilization"
                fill="var(--chart-1)"
              />
              <Bar dataKey="target" name="Target" fill="var(--chart-3)" />
            </BarChart>
          </ResponsiveContainer>
        )
      case "hourly":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={hourlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="utilization"
                name="Utilization"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "cost-impact":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={costImpactData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis domain={[0, 120]} tickFormatter={(v) => `$${v}K`} />
              <Tooltip formatter={(value) => `$${value}K`} />
              <Legend />
              <Bar dataKey="value" name="Cost" fill="var(--chart-1)" />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Target"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="equipment">Equipment</SelectItem>
                <SelectItem value="hourly">Hourly Distribution</SelectItem>
                <SelectItem value="cost-impact">Cost Impact</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default ResourceUtilizationDrilldownModal
