import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface DepartmentDataPoint {
  department: string
  retentionRate: number
  target: number
}

// Define the view types
type ViewType = "trend" | "department" | "reasons" | "demographics"

// Define the props for the PatientRetentionDrilldownModal
interface PatientRetentionDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const departmentData: DepartmentDataPoint[] = [
  {
    department: "OB/GYN",
    retentionRate: 86,
    target: 85,
  },
  {
    department: "Pediatrics",
    retentionRate: 82,
    target: 85,
  },
  {
    department: "Cardiology",
    retentionRate: 78,
    target: 85,
  },
  {
    department: "Orthopedics",
    retentionRate: 75,
    target: 85,
  },
  {
    department: "Internal Medicine",
    retentionRate: 72,
    target: 85,
  },
  {
    department: "Neurology",
    retentionRate: 68,
    target: 85,
  },
]

const reasonsData = [
  { reason: "Wait Times", percentage: 35 },
  { reason: "Communication", percentage: 28 },
  { reason: "Cost", percentage: 18 },
  { reason: "Location", percentage: 12 },
  { reason: "Other", percentage: 7 },
]

const demographicsData = [
  { ageGroup: "18-24", retentionRate: 65 },
  { ageGroup: "25-34", retentionRate: 72 },
  { ageGroup: "35-44", retentionRate: 78 },
  { ageGroup: "45-54", retentionRate: 82 },
  { ageGroup: "55-64", retentionRate: 85 },
  { ageGroup: "65+", retentionRate: 88 },
]

const PatientRetentionDrilldownModal: React.FC<
  PatientRetentionDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "department":
        return "Patient Retention by Department"
      case "reasons":
        return "Reasons for Patient Attrition"
      case "demographics":
        return "Retention Rates by Age Group"
      default:
        return "Patient Retention Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "department":
        return "This chart compares patient retention rates across different departments against the target benchmark of 85%. OB/GYN leads with 86% retention while Neurology struggles at 68%."
      case "reasons":
        return "This chart shows the primary reasons patients cite for not returning, with wait times (35%) and communication issues (28%) being the most significant factors."
      case "demographics":
        return "This chart breaks down retention rates by patient age groups, showing higher retention among older patients and opportunities to improve retention in younger demographics."
      default:
        return "This chart tracks monthly patient retention rates, comparing current performance against target benchmarks and previous year results."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "department":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={departmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="department" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar
                dataKey="retentionRate"
                name="Retention Rate"
                fill="var(--chart-1)"
              />
              <Bar dataKey="target" name="Target" fill="var(--chart-3)" />
            </BarChart>
          </ResponsiveContainer>
        )
      case "reasons":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={reasonsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="reason" />
              <YAxis domain={[0, 40]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar
                dataKey="percentage"
                name="Percentage"
                fill="var(--chart-1)"
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "demographics":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={demographicsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="ageGroup" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar
                dataKey="retentionRate"
                name="Retention Rate"
                fill="var(--chart-1)"
              />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Target"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="reasons">Attrition Reasons</SelectItem>
                <SelectItem value="demographics">Demographics</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default PatientRetentionDrilldownModal
