import React, { useState } from "react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface DepartmentDataPoint {
  department: string
  newPatients: number
  returningPatients: number
  total: number
}

// Define the view types
type ViewType = "trend" | "department" | "new-vs-returning" | "daily"

// Define the props for the PatientVolumeDrilldownModal
interface PatientVolumeDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const departmentData: DepartmentDataPoint[] = [
  {
    department: "Women's Health",
    newPatients: 580,
    returningPatients: 1520,
    total: 2100,
  },
  {
    department: "Pediatrics",
    newPatients: 520,
    returningPatients: 1380,
    total: 1900,
  },
  {
    department: "Cardiology",
    newPatients: 420,
    returningPatients: 1280,
    total: 1700,
  },
  {
    department: "Orthopedics",
    newPatients: 380,
    returningPatients: 1220,
    total: 1600,
  },
  {
    department: "Neurology",
    newPatients: 320,
    returningPatients: 1080,
    total: 1400,
  },
]

const newVsReturningData = [
  { month: "Jul", newPatients: 420, returningPatients: 1250 },
  { month: "Aug", newPatients: 450, returningPatients: 1280 },
  { month: "Sep", newPatients: 480, returningPatients: 1320 },
  { month: "Oct", newPatients: 510, returningPatients: 1380 },
  { month: "Nov", newPatients: 540, returningPatients: 1450 },
  { month: "Dec", newPatients: 580, returningPatients: 1520 },
]

const dailyData = [
  { day: "Mon", volume: 320 },
  { day: "Tue", volume: 350 },
  { day: "Wed", volume: 380 },
  { day: "Thu", volume: 360 },
  { day: "Fri", volume: 340 },
  { day: "Sat", volume: 220 },
  { day: "Sun", volume: 130 },
]

const PatientVolumeDrilldownModal: React.FC<
  PatientVolumeDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "department":
        return "Patient Volume by Department"
      case "new-vs-returning":
        return "New vs. Returning Patients"
      case "daily":
        return "Daily Patient Volume Distribution"
      default:
        return "Patient Volume Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "department":
        return "This chart breaks down patient volume by department, showing both new and returning patients for each area of the hospital."
      case "new-vs-returning":
        return "This chart compares the number of new patients versus returning patients over time, helping identify patient acquisition and retention trends."
      case "daily":
        return "This chart shows the distribution of patient volume across days of the week, helping identify peak times and optimize staffing."
      default:
        return "This chart tracks monthly patient volume trends, comparing current year performance against budget targets and previous year results."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "department":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={departmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="department" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="newPatients"
                name="New Patients"
                fill="var(--chart-1)"
              />
              <Bar
                dataKey="returningPatients"
                name="Returning Patients"
                fill="var(--chart-3)"
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "new-vs-returning":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={newVsReturningData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area
                type="monotone"
                dataKey="newPatients"
                name="New Patients"
                stackId="1"
                fill="var(--chart-1)"
                stroke="var(--chart-1)"
              />
              <Area
                type="monotone"
                dataKey="returningPatients"
                name="Returning Patients"
                stackId="1"
                fill="var(--chart-3)"
                stroke="var(--chart-3)"
              />
            </AreaChart>
          </ResponsiveContainer>
        )
      case "daily":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={dailyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="volume"
                name="Patient Volume"
                fill="var(--chart-1)"
              />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Budget"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="new-vs-returning">
                  New vs. Returning
                </SelectItem>
                <SelectItem value="daily">Daily Distribution</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default PatientVolumeDrilldownModal
