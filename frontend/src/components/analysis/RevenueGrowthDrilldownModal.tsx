import React, { useState } from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface DepartmentDataPoint {
  department: string
  current: number
  budget: number
  previous: number
}

// Define the view types
type ViewType = "monthly" | "quarterly" | "department" | "service"

// Define the props for the RevenueGrowthDrilldownModal
interface RevenueGrowthDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const departmentData: DepartmentDataPoint[] = [
  {
    department: "Women's Health",
    current: 12.0,
    budget: 10.0,
    previous: 9.0,
  },
  {
    department: "Pediatrics",
    current: 10.5,
    budget: 9.5,
    previous: 8.5,
  },
  {
    department: "Cardiology",
    current: 8.2,
    budget: 8.0,
    previous: 7.5,
  },
  {
    department: "Orthopedics",
    current: 7.8,
    budget: 7.5,
    previous: 7.0,
  },
  {
    department: "Neurology",
    current: 6.5,
    budget: 6.0,
    previous: 5.5,
  },
]

const quarterlyData = [
  {
    month: "Q1",
    current: 9.5,
    budget: 8.5,
    previous: 7.5,
  },
  {
    month: "Q2",
    current: 10.2,
    budget: 9.0,
    previous: 8.0,
  },
  {
    month: "Q3",
    current: 11.0,
    budget: 9.5,
    previous: 8.5,
  },
  {
    month: "Q4",
    current: 12.5,
    budget: 10.0,
    previous: 9.0,
  },
]

const serviceData = [
  {
    department: "Consultations",
    current: 8.5,
    budget: 8.0,
    previous: 7.0,
  },
  {
    department: "Procedures",
    current: 12.0,
    budget: 10.0,
    previous: 9.0,
  },
  {
    department: "Diagnostics",
    current: 9.5,
    budget: 9.0,
    previous: 8.0,
  },
  {
    department: "Treatments",
    current: 11.0,
    budget: 10.0,
    previous: 9.0,
  },
]

const RevenueGrowthDrilldownModal: React.FC<
  RevenueGrowthDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("monthly")

  // Get the appropriate data based on the view type
  const getDataForView = () => {
    switch (viewType) {
      case "quarterly":
        return quarterlyData
      case "department":
        return departmentData
      case "service":
        return serviceData
      default:
        return initialData
    }
  }

  // Get the appropriate x-axis key based on the view type
  const getXAxisKey = () => {
    switch (viewType) {
      case "department":
      case "service":
        return "department"
      default:
        return "month"
    }
  }

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "quarterly":
        return "Quarterly Revenue Growth"
      case "department":
        return "Revenue Growth by Department"
      case "service":
        return "Revenue Growth by Service Type"
      default:
        return "Monthly Revenue Growth"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "quarterly":
        return "This chart displays quarterly revenue growth percentages comparing current year performance (blue bars) against budget targets (orange line) and previous year results (green line)."
      case "department":
        return "This chart compares revenue growth percentages across different hospital departments, highlighting which areas are experiencing the strongest financial performance."
      case "service":
        return "This chart breaks down revenue growth by service type, showing which services are driving the most growth in the organization."
      default:
        return "This chart displays monthly revenue growth percentages comparing current year performance (blue bars) against budget targets (orange line) and previous year results (green line)."
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="service">Service Type</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            <ResponsiveContainer width="100%" height={400}>
              <ComposedChart data={getDataForView()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={getXAxisKey()} tick={{ fontSize: 12 }} />
                <YAxis domain={[0, 15]} tickFormatter={(v) => `${v}%`} />
                <Tooltip formatter={(value: number) => `${value}%`} />
                <Legend verticalAlign="bottom" />
                <Bar
                  dataKey="current"
                  name="Current Year"
                  fill="var(--chart-1)"
                  barSize={32}
                />
                <Line
                  type="monotone"
                  dataKey="budget"
                  name="Budget"
                  stroke="var(--chart-3)"
                  strokeWidth={3}
                  dot={{ stroke: "var(--chart-3)" }}
                />
                <Line
                  type="monotone"
                  dataKey="previous"
                  name="Previous Year"
                  stroke="var(--chart-5)"
                  strokeWidth={3}
                  dot={{ stroke: "var(--chart-5)" }}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default RevenueGrowthDrilldownModal
