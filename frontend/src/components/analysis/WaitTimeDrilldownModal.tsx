import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON>s,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface DepartmentDataPoint {
  department: string
  waitTime: number
  target: number
}

// Define the view types
type ViewType = "trend" | "department" | "hourly" | "comparison"

// Define the props for the WaitTimeDrilldownModal
interface WaitTimeDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const departmentData: DepartmentDataPoint[] = [
  { department: "Emergency", waitTime: 47, target: 30 },
  { department: "Outpatient", waitTime: 32, target: 20 },
  { department: "Radiology", waitTime: 25, target: 20 },
  { department: "Laboratory", waitTime: 18, target: 15 },
  { department: "Pharmacy", waitTime: 22, target: 15 },
]

const hourlyData = [
  { hour: "8 AM", waitTime: 25 },
  { hour: "9 AM", waitTime: 35 },
  { hour: "10 AM", waitTime: 42 },
  { hour: "11 AM", waitTime: 48 },
  { hour: "12 PM", waitTime: 52 },
  { hour: "1 PM", waitTime: 45 },
  { hour: "2 PM", waitTime: 38 },
  { hour: "3 PM", waitTime: 32 },
  { hour: "4 PM", waitTime: 28 },
  { hour: "5 PM", waitTime: 22 },
]

const comparisonData = [
  { category: "Before Digital Queue", waitTime: 52 },
  { category: "After Digital Queue", waitTime: 32 },
  { category: "Before Staggered Scheduling", waitTime: 48 },
  { category: "After Staggered Scheduling", waitTime: 28 },
]

const WaitTimeDrilldownModal: React.FC<WaitTimeDrilldownModalProps> = ({
  open,
  onOpenChange,
  initialData,
  title,
  description,
}) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "department":
        return "Wait Time by Department"
      case "hourly":
        return "Hourly Wait Time Distribution"
      case "comparison":
        return "Wait Time Improvement Comparison"
      default:
        return "Wait Time Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "department":
        return "This chart compares wait times across different departments against their target benchmarks. Emergency has the longest wait time at 47 minutes (target: 30), while Laboratory has the shortest at 18 minutes (target: 15)."
      case "hourly":
        return "This chart shows how wait times vary throughout the day, with peak wait times occurring during lunch hours (12 PM: 52 minutes) and shortest wait times in early morning and late afternoon."
      case "comparison":
        return "This chart compares wait times before and after implementing key operational changes, showing a 38% reduction with digital queue management and a 42% improvement with staggered scheduling."
      default:
        return "This chart tracks monthly wait time trends, comparing current performance against target benchmarks and previous year results."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "department":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={departmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="department" />
              <YAxis
                domain={[0, 60]}
                label={{ value: "Minutes", angle: -90, position: "insideLeft" }}
              />
              <Tooltip formatter={(value) => `${value} min`} />
              <Legend />
              <Bar
                dataKey="waitTime"
                name="Current Wait Time"
                fill="var(--chart-1)"
              />
              <Bar dataKey="target" name="Target" fill="var(--chart-3)" />
            </BarChart>
          </ResponsiveContainer>
        )
      case "hourly":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={hourlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis
                domain={[0, 60]}
                label={{ value: "Minutes", angle: -90, position: "insideLeft" }}
              />
              <Tooltip formatter={(value) => `${value} min`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="waitTime"
                name="Wait Time"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                type="number"
                domain={[0, 60]}
                label={{ value: "Minutes", position: "insideBottom" }}
              />
              <YAxis type="category" dataKey="category" width={180} />
              <Tooltip formatter={(value) => `${value} min`} />
              <Legend />
              <Bar dataKey="waitTime" name="Wait Time" fill="var(--chart-1)" />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis
                domain={[0, 60]}
                label={{ value: "Minutes", angle: -90, position: "insideLeft" }}
              />
              <Tooltip formatter={(value) => `${value} min`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Target"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="hourly">Hourly Distribution</SelectItem>
                <SelectItem value="comparison">
                  Before/After Comparison
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default WaitTimeDrilldownModal
