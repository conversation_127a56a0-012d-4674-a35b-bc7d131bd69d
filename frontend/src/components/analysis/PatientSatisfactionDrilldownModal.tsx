import React, { useState } from "react"
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  <PERSON>,
  <PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface CategoryDataPoint {
  category: string
  score: number
  target: number
}

// Define the view types
type ViewType = "trend" | "category" | "demographic" | "feedback"

// Define the props for the PatientSatisfactionDrilldownModal
interface PatientSatisfactionDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const categoryData: CategoryDataPoint[] = [
  { category: "Overall Satisfaction", score: 3.8, target: 4.2 },
  { category: "Staff Communication", score: 3.5, target: 4.0 },
  { category: "Wait Times", score: 3.2, target: 4.0 },
  { category: "Facility Cleanliness", score: 4.3, target: 4.2 },
  { category: "Treatment Explanation", score: 3.7, target: 4.0 },
  { category: "Ease of Scheduling", score: 3.6, target: 4.0 },
]

const demographicData = [
  { ageGroup: "18-24", score: 3.4 },
  { ageGroup: "25-34", score: 3.6 },
  { ageGroup: "35-44", score: 3.8 },
  { ageGroup: "45-54", score: 4.0 },
  { ageGroup: "55-64", score: 4.2 },
  { ageGroup: "65+", score: 4.4 },
]

const feedbackData = [
  { category: "Positive", value: 65 },
  { category: "Neutral", value: 20 },
  { category: "Negative", value: 15 },
]

const COLORS = ["var(--chart-1)", "var(--chart-3)", "var(--chart-5)"]

const PatientSatisfactionDrilldownModal: React.FC<
  PatientSatisfactionDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "category":
        return "Satisfaction Scores by Category"
      case "demographic":
        return "Satisfaction by Age Group"
      case "feedback":
        return "Feedback Distribution"
      default:
        return "Satisfaction Score Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "category":
        return "This chart breaks down satisfaction scores across different categories. Facility Cleanliness scores highest at 4.3/5.0, while Wait Times scores lowest at 3.2/5.0, highlighting areas for improvement."
      case "demographic":
        return "This chart shows how satisfaction varies by patient age group, with older patients (65+: 4.4/5.0) reporting higher satisfaction than younger patients (18-24: 3.4/5.0)."
      case "feedback":
        return "This chart shows the distribution of patient feedback, with 65% positive, 20% neutral, and 15% negative comments, providing a high-level overview of patient sentiment."
      default:
        return "This chart tracks monthly satisfaction score trends, comparing current performance against target benchmarks and previous year results."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "category":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={categoryData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis domain={[0, 5]} tickFormatter={(v) => `${v}/5.0`} />
              <Tooltip formatter={(value) => `${value}/5.0`} />
              <Legend />
              <Bar dataKey="score" name="Current Score" fill="var(--chart-1)" />
              <Bar dataKey="target" name="Target" fill="var(--chart-3)" />
            </BarChart>
          </ResponsiveContainer>
        )
      case "demographic":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={demographicData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="ageGroup" />
              <YAxis domain={[0, 5]} tickFormatter={(v) => `${v}/5.0`} />
              <Tooltip formatter={(value) => `${value}/5.0`} />
              <Legend />
              <Bar
                dataKey="score"
                name="Satisfaction Score"
                fill="var(--chart-1)"
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "feedback":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={feedbackData}
                dataKey="value"
                nameKey="category"
                cx="50%"
                cy="50%"
                outerRadius={150}
                fill="#8884d8"
                label={({ name, percent }) =>
                  `${name} (${(percent * 100).toFixed(0)}%)`
                }
              >
                {feedbackData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[3, 5]} tickFormatter={(v) => `${v}/5.0`} />
              <Tooltip formatter={(value) => `${value}/5.0`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Target"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="category">Category</SelectItem>
                <SelectItem value="demographic">Age Group</SelectItem>
                <SelectItem value="feedback">Feedback Distribution</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default PatientSatisfactionDrilldownModal
