import React, { useState } from "react"
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { <PERSON>, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface DepartmentDataPoint {
  department: string
  accuracy: number
}

interface CaseTypeDataPoint {
  caseType: string
  accuracy: number
  volume: number
}

// Define the view types
type ViewType = "trend" | "department" | "case-type" | "comparison"

// Define the props for the AIDiagnosticDrilldownModal
interface AIDiagnosticDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const departmentData: DepartmentDataPoint[] = [
  { department: "Radiology", accuracy: 95 },
  { department: "Pathology", accuracy: 92 },
  { department: "Dermatology", accuracy: 88 },
  { department: "Cardiology", accuracy: 85 },
  { department: "Neurology", accuracy: 82 },
  { department: "Oncology", accuracy: 90 },
]

const caseTypeData: CaseTypeDataPoint[] = [
  { caseType: "X-Ray Analysis", accuracy: 96, volume: 1200 },
  { caseType: "MRI Interpretation", accuracy: 94, volume: 850 },
  { caseType: "CT Scan Analysis", accuracy: 93, volume: 920 },
  { caseType: "Tissue Sample", accuracy: 91, volume: 780 },
  { caseType: "Skin Lesion", accuracy: 89, volume: 650 },
  { caseType: "ECG Reading", accuracy: 86, volume: 1100 },
]

const comparisonData = [
  { category: "Speed", ai: 95, human: 75 },
  { category: "Accuracy", ai: 92, human: 94 },
  { category: "Cost Efficiency", ai: 98, human: 65 },
  { category: "Complex Cases", ai: 82, human: 96 },
  { category: "Patient Satisfaction", ai: 88, human: 92 },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
  "var(--chart-6)",
]

const AIDiagnosticDrilldownModal: React.FC<AIDiagnosticDrilldownModalProps> = ({
  open,
  onOpenChange,
  initialData,
  title,
  description,
}) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "department":
        return "AI Diagnostic Accuracy by Department"
      case "case-type":
        return "AI Accuracy by Case Type"
      case "comparison":
        return "AI vs. Human Diagnostician Comparison"
      default:
        return "AI Diagnostic Accuracy Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "department":
        return "This chart displays AI diagnostic accuracy percentages across medical specialties. Radiology leads with 95% accuracy, followed by Pathology (92%) and Oncology (90%)."
      case "case-type":
        return "This chart breaks down AI accuracy by specific case types, with X-Ray Analysis (96%) and MRI Interpretation (94%) showing the highest accuracy rates."
      case "comparison":
        return "This chart compares AI and human diagnosticians across five key metrics, showing AI advantages in speed and cost efficiency, while humans excel in complex cases."
      default:
        return "This chart tracks monthly AI diagnostic accuracy trends, showing improvement from 85% to 95% over six months, exceeding the target benchmark of 90%."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "department":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={departmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="department" />
              <YAxis domain={[75, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar dataKey="accuracy" name="Accuracy" fill="var(--chart-1)">
                {departmentData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )
      case "case-type":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={caseTypeData}
                dataKey="volume"
                nameKey="caseType"
                cx="50%"
                cy="50%"
                outerRadius={120}
                fill="#8884d8"
                label={({ caseType, accuracy }) => `${caseType}: ${accuracy}%`}
              >
                {caseTypeData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                formatter={(value, name) => {
                  if (name === "volume") return [`${value} cases`, "Volume"]
                  return [value, name]
                }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )
      case "comparison":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={comparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar dataKey="ai" name="AI" fill="var(--chart-1)" />
              <Bar dataKey="human" name="Human" fill="var(--chart-3)" />
            </BarChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[75, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Target"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="case-type">Case Type</SelectItem>
                <SelectItem value="comparison">AI vs. Human</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default AIDiagnosticDrilldownModal
