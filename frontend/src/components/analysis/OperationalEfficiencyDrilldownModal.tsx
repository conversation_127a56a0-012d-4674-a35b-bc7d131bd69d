import React, { useState } from "react"
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define data point types for different views
interface BaseDataPoint {
  month: string
  current: number
  budget: number
  previous: number
}

interface MetricDataPoint {
  metric: string
  current: number
  target: number
  improvement: number
}

// Define the view types
type ViewType = "trend" | "metrics" | "department" | "hourly"

// Define the props for the OperationalEfficiencyDrilldownModal
interface OperationalEfficiencyDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: BaseDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const metricsData: MetricDataPoint[] = [
  {
    metric: "Wait Time (min)",
    current: 42,
    target: 27,
    improvement: 35,
  },
  {
    metric: "Staff Utilization (%)",
    current: 72,
    target: 85,
    improvement: 18,
  },
  {
    metric: "Patient Throughput (patients/hr)",
    current: 8.5,
    target: 10.2,
    improvement: 20,
  },
  {
    metric: "Processing Time (min)",
    current: 18,
    target: 12,
    improvement: 33,
  },
]

const departmentData = [
  { department: "Emergency", efficiency: 68, target: 80 },
  { department: "Outpatient", efficiency: 75, target: 80 },
  { department: "Surgery", efficiency: 82, target: 85 },
  { department: "Radiology", efficiency: 78, target: 85 },
  { department: "Laboratory", efficiency: 85, target: 85 },
]

const hourlyData = [
  { hour: "8 AM", efficiency: 65 },
  { hour: "9 AM", efficiency: 72 },
  { hour: "10 AM", efficiency: 85 },
  { hour: "11 AM", efficiency: 88 },
  { hour: "12 PM", efficiency: 75 },
  { hour: "1 PM", efficiency: 70 },
  { hour: "2 PM", efficiency: 78 },
  { hour: "3 PM", efficiency: 82 },
  { hour: "4 PM", efficiency: 80 },
  { hour: "5 PM", efficiency: 68 },
]

const OperationalEfficiencyDrilldownModal: React.FC<
  OperationalEfficiencyDrilldownModalProps
> = ({ open, onOpenChange, initialData, title, description }) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("trend")

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "metrics":
        return "Efficiency Metrics and Improvement Potential"
      case "department":
        return "Efficiency by Department"
      case "hourly":
        return "Hourly Efficiency Distribution"
      default:
        return "Operational Efficiency Trend"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "metrics":
        return "This chart compares current operational metrics against potential improvement targets across four key areas, showing significant improvement potential in wait times (35%) and processing time (33%)."
      case "department":
        return "This chart breaks down operational efficiency by department, highlighting areas that are meeting targets (Laboratory at 85%) and those requiring improvement (Emergency at 68%)."
      case "hourly":
        return "This chart shows efficiency variations throughout the day, with peak efficiency during mid-morning (10-11 AM) and lower efficiency during early morning and late afternoon hours."
      default:
        return "This chart tracks monthly operational efficiency trends, comparing current performance against targets and previous year results."
    }
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    switch (viewType) {
      case "metrics":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={metricsData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis type="category" dataKey="metric" width={150} />
              <Tooltip
                formatter={(value, name) => {
                  if (name === "improvement")
                    return [`${value}%`, "Potential Improvement"]
                  return [value, name]
                }}
              />
              <Legend />
              <Bar dataKey="current" name="Current" fill="var(--chart-1)" />
              <Bar dataKey="target" name="Target" fill="var(--chart-3)" />
              <Bar
                dataKey="improvement"
                name="Improvement %"
                fill="var(--chart-5)"
              />
            </BarChart>
          </ResponsiveContainer>
        )
      case "department":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={departmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="department" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Bar
                dataKey="efficiency"
                name="Current Efficiency"
                fill="var(--chart-1)"
              />
              <Bar dataKey="target" name="Target" fill="var(--chart-3)" />
            </BarChart>
          </ResponsiveContainer>
        )
      case "hourly":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={hourlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis domain={[50, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="efficiency"
                name="Efficiency"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
      default:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={initialData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[0, 100]} tickFormatter={(v) => `${v}%`} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Legend />
              <Line
                type="monotone"
                dataKey="current"
                name="Current Year"
                stroke="var(--chart-1)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-1)" }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                name="Target"
                stroke="var(--chart-3)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-3)" }}
              />
              <Line
                type="monotone"
                dataKey="previous"
                name="Previous Year"
                stroke="var(--chart-5)"
                strokeWidth={3}
                dot={{ stroke: "var(--chart-5)" }}
              />
            </LineChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trend">Monthly Trend</SelectItem>
                <SelectItem value="metrics">Key Metrics</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="hourly">Hourly Distribution</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default OperationalEfficiencyDrilldownModal
