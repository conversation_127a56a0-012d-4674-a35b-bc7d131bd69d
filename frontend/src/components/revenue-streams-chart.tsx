import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cartes<PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

const data = [
  {
    name: "Primary Care",
    value: 240000,
    median: 220000,
    percentile: 260000,
  },
  {
    name: "<PERSON><PERSON>",
    value: 320000,
    median: 300000,
    percentile: 340000,
  },
  {
    name: "Emergency",
    value: 180000,
    median: 160000,
    percentile: 200000,
  },
  {
    name: "Procedures",
    value: 420000,
    median: 400000,
    percentile: 440000,
  },
  {
    name: "Labs",
    value: 140000,
    median: 120000,
    percentile: 160000,
  },
]

export function RevenueStreamsChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <CartesianGrid vertical horizontal={false} strokeDasharray="3 3" />
          <XAxis dataKey="name" tickLine={false} axisLine={false} />
          <YAxis
            tickLine={false}
            axisLine={false}
            domain={[0, 500000]}
            tickFormatter={(value) => `$${value / 1000}k`}
          />
          <Tooltip
            formatter={(value: number) => `$${value.toLocaleString()}`}
          />
          <Legend />
          <Bar dataKey="value" name="Value" fill="var(--chart-1)" />
          <Bar dataKey="median" name="Median" fill="var(--chart-2)" />
          <Bar
            dataKey="percentile"
            name="25th-75th Percentile"
            fill="var(--chart-3)"
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
