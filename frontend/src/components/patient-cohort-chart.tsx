import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

const financialData = [
  { month: "Jan", revenue: 120000, expenses: 80000 },
  { month: "Feb", revenue: 130000, expenses: 85000 },
  { month: "Mar", revenue: 140000, expenses: 90000 },
  { month: "Apr", revenue: 125000, expenses: 87000 },
  { month: "May", revenue: 150000, expenses: 92000 },
  { month: "Jun", revenue: 160000, expenses: 95000 },
  { month: "Jul", revenue: 170000, expenses: 97000 },
  { month: "Aug", revenue: 165000, expenses: 94000 },
  { month: "Sep", revenue: 155000, expenses: 91000 },
  { month: "Oct", revenue: 180000, expenses: 99000 },
  { month: "Nov", revenue: 175000, expenses: 96000 },
  { month: "Dec", revenue: 185000, expenses: 102000 },
].map((item) => ({
  ...item,
  profit: item.revenue - item.expenses,
}))

export function PatientCohortChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={financialData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
          <Tooltip
            formatter={(value: number) => `$${value.toLocaleString()}`}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="revenue"
            stroke="var(--chart-1)"
            name="Revenue"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="expenses"
            stroke="var(--chart-3)"
            name="Expenses"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="profit"
            stroke="var(--chart-5)"
            name="Net Profit"
            strokeDasharray="3 3"
            strokeWidth={2}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
