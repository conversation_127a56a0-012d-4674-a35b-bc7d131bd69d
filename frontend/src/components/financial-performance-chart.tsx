import {
  Bar,
  CartesianGrid,
  Composed<PERSON>hart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

const data = [
  {
    unit: "Cardiology",
    revenue: 300000,
    expenses: 200000,
    profit: 100000,
    target: 120000,
    profitMargin: 33.33, // Profit / Revenue
    roi: 50, // (Profit / Expenses) * 100
  },
  {
    unit: "Orthopedics",
    revenue: 350000,
    expenses: 220000,
    profit: 130000,
    target: 140000,
    profitMargin: 37.14,
    roi: 59.09,
  },
  {
    unit: "Neurology",
    revenue: 400000,
    expenses: 250000,
    profit: 150000,
    target: 160000,
    profitMargin: 37.5,
    roi: 60,
  },
  {
    unit: "Pediatrics",
    revenue: 450000,
    expenses: 280000,
    profit: 170000,
    target: 180000,
    profitMargin: 37.78,
    roi: 60.71,
  },
  {
    unit: "Oncology",
    revenue: 500000,
    expenses: 300000,
    profit: 200000,
    target: 190000,
    profitMargin: 40,
    roi: 66.67,
  },
  {
    unit: "Gastroenterology",
    revenue: 550000,
    expenses: 320000,
    profit: 230000,
    target: 210000,
    profitMargin: 41.82,
    roi: 71.88,
  },
]

export function FinancialPerformanceChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="unit" />

          {/* Left Y-Axis for Revenue and Expenses */}
          <YAxis
            yAxisId="left"
            tickFormatter={(value) => `$${value / 1000}k`}
          />

          {/* Right Y-Axis for Profit Margin and ROI */}
          <YAxis
            yAxisId="right"
            orientation="right"
            tickFormatter={(value) => `${value}%`}
            domain={[0, 100]}
          />

          <Tooltip
            formatter={(value, name) => {
              if (
                name === "Revenue" ||
                name === "Expenses" ||
                name === "Target"
              )
                return `$${value}`

              return `${value}%`
            }}
          />
          <Legend />

          {/* Revenue and Expenses Bars */}
          <Bar
            dataKey="revenue"
            stackId="a"
            fill="var(--chart-1)"
            name="Revenue"
            yAxisId="left"
          />
          <Bar
            dataKey="expenses"
            stackId="a"
            fill="var(--chart-2)"
            name="Expenses"
            yAxisId="left"
          />

          {/* Target Line (Using Left Y-Axis) */}
          <Line
            type="monotone"
            dataKey="target"
            stroke="var(--chart-5)"
            name="Target"
            strokeWidth={2}
            yAxisId="left"
          />

          {/* Profit Margin and ROI Lines (Using Right Y-Axis) */}
          <Line
            type="monotone"
            dataKey="profitMargin"
            stroke="var(--chart-4)"
            name="Profit Margin"
            strokeWidth={2}
            yAxisId="right"
          />
          <Line
            type="monotone"
            dataKey="roi"
            stroke="var(--chart-4)"
            name="ROI"
            strokeWidth={2}
            yAxisId="right"
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  )
}
