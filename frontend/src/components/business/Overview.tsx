"use client"

import React from "react"
import {
  CircleDollarSign,
  DollarSign,
  LucideIcon,
  Receipt,
  TrendingUp,
  Wallet,
} from "lucide-react"

import { Card } from "@/components/ui/card"
import {
  <PERSON><PERSON>ontainer,
  CardTitleWithIcon,
} from "@/components/summary/Components"

const CARDS = [
  {
    icon: DollarSign,
    title: "Revenue",
  },
  {
    icon: Receipt,
    title: "Operating Expenses",
  },
  {
    icon: CircleDollarSign,
    title: "EBITDA",
  },
  {
    icon: Wallet,
    title: "Net Profit",
  },
]

const Overview = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {CARDS.map((card, index) => (
      <OverviewCard key={index} icon={card.icon} title={card.title} />
    ))}
  </div>
)

export default Overview

const OverviewCard = ({
  icon,
  title,
}: {
  icon: LucideIcon | React.ElementType
  title: string
}) => (
  <Card>
    <CardTitleWithIcon icon={icon} title={title} />

    <CardContainer>
      <div className="-mb-1 flex items-baseline justify-between gap-1.5">
        <p className="text-2xl font-bold">$100M</p>
        <p className="inline-flex items-center gap-1.5 text-green-600">
          <TrendingUp className="size-3.5" />
          5.4%
        </p>
      </div>

      <p className="text-sm">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </p>
    </CardContainer>
  </Card>
)
