import {
  <PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

const data = [
  {
    metric: "Revenue",
    actual: 9800,
    forecast: 9550,
    target: 10000,
  },
  {
    metric: "Gross Profit",
    actual: 6800,
    forecast: 6650,
    target: 7000,
  },
  {
    metric: "EBITDA",
    actual: 2040,
    forecast: 2285,
    target: 3000,
  },
  {
    metric: "Net Income",
    actual: 2156,
    forecast: 2090,
    target: 2200,
  },
]

export function PLPerformanceChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="metric" />
          <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
          <Tooltip
            formatter={(value) => `$${(value as number).toLocaleString()}`}
          />
          <Legend />
          {/* Actual Bar */}
          <Bar
            dataKey="actual"
            fill="var(--chart-1)"
            name="Actual"
            stackId="a"
          />
          {/* Forecast Bar */}
          <Bar
            dataKey="forecast"
            fill="var(--chart-3)"
            name="Forecast"
            stackId="a"
          />
          {/* Target Bar */}
          <Bar
            dataKey="target"
            fill="var(--chart-5)"
            name="Target"
            stackId="a"
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  )
}
