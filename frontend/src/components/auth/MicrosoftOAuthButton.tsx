"use client"

import React, { useState } from "react"
import { toast } from "sonner"

import { useRouter } from "@/hooks/use-router"
import { LoaderButton } from "@/components/ui/loader-button"
import Microsoft from "@/icons/Microsoft"

const MicrosoftOAuthButton = () => {
  const { push } = useRouter()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleClick = async () => {
    try {
      setIsLoading(true)
      await new Promise((resolve) => setTimeout(resolve, 1000))
      push("/")
    } catch (error) {
      toast.error(String(error))
      setIsLoading(false)
    }
  }

  return (
    <LoaderButton
      size="lg"
      className="w-full"
      onClick={handleClick}
      isLoading={isLoading}
      icon={Microsoft}
    >
      Continue with Microsoft 365
    </LoaderButton>
  )
}

export default MicrosoftOAuthButton
