import React, { ElementType } from "react"
import { Loader2, LucideIcon } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

type LoaderButtonProps = React.ComponentProps<typeof Button> & {
  icon?: LucideIcon | ElementType
  isLoading?: boolean
}

export const LoaderButton = ({
  icon: Icon,
  isLoading,
  disabled,
  children,
  ...props
}: LoaderButtonProps) => (
  <Button disabled={disabled || isLoading} {...props}>
    {isLoading ? (
      <Loader2 className="size-4 animate-spin" />
    ) : Icon ? (
      <Icon className="size-4" />
    ) : null}
    {children}
  </Button>
)
