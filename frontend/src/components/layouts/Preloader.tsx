"use client"

import React, { useEffect, useState } from "react"
import Image from "next/image"

import { cn } from "@/lib/utils"

const Preloader = () => {
  const [isAnimated, setIsAnimated] = useState<boolean>(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsAnimated(true), 500)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div
      className={cn(
        "bg-background fixed inset-0 z-50 flex items-center justify-center opacity-100 transition-opacity duration-500",
        isAnimated && "pointer-events-none opacity-0"
      )}
    >
      <Image
        className="flex h-12 w-auto object-contain dark:hidden"
        src="/logo-blue.png"
        alt="Singapore Medical Group"
        width={512}
        height={175}
        priority
      />
      <Image
        className="hidden h-12 w-auto object-contain dark:flex"
        src="/logo-white.png"
        alt="Singapore Medical Group"
        width={512}
        height={175}
        priority
      />
    </div>
  )
}

export default Preloader
