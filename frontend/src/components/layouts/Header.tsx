import React from "react"

import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import AppBreadcrumb from "@/components/layouts/AppBreadcrumb"
import AvatarDropdown from "@/components/layouts/AvatarDropdown"
import ModeToggle from "@/components/layouts/ModeToggle"

const Header = () => (
  <header className="bg-sidebar sticky top-0 z-10 flex shrink-0 items-center gap-2 border-b p-4">
    <SidebarTrigger className="-ml-1" />
    <Separator orientation="vertical" className="mr-2 h-4" />
    <AppBreadcrumb />
    <div className="flex-1" />
    <ModeToggle />
    <AvatarDropdown />
  </header>
)

export default Header
