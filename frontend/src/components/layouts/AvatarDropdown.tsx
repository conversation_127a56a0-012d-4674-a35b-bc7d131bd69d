"use client"

import React, { useState } from "react"
import { Loader2, LogOut } from "lucide-react"
import { toast } from "sonner"

import { useRouter } from "@/hooks/use-router"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const AvatarDropdown = () => {
  const { push } = useRouter()

  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleSignOut = async () => {
    try {
      setIsLoading(true)
      await new Promise((resolve) => setTimeout(resolve, 1000))
      push("/sign-in")
    } catch (error) {
      toast.error(String(error))
      setIsLoading(false)
    }
  }

  const user = {
    name: "User",
    email: "<EMAIL>",
    // image: "https://github.com/shadcn.png",
    image: "",
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="rounded-lg">
        <Avatar className="size-9 rounded-lg">
          <AvatarImage src={user.image} alt={user.name} />
          <AvatarFallback className="rounded-lg">
            {(user.name || user.email || "U")[0]}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="w-[--radix-dropdown-menu-trigger-width] min-w-52 rounded-lg"
        side="bottom"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex items-center gap-2">
            <Avatar className="size-8 rounded-lg">
              <AvatarImage src={user.image} alt={user.name} />
              <AvatarFallback className="rounded-lg">
                {(user.name || user.email || "U")[0]}
              </AvatarFallback>
            </Avatar>

            <div className="flex flex-1 flex-col truncate text-sm leading-tight">
              <span className="truncate font-semibold">{user.name}</span>
              <span className="truncate text-xs">{user.email}</span>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuGroup>
          <DropdownMenuItem
            onSelect={(e) => e.preventDefault()}
            onClick={handleSignOut}
            disabled={isLoading}
          >
            {isLoading ? <Loader2 className="animate-spin" /> : <LogOut />}
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default AvatarDropdown
