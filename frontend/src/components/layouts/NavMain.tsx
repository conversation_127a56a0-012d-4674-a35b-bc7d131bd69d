"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight } from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"

type Page = {
  title: string
  url: string
  subPages?: {
    title: string
    url: string
  }[]
}

const PAGES: Page[] = [
  {
    title: "Summary",
    url: "/",
  },
  {
    title: "Financial",
    url: "/financial",
  },
  {
    title: "Business",
    url: "/business",
  },
  {
    title: "Operations",
    url: "/operations",
  },
  {
    title: "Targets",
    url: "/targets",
  },
  {
    title: "Notes",
    url: "/notes",
  },
]

const NavMain = () => {
  const pathname = usePathname()
  const { setOpenMobile } = useSidebar()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Application</SidebarGroupLabel>

      <SidebarGroupContent>
        <SidebarMenu>
          {PAGES.map((page) => (
            <Collapsible
              key={page.title}
              defaultOpen={pathname === page.url}
              onClick={() => setOpenMobile(false)}
              asChild
            >
              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={pathname === page.url}
                  onClick={() => setOpenMobile(false)}
                  asChild
                >
                  <Link href={page.url}>{page.title}</Link>
                </SidebarMenuButton>

                {page.subPages && page.subPages.length !== 0 && (
                  <>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuAction className="data-[state=open]:rotate-90">
                        <ChevronRight />
                      </SidebarMenuAction>
                    </CollapsibleTrigger>

                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {page.subPages.map((subPage) => (
                          <SidebarMenuSubItem key={subPage.title}>
                            <SidebarMenuSubButton
                              isActive={
                                pathname === `${page.url}${subPage.url}`
                              }
                              onClick={() => setOpenMobile(false)}
                              asChild
                            >
                              <Link href={`${page.url}${subPage.url}`}>
                                {subPage.title}
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </>
                )}
              </SidebarMenuItem>
            </Collapsible>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

export default NavMain
