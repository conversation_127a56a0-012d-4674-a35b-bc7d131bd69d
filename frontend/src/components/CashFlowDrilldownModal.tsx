import React, { useState } from "react"
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  Legend,
  Line,
  Pie,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define the data point type for the chart
interface CashFlowDataPoint {
  month: string
  inflow: number
  outflow: number
  net: number
}

// Define the view types
type ViewType = "monthly" | "quarterly" | "category" | "trend"

// Define the props for the CashFlowDrilldownModal
interface CashFlowDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData: CashFlowDataPoint[]
  title: string
  description?: string
}

// Sample data for different views
const categoryData = [
  { name: "Revenue", value: 101558 },
  { name: "Expenses", value: 54929 },
  { name: "Investments", value: 12000 },
  { name: "Loans", value: 8500 },
  { name: "Taxes", value: 15000 },
]

const quarterlyData = [
  {
    quarter: "Q1 (Jul-Sep)",
    inflow: 27100,
    outflow: 12561,
    net: 14539,
  },
  {
    quarter: "Q2 (Oct-Dec)",
    inflow: 28000,
    outflow: 19500,
    net: 8500,
  },
  {
    quarter: "Q3 (Jan-Mar)",
    inflow: 26257,
    outflow: 13493,
    net: 12764,
  },
  {
    quarter: "Q4 (Apr-Jun)",
    inflow: 28101,
    outflow: 13375,
    net: 14726,
  },
]

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const CashFlowDrilldownModal: React.FC<CashFlowDrilldownModalProps> = ({
  open,
  onOpenChange,
  initialData,
  title,
  description,
}) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("monthly")

  // Get the appropriate data based on the view type
  const getDataForView = () => {
    switch (viewType) {
      case "quarterly":
        return quarterlyData
      case "category":
        return categoryData
      case "trend":
        // For trend view, we'll use the initial data but calculate the cumulative net
        return initialData.map((item, index) => {
          // Get the previous item's cumulative net value if it exists
          const previousItem =
            index > 0
              ? initialData
                  .slice(0, index)
                  .reduce((acc, curr) => acc + curr.net, 0)
              : 0

          return {
            ...item,
            cumulativeNet: previousItem + item.net,
          }
        })
      default:
        return initialData
    }
  }

  // Get the appropriate chart title based on the view type
  const getChartTitle = () => {
    switch (viewType) {
      case "quarterly":
        return "Quarterly Cash Flow Analysis"
      case "category":
        return "Cash Flow by Category"
      case "trend":
        return "Cumulative Cash Flow Trend"
      default:
        return "Monthly Cash Flow Analysis"
    }
  }

  // Get the appropriate tooltip description based on the view type
  const getTooltipDescription = () => {
    switch (viewType) {
      case "quarterly":
        return "This chart displays quarterly cash flow comparing inflows (blue bars) against outflows (orange bars) and net cash flow (green line)."
      case "category":
        return "This chart breaks down cash flow by category, showing the proportion of different sources and uses of cash."
      case "trend":
        return "This chart shows the cumulative cash flow trend over time, helping to identify long-term cash position changes."
      default:
        return "This chart displays monthly cash flow comparing inflows (blue bars) against outflows (orange bars) and net cash flow (green line)."
    }
  }

  // Format currency values
  const formatCurrency = (value: number) => {
    return `$${value.toLocaleString()}`
  }

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    const data = getDataForView()

    if (viewType === "category") {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <PieChart>
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={150}
              fill="#8884d8"
              label={({ name, percent }) =>
                `${name} (${(percent * 100).toFixed(1)}%)`
              }
            >
              {data.map((_, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip formatter={(value: number) => formatCurrency(value)} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      )
    }

    if (viewType === "quarterly") {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <ComposedChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="quarter" tick={{ fontSize: 12 }} />
            <YAxis tickFormatter={(v) => `$${(v / 1000).toFixed(0)}K`} />
            <Tooltip formatter={(value: number) => formatCurrency(value)} />
            <Legend verticalAlign="bottom" />
            <Bar
              dataKey="inflow"
              name="Cash Inflow"
              fill="var(--chart-1)"
              barSize={32}
            />
            <Bar
              dataKey="outflow"
              name="Cash Outflow"
              fill="var(--chart-3)"
              barSize={32}
            />
            <Line
              type="monotone"
              dataKey="net"
              name="Net Cash Flow"
              stroke="var(--chart-5)"
              strokeWidth={3}
              dot={{ stroke: "var(--chart-5)" }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      )
    }

    if (viewType === "trend") {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <ComposedChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" tick={{ fontSize: 12 }} />
            <YAxis tickFormatter={(v) => `$${(v / 1000).toFixed(0)}K`} />
            <Tooltip formatter={(value: number) => formatCurrency(value)} />
            <Legend verticalAlign="bottom" />
            <Bar
              dataKey="net"
              name="Monthly Net Cash Flow"
              fill="var(--chart-1)"
              barSize={32}
            />
            <Line
              type="monotone"
              dataKey="cumulativeNet"
              name="Cumulative Cash Flow"
              stroke="var(--chart-5)"
              strokeWidth={3}
              dot={{ stroke: "var(--chart-5)" }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      )
    }

    // Default monthly view
    return (
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis tickFormatter={(v) => `$${(v / 1000).toFixed(0)}K`} />
          <Tooltip formatter={(value: number) => formatCurrency(value)} />
          <Legend verticalAlign="bottom" />
          <Bar
            dataKey="inflow"
            name="Cash Inflow"
            fill="var(--chart-1)"
            barSize={32}
          />
          <Bar
            dataKey="outflow"
            name="Cash Outflow"
            fill="var(--chart-3)"
            barSize={32}
          />
          <Line
            type="monotone"
            dataKey="net"
            name="Net Cash Flow"
            stroke="var(--chart-5)"
            strokeWidth={3}
            dot={{ stroke: "var(--chart-5)" }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View By:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="category">Category</SelectItem>
                <SelectItem value="trend">Trend Analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {getTooltipDescription()}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default CashFlowDrilldownModal
