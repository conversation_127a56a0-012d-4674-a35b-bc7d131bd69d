import {
  CartesianGrid,
  Legend,
  Line,
  Line<PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  Toolt<PERSON>,
  <PERSON>A<PERSON>s,
  YAxis,
} from "recharts"

const data = [
  { month: "Jan", primary: 25000, specialty: 35000, emergency: 15000 },
  { month: "Feb", primary: 26000, specialty: 36000, emergency: 16000 },
  { month: "Mar", primary: 27000, specialty: 37000, emergency: 17000 },
  { month: "Apr", primary: 28000, specialty: 38000, emergency: 18000 },
  { month: "May", primary: 29000, specialty: 39000, emergency: 19000 },
  { month: "Jun", primary: 30000, specialty: 40000, emergency: 20000 },
  { month: "Jul", primary: 31000, specialty: 41000, emergency: 21000 },
  { month: "Aug", primary: 32000, specialty: 42000, emergency: 22000 },
  { month: "Sep", primary: 33000, specialty: 43000, emergency: 23000 },
  { month: "Oct", primary: 34000, specialty: 44000, emergency: 24000 },
  { month: "Nov", primary: 35000, specialty: 45000, emergency: 25000 },
  { month: "Dec", primary: 36000, specialty: 46000, emergency: 26000 },
]

export function RevenueLineChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" tickLine={false} axisLine={false} />
          <YAxis
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `$${value / 1000}k`}
          />
          <Tooltip
            formatter={(value: number) => `$${value.toLocaleString()}`}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="primary"
            name="Primary Care"
            stroke="var(--chart-1)"
            strokeWidth={2}
            dot={{ r: 3 }}
          />
          <Line
            type="monotone"
            dataKey="specialty"
            name="Specialty"
            stroke="var(--chart-2)"
            strokeWidth={2}
            dot={{ r: 3 }}
          />
          <Line
            type="monotone"
            dataKey="emergency"
            name="Emergency"
            stroke="var(--chart-3)"
            strokeWidth={2}
            dot={{ r: 3 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
