import React, { useState } from "react"
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  <PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Define the treemap data structure
interface TreemapChild {
  name: string
  size: number
}

interface TreemapItem {
  name: string
  size: number
  children?: TreemapChild[]
}

// Define the view types
type ViewType = "pie" | "bar"

// Define the props for the TreemapDrilldownModal
interface TreemapDrilldownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedItem: TreemapItem
  title: string
  description?: string
}

const COLORS = [
  "var(--chart-1)",
  "var(--chart-2)",
  "var(--chart-3)",
  "var(--chart-4)",
  "var(--chart-5)",
]

const TreemapDrilldownModal: React.FC<TreemapDrilldownModalProps> = ({
  open,
  onOpenChange,
  selectedItem,
  title,
  description,
}) => {
  // State for the current view type
  const [viewType, setViewType] = useState<ViewType>("pie")

  // Prepare data for visualization
  const getChartData = () => {
    // If the item has children, use them
    if (selectedItem.children && selectedItem.children.length > 0) {
      return selectedItem.children
    }

    // If no children, create a single item
    return [{ name: selectedItem.name, size: selectedItem.size }]
  }

  const chartData = getChartData()

  // Get the appropriate chart title based on the selected item
  const getChartTitle = () => {
    return `${selectedItem.name} - Operating Income Breakdown`
  }

  // Format currency values
  const formatCurrency = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`
    }
    return `$${value.toFixed(1)}`
  }

  // Calculate total size for percentage calculation
  const totalSize = chartData.reduce((sum, item) => sum + item.size, 0)

  // Render the appropriate chart based on the view type
  const renderChart = () => {
    if (viewType === "pie") {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <PieChart>
            <Pie
              data={chartData}
              dataKey="size"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={150}
              fill="#8884d8"
              label={({ name, percent }) =>
                `${name} (${(percent * 100).toFixed(1)}%)`
              }
            >
              {chartData.map((_, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [
                formatCurrency(value),
                "Operating Income",
              ]}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      )
    }

    return (
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis tickFormatter={(value) => formatCurrency(value)} />
          <Tooltip
            formatter={(value: number) => [
              formatCurrency(value),
              "Operating Income",
            ]}
          />
          <Legend />
          <Bar dataKey="size" name="Operating Income">
            {chartData.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl!">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>

        <div className="mb-2">
          <div className="flex items-center gap-4">
            <Label htmlFor="view-type">View As:</Label>
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as ViewType)}
            >
              <SelectTrigger id="view-type" className="bg-card w-[180px]">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pie">Pie Chart</SelectItem>
                <SelectItem value="bar">Bar Chart</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card>
          <CardContent>
            <h3 className="mb-4 text-center text-base font-semibold text-gray-900 dark:text-white">
              {getChartTitle()}
            </h3>
            <div className="dark:bg-primary/20 dark:text-primary mb-4 rounded bg-blue-50 p-4 text-sm text-blue-900">
              {selectedItem.children && selectedItem.children.length > 0
                ? `This visualization breaks down the operating income for ${selectedItem.name}, showing the distribution across different segments. Total operating income: ${formatCurrency(totalSize)}.`
                : `Operating income for ${selectedItem.name}: ${formatCurrency(totalSize)}.`}
            </div>
            {renderChart()}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  )
}

export default TreemapDrilldownModal
