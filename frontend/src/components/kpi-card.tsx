import { <PERSON>D<PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"

import { Card, CardContent, CardTitle } from "@/components/ui/card"

interface KpiCardProps {
  title: string
  value: string
  description: string
  trend: string
  comparison: string
  trendDirection: "up" | "down"
}

export function KpiCard({
  title,
  value,
  description,
  trend,
  comparison,
  trendDirection,
}: KpiCardProps) {
  return (
    <Card>
      <CardContent>
        <CardTitle className="text-muted-foreground text-sm font-medium">
          {title}
        </CardTitle>

        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold">{value}</span>
            <span
              className={`flex items-center text-xs ${trendDirection === "up" ? "text-green-500" : "text-red-500"}`}
            >
              {trendDirection === "up" ? (
                <ArrowUp className="h-3 w-3" />
              ) : (
                <ArrowDown className="h-3 w-3" />
              )}
              {trend}
            </span>
          </div>
          <p className="text-muted-foreground text-xs">{description}</p>
          <p className="text-muted-foreground text-xs">{comparison}</p>
        </div>
      </CardContent>
    </Card>
  )
}
