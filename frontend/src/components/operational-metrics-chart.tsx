import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON><PERSON>,
} from "recharts"

const data = [
  {
    month: "Jan",
    "Cost per Visit": 150,
    "Revenue per Visit": 230,
    "Operating Margin (%)": 34.8,
  },
  {
    month: "Feb",
    "Cost per Visit": 145,
    "Revenue per Visit": 225,
    "Operating Margin (%)": 35.6,
  },
  {
    month: "Mar",
    "Cost per Visit": 148,
    "Revenue per Visit": 228,
    "Operating Margin (%)": 35.1,
  },
  {
    month: "Apr",
    "Cost per Visit": 142,
    "Revenue per Visit": 235,
    "Operating Margin (%)": 36.3,
  },
  {
    month: "May",
    "Cost per Visit": 140,
    "Revenue per Visit": 240,
    "Operating Margin (%)": 37.5,
  },
  {
    month: "Jun",
    "Cost per Visit": 138,
    "Revenue per Visit": 243,
    "Operating Margin (%)": 38.1,
  },
]

export function OperationalMetricsChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis
            yAxisId="left"
            orientation="left"
            tickFormatter={(v) => `$${v}`}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[0, 100]}
            tickFormatter={(v) => `${v}%`}
          />
          <Tooltip
            formatter={(value: number, name: string) => {
              if (name.includes("Margin")) return [`${value}%`, name]
              if (name.includes("Visit")) return [`$${value}`, name]
              return [value, name]
            }}
          />
          <Legend />
          <Bar
            yAxisId="left"
            dataKey="Cost per Visit"
            fill="var(--chart-1)"
            name="Cost per Visit"
          />
          <Bar
            yAxisId="left"
            dataKey="Revenue per Visit"
            fill="var(--chart-3)"
            name="Revenue per Visit"
          />
          <Bar
            yAxisId="right"
            dataKey="Operating Margin (%)"
            fill="var(--chart-5)"
            name="Operating Margin (%)"
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
