import {
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
} from "recharts"

const data = [
  {
    metric: "Cost per Visit",
    "Our Cost": 50,
    "Industry Avg": 70,
  },
  {
    metric: "Admin Overhead (%)",
    "Our Cost": 12,
    "Industry Avg": 30,
  },
  {
    metric: "Supply Costs per Patient",
    "Our Cost": 45,
    "Industry Avg": 60,
  },
  {
    metric: "Staffing Cost per Visit",
    "Our Cost": 60,
    "Industry Avg": 85,
  },
  {
    metric: "Tech & Equipment (%)",
    "Our Cost": 20,
    "Industry Avg": 25,
  },
  {
    metric: "Facility Costs per sq.ft",
    "Our Cost": 18,
    "Industry Avg": 22,
  },
]

export function MarketPositionChart() {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <RadarChart data={data}>
          <PolarGrid />
          <PolarAngleAxis dataKey="metric" />
          <PolarRadiusAxis angle={30} />
          <Tooltip
            formatter={(value: number) =>
              typeof value === "number" && value < 100
                ? `$${value}`
                : `${value}%`
            }
          />
          <Legend />
          <Radar
            name="Our Cost"
            dataKey="Our Cost"
            stroke="var(--chart-1)"
            fill="var(--chart-1)"
            fillOpacity={0.5}
          />
          <Radar
            name="Industry Avg"
            dataKey="Industry Avg"
            stroke="var(--chart-3)"
            fill="var(--chart-3)"
            fillOpacity={0.5}
          />
        </RadarChart>
      </ResponsiveContainer>
    </div>
  )
}
